/**
 * 采购管理API - 包含采购订单、采购入库单、采购退货单
 */

import api from './index'

// ==================== 采购订单相关类型定义 ====================

// 采购订单状态枚举
export enum PurchaseOrderStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PURCHASING = 'purchasing',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// 采购订单明细
export interface PurchaseOrderItem {
  id?: number
  order_id?: number
  product_id: number
  line_number: number
  product_name: string
  product_sku?: string
  quantity: number
  unit_price: number
  total_price: number
  received_quantity?: number
  created_at?: string
}

// 采购订单
export interface PurchaseOrder {
  id: number
  order_no: string
  supplier_id: number
  supplier_name?: string
  total_amount: number
  status: PurchaseOrderStatus
  expected_date?: string
  created_by: string
  submitted_by?: string
  submitted_at?: string
  approved_by?: string
  approved_at?: string
  created_at: string
  updated_at?: string
  remark?: string
  items: PurchaseOrderItem[]
}

// 创建采购订单
export interface PurchaseOrderCreate {
  supplier_id: number
  total_amount: number
  status?: PurchaseOrderStatus
  expected_date?: string
  created_by: string
  remark?: string
  items: Omit<PurchaseOrderItem, 'id' | 'order_id' | 'received_quantity' | 'created_at'>[]
}

// 更新采购订单
export interface PurchaseOrderUpdate {
  supplier_id?: number
  total_amount?: number
  status?: PurchaseOrderStatus
  expected_date?: string
  approved_by?: string
  approved_at?: string
  remark?: string
  items?: Omit<PurchaseOrderItem, 'id' | 'order_id' | 'received_quantity' | 'created_at'>[]
}

// 采购订单列表响应
export interface PurchaseOrderListResponse {
  items: PurchaseOrder[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 采购订单统计
export interface PurchaseOrderStats {
  total_orders: number
  pending_orders: number
  processing_orders: number
  completed_orders: number
  total_amount: number
  this_month_orders: number
}

// ==================== 采购入库单相关类型定义 ====================

// 采购入库单状态枚举
export enum PurchaseReceiptStatus {
  DRAFT = 'draft',         // 草稿
  SUBMITTED = 'submitted', // 已提交
  APPROVED = 'approved',   // 已审核
  REJECTED = 'rejected',   // 已拒绝
  CANCELLED = 'cancelled'  // 已取消
}

// 采购入库单明细
export interface PurchaseReceiptItem {
  id?: number
  receipt_id?: number
  product_id: number
  product_name: string
  product_sku?: string
  quantity: number
  batch_no?: string
  created_at?: string
}

// 采购入库单明细创建
export interface PurchaseReceiptItemCreate {
  product_id: number
  product_name: string
  product_sku?: string
  quantity: number
  batch_no?: string
}

// 采购入库单
export interface PurchaseReceipt {
  id: number
  receipt_no: string
  purchase_order_id?: number
  purchase_order_no?: string
  supplier_id: number
  warehouse_id: number
  status: PurchaseReceiptStatus
  receipt_date?: string
  created_by: string
  submitted_by?: string
  submitted_at?: string
  approved_by?: string
  approved_at?: string
  remark?: string
  created_at: string
  updated_at?: string
  items: PurchaseReceiptItem[]

  // 关联对象名称
  supplier_name?: string
  warehouse_name?: string
}

// 创建采购入库单
export interface PurchaseReceiptCreate {
  purchase_order_id?: number
  purchase_order_no?: string
  supplier_id: number
  warehouse_id: number
  status?: PurchaseReceiptStatus
  receipt_date?: string
  created_by: string
  submitted_by?: string
  submitted_at?: string
  approved_by?: string
  approved_at?: string
  remark?: string
  items: PurchaseReceiptItemCreate[]
}

// 更新采购入库单
export interface PurchaseReceiptUpdate {
  supplier_id?: number
  warehouse_id?: number
  status?: PurchaseReceiptStatus
  receipt_date?: string
  submitted_by?: string
  submitted_at?: string
  approved_by?: string
  approved_at?: string
  remark?: string
  items?: PurchaseReceiptItemCreate[]
}

// 采购入库单列表响应
export interface PurchaseReceiptListResponse {
  items: PurchaseReceipt[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 采购入库单统计
export interface PurchaseReceiptStats {
  total_receipts: number
  pending_receipts: number
  partial_receipts: number
  completed_receipts: number
  this_month_receipts: number
}

// 仓库信息
export interface Warehouse {
  id: number
  name: string
  address: string
  capacity?: number
  manager?: string
  phone?: string
  status?: string
  created_at?: string
  updated_at?: string
}

// ==================== 采购退货单相关类型定义 ====================

// 采购退货单状态枚举
export enum PurchaseReturnStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  RETURNED = 'returned',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// 采购退货单明细接口
export interface PurchaseReturnItem {
  id?: number
  purchase_return_id?: number
  product_id: number
  product_name: string
  product_sku?: string
  return_quantity: number
  unit_price: number
  total_price: number
  quality_issue?: string
  created_at?: string
  updated_at?: string
}

// 采购退货单接口
export interface PurchaseReturn {
  id?: number
  return_no: string
  purchase_receipt_id?: number
  supplier_id: number
  return_date: string
  reason: string
  total_amount: number
  status: PurchaseReturnStatus
  remark?: string

  // 审核信息
  submitted_at?: string
  submitted_by?: string
  approved_at?: string
  approved_by?: string
  approval_note?: string

  // 退货信息
  returned_at?: string
  returned_by?: string

  // 时间戳
  created_at?: string
  updated_at?: string
  created_by?: string
  updated_by?: string

  // 关联信息
  supplier_name?: string
  purchase_receipt_no?: string

  // 明细
  items: PurchaseReturnItem[]
}

// 创建采购退货单数据
export interface PurchaseReturnCreate {
  return_no?: string
  purchase_receipt_id?: number
  supplier_id: number
  return_date: string
  reason: string
  total_amount: number
  remark?: string
  items: Omit<PurchaseReturnItem, 'id' | 'purchase_return_id' | 'created_at' | 'updated_at'>[]
}

// 更新采购退货单数据
export interface PurchaseReturnUpdate {
  return_no?: string
  purchase_receipt_id?: number
  supplier_id?: number
  return_date?: string
  reason?: string
  total_amount?: number
  remark?: string
  items?: Omit<PurchaseReturnItem, 'id' | 'purchase_return_id' | 'created_at' | 'updated_at'>[]
}

// 状态更新数据
export interface PurchaseReturnStatusUpdate {
  status: PurchaseReturnStatus
  note?: string
}

// 采购退货单统计数据接口
export interface PurchaseReturnStats {
  total_returns: number
  draft_returns: number
  submitted_returns: number
  approved_returns: number
  returned_returns: number
  completed_returns: number
  total_amount: number
}

// 采购退货单查询参数接口
export interface PurchaseReturnQuery {
  return_no?: string
  supplier_id?: number
  status?: PurchaseReturnStatus
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}

// ==================== API接口 ====================

// 采购订单API
export const purchaseApi = {
  // 获取采购订单列表
  async getPurchaseOrders(params?: {
    page?: number
    page_size?: number
    order_no?: string
    supplier_id?: number
    status?: PurchaseOrderStatus
    created_by?: string
    start_date?: string
    end_date?: string
  }): Promise<PurchaseOrderListResponse> {
    return await api.get('/api/purchase/', { params })
  },

  // 获取采购订单统计信息
  async getPurchaseOrderStats(): Promise<PurchaseOrderStats> {
    return await api.get('/api/purchase/stats')
  },

  // 获取单个采购订单
  async getPurchaseOrder(id: number): Promise<PurchaseOrder> {
    return await api.get(`/api/purchase/${id}`)
  },

  // 创建采购订单
  async createPurchaseOrder(data: PurchaseOrderCreate): Promise<PurchaseOrder> {
    return await api.post('/api/purchase/', data)
  },

  // 更新采购订单
  async updatePurchaseOrder(id: number, data: PurchaseOrderUpdate): Promise<PurchaseOrder> {
    return await api.put(`/api/purchase/${id}`, data)
  },

  // 删除采购订单
  async deletePurchaseOrder(id: number): Promise<{ message: string }> {
    return await api.delete(`/api/purchase/${id}`)
  },

  // 提交采购订单
  async submitPurchaseOrder(id: number, submitted_by: string): Promise<{ message: string }> {
    return await api.put(`/api/purchase/${id}/submit`, null, {
      params: { submitted_by }
    })
  },

  // 审核采购订单
  async approvePurchaseOrder(id: number, approved_by: string, approved: boolean = true, remark?: string): Promise<{ message: string }> {
    return await api.put(`/api/purchase/${id}/approve`, null, {
      params: { approved_by, approved, remark }
    })
  },

  // 撤回采购订单
  async recallPurchaseOrder(id: number): Promise<{ message: string }> {
    return await api.put(`/api/purchase/${id}/recall`)
  },

  // 更新订单状态（保留兼容性）
  async updateOrderStatus(id: number, status: PurchaseOrderStatus, approved_by?: string): Promise<{ message: string }> {
    return await api.put(`/api/purchase/${id}/status`, null, {
      params: { status, approved_by }
    })
  }
}

// 采购入库单API
export const purchaseReceiptApi = {
  // 获取采购入库单列表
  async getPurchaseReceipts(params?: {
    page?: number
    page_size?: number
    receipt_no?: string
    purchase_order_no?: string
    supplier_id?: number
    warehouse_id?: number
    status?: PurchaseReceiptStatus
    created_by?: string
    start_date?: string
    end_date?: string
  }): Promise<PurchaseReceiptListResponse> {
    return await api.get('/api/purchase/receipts/', { params })
  },

  // 获取采购入库单统计信息
  async getPurchaseReceiptStats(): Promise<PurchaseReceiptStats> {
    return await api.get('/api/purchase/receipts/stats')
  },

  // 获取单个采购入库单
  async getPurchaseReceipt(id: number): Promise<PurchaseReceipt> {
    return await api.get(`/api/purchase/receipts/${id}`)
  },

  // 创建采购入库单
  async createPurchaseReceipt(data: PurchaseReceiptCreate): Promise<PurchaseReceipt> {
    return await api.post('/api/purchase/receipts/', data)
  },

  // 更新采购入库单
  async updatePurchaseReceipt(id: number, data: PurchaseReceiptUpdate): Promise<PurchaseReceipt> {
    return await api.put(`/api/purchase/receipts/${id}`, data)
  },

  // 删除采购入库单
  async deletePurchaseReceipt(id: number): Promise<{ message: string }> {
    return await api.delete(`/api/purchase/receipts/${id}`)
  },

  // 从采购订单创建入库单
  async createReceiptFromOrder(orderId: number, warehouseId: number, createdBy: string): Promise<PurchaseReceipt> {
    return await api.post(`/api/purchase/receipts/from-order/${orderId}`, null, {
      params: { warehouse_id: warehouseId, created_by: createdBy }
    })
  },

  // 提交采购入库单
  async submitPurchaseReceipt(id: number, submittedBy: string): Promise<{ message: string }> {
    return await api.put(`/api/purchase/receipts/${id}/submit?submitted_by=${encodeURIComponent(submittedBy)}`)
  },

  // 审核采购入库单
  async approvePurchaseReceipt(id: number, approvedBy: string, approved: boolean = true, remark?: string): Promise<{ message: string }> {
    const params = new URLSearchParams({
      approved_by: approvedBy,
      approved: approved.toString()
    })
    if (remark) {
      params.append('remark', remark)
    }
    return await api.put(`/api/purchase/receipts/${id}/approve?${params.toString()}`)
  },

  // 撤回采购入库单
  async recallPurchaseReceipt(id: number): Promise<{ message: string }> {
    return await api.put(`/api/purchase/receipts/${id}/recall`)
  },

  // 取消采购入库单
  async cancelPurchaseReceipt(id: number): Promise<{ message: string }> {
    return await api.put(`/api/purchase/receipts/${id}/cancel`)
  }
}

// 采购退货单API
export const purchaseReturnApi = {
  // 获取统计信息
  async getStats(): Promise<PurchaseReturnStats> {
    return await api.get('/api/purchase/returns/stats')
  },

  // 获取采购退货单列表
  async getPurchaseReturns(params?: PurchaseReturnQuery): Promise<PurchaseReturn[]> {
    return await api.get('/api/purchase/returns/', { params })
  },

  // 获取采购退货单详情
  async getPurchaseReturn(id: number): Promise<PurchaseReturn> {
    return await api.get(`/api/purchase/returns/${id}`)
  },

  // 创建采购退货单
  async createPurchaseReturn(data: PurchaseReturnCreate): Promise<PurchaseReturn> {
    return await api.post('/api/purchase/returns/', data)
  },

  // 更新采购退货单
  async updatePurchaseReturn(id: number, data: PurchaseReturnUpdate): Promise<PurchaseReturn> {
    return await api.put(`/api/purchase/returns/${id}`, data)
  },

  // 删除采购退货单
  async deletePurchaseReturn(id: number): Promise<{ message: string }> {
    return await api.delete(`/api/purchase/returns/${id}`)
  },

  // 提交采购退货单
  async submitPurchaseReturn(id: number): Promise<{ message: string }> {
    return await api.post(`/api/purchase/returns/${id}/submit`)
  },

  // 审核采购退货单
  async approvePurchaseReturn(id: number, data: PurchaseReturnStatusUpdate): Promise<{ message: string }> {
    return await api.post(`/api/purchase/returns/${id}/approve`, data)
  },

  // 确认退货
  async returnPurchaseReturn(id: number): Promise<{ message: string }> {
    return await api.post(`/api/purchase/returns/${id}/return`)
  },

  // 完成采购退货单
  async completePurchaseReturn(id: number): Promise<{ message: string }> {
    return await api.post(`/api/purchase/returns/${id}/complete`)
  }
}

// 仓库API
export const warehouseApi = {
  // 获取仓库列表
  async getWarehouses(): Promise<Warehouse[]> {
    return await api.get('/api/logistics/warehouses')
  },

  // 获取单个仓库
  async getWarehouse(id: number): Promise<Warehouse> {
    return await api.get(`/api/logistics/warehouses/${id}`)
  }
}

// 默认导出采购订单API（保持向后兼容）
export default purchaseApi
