#!/usr/bin/env python3
"""
添加批次管理测试数据
"""

import sys
import os
from datetime import date, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from backend.config import config
import pymysql

def add_batch_test_data():
    """添加批次管理测试数据"""
    try:
        # 创建数据库连接
        connection = pymysql.connect(
            host=config.DB_HOST,
            port=config.DB_PORT,
            user=config.DB_USER,
            password=config.DB_PASSWORD,
            database=config.DB_NAME,
            charset=config.DB_CHARSET
        )
        
        with connection.cursor() as cursor:
            print("开始添加批次管理测试数据...")
            
            # 获取现有的库存记录
            cursor.execute("SELECT id, product_id, warehouse_id FROM inventory LIMIT 10")
            inventories = cursor.fetchall()
            
            if not inventories:
                print("没有找到库存记录，请先添加一些库存数据")
                return
            
            # 为前几个库存记录添加批次信息
            today = date.today()
            
            test_batches = [
                {
                    'batch_no': 'BATCH001',
                    'production_date': today - timedelta(days=30),
                    'expiry_date': today + timedelta(days=90)
                },
                {
                    'batch_no': 'BATCH002', 
                    'production_date': today - timedelta(days=15),
                    'expiry_date': today + timedelta(days=5)  # 临近过期
                },
                {
                    'batch_no': 'BATCH003',
                    'production_date': today - timedelta(days=60),
                    'expiry_date': today - timedelta(days=5)  # 已过期
                },
                {
                    'batch_no': 'BATCH004',
                    'production_date': today - timedelta(days=10),
                    'expiry_date': today + timedelta(days=180)
                },
                {
                    'batch_no': 'BATCH005',
                    'production_date': today - timedelta(days=45),
                    'expiry_date': today + timedelta(days=60)
                }
            ]
            
            # 更新库存记录，添加批次信息
            for i, inventory in enumerate(inventories[:5]):
                if i < len(test_batches):
                    batch = test_batches[i]
                    
                    update_sql = """
                        UPDATE inventory 
                        SET batch_no = %s, 
                            production_date = %s, 
                            expiry_date = %s
                        WHERE id = %s
                    """
                    
                    cursor.execute(update_sql, (
                        batch['batch_no'],
                        batch['production_date'],
                        batch['expiry_date'],
                        inventory[0]
                    ))
                    
                    print(f"✓ 更新库存记录 {inventory[0]}，添加批次 {batch['batch_no']}")
            
            # 提交更改
            connection.commit()
            print("✓ 批次管理测试数据添加完成")
            
    except Exception as e:
        print(f"添加测试数据时出错: {e}")
        if 'connection' in locals():
            connection.rollback()
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    add_batch_test_data()
