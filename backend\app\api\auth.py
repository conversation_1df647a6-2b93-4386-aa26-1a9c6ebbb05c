from fastapi import APIRouter, HTTPException, Depends
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from datetime import datetime, timedelta
import jwt
import hashlib
from typing import Optional
from sqlalchemy.orm import Session

from app.schemas.auth import LoginRequest, LoginResponse
from app.models.user import User
from app.core.database import get_db

router = APIRouter()
security = HTTPBearer()

# JWT配置
SECRET_KEY = "your-secret-key-here"  # 在生产环境中应该使用环境变量
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建JWT访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt



def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    # 使用MD5验证密码（与数据库中的存储方式一致）
    return hashlib.md5(plain_password.encode()).hexdigest() == hashed_password


def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security), db: Session = Depends(get_db)):
    """获取当前用户 - 从数据库查询"""
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(status_code=401, detail="无效的认证凭据")
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="无效的认证凭据")

    # 从数据库查询用户
    user = db.query(User).filter(User.username == username).first()
    if not user:
        raise HTTPException(status_code=401, detail="用户不存在")

    if not user.is_active:
        raise HTTPException(status_code=401, detail="用户已被禁用")

    return {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "role": user.role,
        "permissions": user.permissions or [],
        "avatar": user.avatar,
        "is_active": user.is_active,
        "created_at": user.created_at.isoformat() if user.created_at else None
    }

@router.post("/login", response_model=LoginResponse)
async def login(login_data: LoginRequest, db: Session = Depends(get_db)):
    """用户登录 - 从数据库验证"""
    try:
        # 从数据库查询用户（支持用户名或邮箱登录）
        user = db.query(User).filter(
            (User.username == login_data.username) | (User.email == login_data.username)
        ).first()

        if not user:
            raise HTTPException(status_code=401, detail="用户名或密码错误")

        # 检查用户是否激活
        if not user.is_active:
            raise HTTPException(status_code=401, detail="账户已被禁用")

        # 验证密码
        if not verify_password(login_data.password, user.hashed_password):
            raise HTTPException(status_code=401, detail="用户名或密码错误")

        # 创建访问令牌
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.username}, expires_delta=access_token_expires
        )

        # 构建响应用户数据
        response_user = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "role": user.role,
            "permissions": user.permissions or [],
            "avatar": user.avatar,
            "is_active": user.is_active,
            "created_at": user.created_at.isoformat() if user.created_at else None
        }

        return LoginResponse(
            success=True,
            token=access_token,
            user=response_user,
            message="登录成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"登录失败: {str(e)}")

@router.post("/logout")
async def logout(current_user: dict = Depends(get_current_user)):
    """用户登出"""
    return {"success": True, "message": "登出成功"}

@router.get("/me")
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    """获取当前用户信息"""
    return {
        "success": True,
        "user": {
            "id": current_user["id"],
            "username": current_user["username"],
            "email": current_user["email"],
            "role": current_user["role"],
            "permissions": current_user["permissions"],
            "avatar": current_user["avatar"],
            "is_active": current_user["is_active"],
            "created_at": current_user["created_at"]
        }
    }

@router.post("/refresh")
async def refresh_token(current_user: dict = Depends(get_current_user)):
    """刷新访问令牌"""
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": current_user["username"]}, expires_delta=access_token_expires
    )

    return {
        "success": True,
        "token": access_token,
        "message": "令牌刷新成功"
    }

@router.post("/change-password")
async def change_password(
    old_password: str,
    new_password: str,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """修改密码 - 从数据库更新"""
    try:
        # 从数据库获取用户
        user = db.query(User).filter(User.id == current_user["id"]).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 验证旧密码
        if not verify_password(old_password, user.hashed_password):
            raise HTTPException(status_code=400, detail="原密码错误")

        # 更新密码
        user.hashed_password = hashlib.md5(new_password.encode()).hexdigest()
        db.commit()

        return {"success": True, "message": "密码修改成功"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"密码修改失败: {str(e)}")

@router.post("/reset-password")
async def reset_password(email: str):
    """重置密码 - 模拟功能"""
    # 模拟发送重置邮件
    return {"success": True, "message": "重置链接已发送到您的邮箱"}
