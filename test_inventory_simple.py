#!/usr/bin/env python3
"""
简单测试库存API
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.core.database import SessionLocal
from backend.app.services.inventory_service import InventoryService
from backend.app.schemas.inventory import InventoryQuery

def test_inventory_service():
    """测试库存服务"""
    db = SessionLocal()
    try:
        service = InventoryService(db)
        
        # 测试获取库存列表
        print("测试获取库存列表...")
        query = InventoryQuery()
        inventories, total = service.get_inventory_list(skip=0, limit=10, query=query)
        
        print(f"库存记录数量: {len(inventories)}")
        print(f"总数: {total}")
        
        if inventories:
            print(f"第一条记录:")
            inv = inventories[0]
            print(f"  ID: {inv.id}")
            print(f"  商品ID: {inv.product_id}")
            print(f"  仓库ID: {inv.warehouse_id}")
            print(f"  当前库存: {inv.current_stock}")
            print(f"  商品名称: {inv.product.name if inv.product else 'N/A'}")
            print(f"  仓库名称: {inv.warehouse.name if inv.warehouse else 'N/A'}")
        
        # 测试获取库存统计
        print("\n测试获取库存统计...")
        stats = service.get_inventory_stats()
        print(f"统计数据: {stats}")
        
        # 测试获取仓库列表
        print("\n测试获取仓库列表...")
        warehouses = service.get_warehouses()
        print(f"仓库数量: {len(warehouses)}")
        if warehouses:
            print(f"第一个仓库: {warehouses[0].name}")
            
    except Exception as e:
        print(f'测试服务时出错: {e}')
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == '__main__':
    test_inventory_service()
