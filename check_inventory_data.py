#!/usr/bin/env python3
"""
检查库存数据脚本
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.core.database import SessionLocal
from backend.app.models.inventory import Inventory
from backend.app.models.product import Product
from backend.app.models.warehouse import Warehouse
from sqlalchemy.orm import joinedload

def check_inventory_data():
    """检查库存数据"""
    db = SessionLocal()
    try:
        # 检查库存表数据
        inventory_count = db.query(Inventory).count()
        print(f'库存记录总数: {inventory_count}')
        
        # 检查商品表数据
        product_count = db.query(Product).count()
        print(f'商品记录总数: {product_count}')
        
        # 检查仓库表数据
        warehouse_count = db.query(Warehouse).count()
        print(f'仓库记录总数: {warehouse_count}')
        
        # 查看前几条库存记录
        inventories = db.query(Inventory).limit(5).all()
        print(f'\n前5条库存记录:')
        for inv in inventories:
            print(f'ID: {inv.id}, 商品ID: {inv.product_id}, 仓库ID: {inv.warehouse_id}, 当前库存: {inv.current_stock}')
            
        # 检查关联数据
        inventories_with_relations = db.query(Inventory).options(
            joinedload(Inventory.product),
            joinedload(Inventory.warehouse)
        ).limit(3).all()
        
        print(f'\n带关联数据的库存记录:')
        for inv in inventories_with_relations:
            product_name = inv.product.name if inv.product else 'None'
            warehouse_name = inv.warehouse.name if inv.warehouse else 'None'
            print(f'ID: {inv.id}, 商品: {product_name}, 仓库: {warehouse_name}, 库存: {inv.current_stock}')
            
        # 检查是否有活跃的库存记录
        active_inventory_count = db.query(Inventory).filter(Inventory.is_active == True).count()
        print(f'\n活跃库存记录数: {active_inventory_count}')
        
        # 检查库存记录的字段
        if inventories:
            inv = inventories[0]
            print(f'\n第一条库存记录的详细信息:')
            print(f'  ID: {inv.id}')
            print(f'  商品ID: {inv.product_id}')
            print(f'  仓库ID: {inv.warehouse_id}')
            print(f'  当前库存: {inv.current_stock}')
            print(f'  预留库存: {inv.reserved_stock}')
            print(f'  最小库存: {inv.min_stock}')
            print(f'  最大库存: {inv.max_stock}')
            print(f'  平均成本: {inv.average_cost}')
            print(f'  状态: {inv.status}')
            print(f'  是否活跃: {inv.is_active}')
            print(f'  创建时间: {inv.created_at}')
            print(f'  更新时间: {inv.updated_at}')
            
    except Exception as e:
        print(f'检查数据时出错: {e}')
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == '__main__':
    check_inventory_data()
