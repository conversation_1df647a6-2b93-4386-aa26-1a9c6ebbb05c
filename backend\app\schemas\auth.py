from pydantic import BaseModel, EmailStr
from typing import List, Optional
from datetime import datetime

class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str
    password: str

class UserResponse(BaseModel):
    """用户响应模型"""
    id: int
    username: str
    email: str
    role: str
    permissions: List[str] = []
    avatar: Optional[str] = None
    is_active: bool = True
    created_at: Optional[str] = None

    class Config:
        from_attributes = True

class LoginResponse(BaseModel):
    """登录响应模型"""
    success: bool
    token: str
    user: dict
    message: str

class RegisterRequest(BaseModel):
    """注册请求模型"""
    username: str
    email: EmailStr
    password: str
    confirm_password: str

class ChangePasswordRequest(BaseModel):
    """修改密码请求模型"""
    old_password: str
    new_password: str

class ResetPasswordRequest(BaseModel):
    """重置密码请求模型"""
    email: EmailStr

class TokenResponse(BaseModel):
    """令牌响应模型"""
    success: bool
    token: str
    message: str
