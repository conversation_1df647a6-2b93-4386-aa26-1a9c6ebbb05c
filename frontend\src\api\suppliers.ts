import api from './index'

// 供应商接口定义
export interface Supplier {
  id: number
  name: string
  code: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  category?: string
  credit_rating: 'AAA' | 'AA' | 'A' | 'BBB' | 'BB' | 'B' | 'CCC' | 'CC' | 'C' | 'D'
  payment_terms?: string
  delivery_terms?: string
  credit_limit: number
  current_balance: number
  status: 'active' | 'inactive' | 'suspended' | 'terminated' | 'pending'
  is_active: boolean
  business_license?: string
  tax_number?: string
  bank_account?: any
  certificates?: any
  remark?: string
  created_at: string
  updated_at?: string
}

export interface SupplierListResponse {
  items: Supplier[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

export interface SupplierQuery {
  name?: string
  code?: string
  category?: string
  status?: string
  credit_rating?: string
  contact_person?: string
  address?: string
  page?: number
  page_size?: number
}

export interface SupplierCreate {
  name: string
  code: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  category?: string
  credit_rating?: string
  payment_terms?: string
  delivery_terms?: string
  credit_limit?: number
  status?: string
  business_license?: string
  tax_number?: string
  bank_account?: any
  certificates?: any
  remark?: string
}

export interface SupplierUpdate {
  name?: string
  code?: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  category?: string
  credit_rating?: string
  payment_terms?: string
  delivery_terms?: string
  credit_limit?: number
  status?: string
  business_license?: string
  tax_number?: string
  bank_account?: any
  certificates?: any
  remark?: string
}

// 供应商API
export const supplierApi = {
  // 获取供应商列表
  getSuppliers: (params?: SupplierQuery) => {
    return api.get<SupplierListResponse>('/api/suppliers/', { params })
  },

  // 获取单个供应商
  getSupplier: (id: number) => {
    return api.get<Supplier>(`/api/suppliers/${id}`)
  },

  // 创建供应商
  createSupplier: (data: SupplierCreate) => {
    return api.post<Supplier>('/api/suppliers/', data)
  },

  // 更新供应商
  updateSupplier: (id: number, data: SupplierUpdate) => {
    return api.put<Supplier>(`/api/suppliers/${id}`, data)
  },

  // 删除供应商
  deleteSupplier: (id: number) => {
    return api.delete(`/api/suppliers/${id}`)
  },

  // 获取供应商类别
  getSupplierCategories: () => {
    return api.get<string[]>('/api/suppliers/categories/list')
  },

  // 获取活跃供应商
  getActiveSuppliers: () => {
    return api.get<Supplier[]>('/api/suppliers/active/list')
  },

  // 搜索供应商
  searchSuppliers: (keyword: string) => {
    return api.get<Supplier[]>(`/api/suppliers/search/${keyword}`)
  },

  // 更新供应商余额
  updateSupplierBalance: (id: number, amount: number) => {
    return api.put<Supplier>(`/api/suppliers/${id}/balance`, { amount })
  },

  // 根据信用等级获取供应商
  getSuppliersByCreditRating: (rating: string) => {
    return api.get<Supplier[]>(`/api/suppliers/credit-rating/${rating}`)
  }
}
