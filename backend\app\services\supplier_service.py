"""
供应商服务层
"""

from sqlalchemy.orm import Session
from typing import List, Optional, Tuple
from app.models.supplier import Supplier
from app.schemas.supplier import (
    SupplierCreate, SupplierUpdate, SupplierQuery
)


class SupplierService:
    def __init__(self, db: Session):
        self.db = db

    def get_suppliers(self, skip: int = 0, limit: int = 100) -> List[Supplier]:
        """获取供应商列表"""
        return self.db.query(Supplier).offset(skip).limit(limit).all()

    def get_suppliers_paginated(self, query_params: SupplierQuery) -> Tuple[List[Supplier], int]:
        """分页获取供应商列表"""
        query = self.db.query(Supplier)
        
        # 应用过滤条件
        if query_params.name:
            query = query.filter(Supplier.name.contains(query_params.name))
        
        if query_params.code:
            query = query.filter(Supplier.code.contains(query_params.code))
        
        if query_params.category:
            query = query.filter(Supplier.category == query_params.category)
        
        if query_params.status:
            query = query.filter(Supplier.status == query_params.status)
        
        if query_params.credit_rating:
            query = query.filter(Supplier.credit_rating == query_params.credit_rating)
        
        if query_params.contact_person:
            query = query.filter(Supplier.contact_person.contains(query_params.contact_person))

        if query_params.address:
            query = query.filter(Supplier.address.contains(query_params.address))

        # 获取总数
        total = query.count()
        
        # 分页和排序
        suppliers = query.order_by(Supplier.created_at.desc()).offset(
            (query_params.page - 1) * query_params.page_size
        ).limit(query_params.page_size).all()
        
        return suppliers, total

    def get_supplier(self, supplier_id: int) -> Optional[Supplier]:
        """根据ID获取供应商"""
        return self.db.query(Supplier).filter(Supplier.id == supplier_id).first()

    def get_supplier_by_code(self, code: str) -> Optional[Supplier]:
        """根据编码获取供应商"""
        return self.db.query(Supplier).filter(Supplier.code == code).first()

    def create_supplier(self, supplier_data: SupplierCreate) -> Supplier:
        """创建供应商"""
        # 检查编码是否已存在
        existing = self.get_supplier_by_code(supplier_data.code)
        if existing:
            raise ValueError(f"供应商编码 {supplier_data.code} 已存在")
        
        db_supplier = Supplier(**supplier_data.model_dump())
        self.db.add(db_supplier)
        self.db.commit()
        self.db.refresh(db_supplier)
        return db_supplier

    def update_supplier(self, supplier_id: int, supplier_data: SupplierUpdate) -> Optional[Supplier]:
        """更新供应商"""
        db_supplier = self.get_supplier(supplier_id)
        if not db_supplier:
            return None
        
        # 如果更新编码，检查是否与其他供应商冲突
        if supplier_data.code and supplier_data.code != db_supplier.code:
            existing = self.get_supplier_by_code(supplier_data.code)
            if existing:
                raise ValueError(f"供应商编码 {supplier_data.code} 已存在")
        
        update_data = supplier_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_supplier, field, value)
        
        self.db.commit()
        self.db.refresh(db_supplier)
        return db_supplier

    def delete_supplier(self, supplier_id: int) -> bool:
        """删除供应商"""
        db_supplier = self.get_supplier(supplier_id)
        if not db_supplier:
            return False
        
        self.db.delete(db_supplier)
        self.db.commit()
        return True

    def get_supplier_categories(self) -> List[str]:
        """获取所有供应商类别"""
        categories = self.db.query(Supplier.category).distinct().filter(
            Supplier.category.isnot(None)
        ).all()
        return [cat[0] for cat in categories if cat[0]]

    def get_active_suppliers(self) -> List[Supplier]:
        """获取所有活跃的供应商"""
        return self.db.query(Supplier).filter(
            Supplier.status == "active",
            Supplier.is_active == True
        ).all()

    def update_supplier_balance(self, supplier_id: int, amount: float) -> Optional[Supplier]:
        """更新供应商余额"""
        db_supplier = self.get_supplier(supplier_id)
        if not db_supplier:
            return None
        
        db_supplier.current_balance += amount
        self.db.commit()
        self.db.refresh(db_supplier)
        return db_supplier

    def get_suppliers_by_credit_rating(self, rating: str) -> List[Supplier]:
        """根据信用等级获取供应商"""
        return self.db.query(Supplier).filter(Supplier.credit_rating == rating).all()

    def search_suppliers(self, keyword: str) -> List[Supplier]:
        """搜索供应商"""
        return self.db.query(Supplier).filter(
            Supplier.name.contains(keyword) |
            Supplier.code.contains(keyword) |
            Supplier.contact_person.contains(keyword)
        ).all()
