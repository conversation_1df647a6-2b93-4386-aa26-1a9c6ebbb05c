from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import math
from app.core.database import get_db
from app.schemas.product import (
    Product, ProductCreate, ProductUpdate, ProductListResponse,
    ProductQuery, ProductStatus,
    ProductAnalysisRequest, ProductAnalysisResponse,
    ProductRecommendationRequest, ProductRecommendationResponse
)
from app.services.product_service import ProductService

router = APIRouter()

@router.get("/", response_model=ProductListResponse)
async def get_products(
    name: Optional[str] = Query(None, description="商品名称"),
    category: Optional[str] = Query(None, description="商品类别"),
    brand: Optional[str] = Query(None, description="品牌"),
    status: Optional[ProductStatus] = Query(None, description="商品状态"),
    min_price: Optional[float] = Query(None, ge=0, description="最低价格"),
    max_price: Optional[float] = Query(None, ge=0, description="最高价格"),
    low_stock: Optional[bool] = Query(None, description="是否只显示低库存商品"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取商品列表"""
    service = ProductService(db)

    # 构建查询参数
    query_params = ProductQuery(
        name=name,
        category=category,
        brand=brand,
        status=status,
        min_price=min_price,
        max_price=max_price,
        low_stock=low_stock,
        page=page,
        page_size=page_size
    )

    products, total = service.get_products_paginated(query_params)
    total_pages = math.ceil(total / page_size)

    return ProductListResponse(
        items=products,
        total=total,
        page=page,
        page_size=page_size,
        total_pages=total_pages
    )

@router.get("/{product_id}", response_model=Product)
async def get_product(product_id: int, db: Session = Depends(get_db)):
    """获取单个商品详情"""
    service = ProductService(db)
    product = service.get_product(product_id)
    if not product:
        raise HTTPException(status_code=404, detail="商品不存在")
    return product

@router.post("/", response_model=Product)
async def create_product(product: ProductCreate, db: Session = Depends(get_db)):
    """创建新商品"""
    service = ProductService(db)
    return service.create_product(product)

@router.put("/{product_id}", response_model=Product)
async def update_product(
    product_id: int, 
    product: ProductUpdate, 
    db: Session = Depends(get_db)
):
    """更新商品信息"""
    service = ProductService(db)
    updated_product = service.update_product(product_id, product)
    if not updated_product:
        raise HTTPException(status_code=404, detail="商品不存在")
    return updated_product

@router.delete("/{product_id}")
async def delete_product(product_id: int, db: Session = Depends(get_db)):
    """删除商品"""
    service = ProductService(db)
    success = service.delete_product(product_id)
    if not success:
        raise HTTPException(status_code=404, detail="商品不存在")
    return {"message": "商品删除成功"}

@router.post("/analysis", response_model=List[ProductAnalysisResponse])
async def analyze_products(
    request: ProductAnalysisRequest,
    db: Session = Depends(get_db)
):
    """商品分析"""
    service = ProductService(db)
    return service.analyze_products(request)

@router.get("/trends/market")
async def get_market_trends(
    category: str = None,
    time_period: str = "30d",
    db: Session = Depends(get_db)
):
    """获取市场趋势"""
    service = ProductService(db)
    return service.get_market_trends(category, time_period)

@router.post("/recommend", response_model=ProductRecommendationResponse)
async def recommend_products(
    request: ProductRecommendationRequest,
    db: Session = Depends(get_db)
):
    """商品推荐"""
    service = ProductService(db)
    return service.recommend_products(request)

@router.get("/analytics/dashboard")
async def get_analytics_dashboard(db: Session = Depends(get_db)):
    """获取分析仪表板数据"""
    service = ProductService(db)
    return service.get_dashboard_data()
