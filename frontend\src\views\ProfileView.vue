<template>
  <div class="profile-view">
    <div class="page-header">
      <h2>个人中心</h2>
    </div>

    <el-row :gutter="20">
      <!-- 左侧个人信息 -->
      <el-col :span="8">
        <el-card class="profile-card">
          <template #header>
            <div class="card-header">
              <el-icon><User /></el-icon>
              <span>个人信息</span>
            </div>
          </template>
          
          <div class="profile-info">
            <div class="avatar-section">
              <el-avatar 
                :src="authStore.user?.avatar" 
                :size="80"
                class="profile-avatar"
              >
                <el-icon size="40"><User /></el-icon>
              </el-avatar>
              <el-button size="small" @click="showAvatarDialog = true">
                更换头像
              </el-button>
            </div>
            
            <div class="info-section">
              <div class="info-item">
                <span class="label">用户名:</span>
                <span class="value">{{ authStore.user?.username }}</span>
              </div>
              <div class="info-item">
                <span class="label">邮箱:</span>
                <span class="value">{{ authStore.user?.email }}</span>
              </div>
              <div class="info-item">
                <span class="label">角色:</span>
                <el-tag :type="getRoleTagType(authStore.user?.role)">
                  {{ getRoleText(authStore.user?.role) }}
                </el-tag>
              </div>
              <div class="info-item">
                <span class="label">注册时间:</span>
                <span class="value">2024-01-01</span>
              </div>
            </div>
            
            <div class="action-section">
              <el-button type="primary" @click="showEditDialog = true">
                编辑资料
              </el-button>
              <el-button @click="showPasswordDialog = true">
                修改密码
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 右侧统计信息 -->
      <el-col :span="16">
        <el-card class="stats-card">
          <template #header>
            <div class="card-header">
              <el-icon><DataAnalysis /></el-icon>
              <span>使用统计</span>
            </div>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-icon">
                  <el-icon size="24" color="#409EFF">
                    <Box />
                  </el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">156</div>
                  <div class="stat-label">管理商品</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-icon">
                  <el-icon size="24" color="#67C23A">
                    <TrendCharts />
                  </el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">89</div>
                  <div class="stat-label">分析报告</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-icon">
                  <el-icon size="24" color="#E6A23C">
                    <Van />
                  </el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">234</div>
                  <div class="stat-label">配送订单</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-icon">
                  <el-icon size="24" color="#F56C6C">
                    <Clock />
                  </el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">45</div>
                  <div class="stat-label">在线时长(h)</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
        
        <!-- 最近活动 -->
        <el-card class="activity-card">
          <template #header>
            <div class="card-header">
              <el-icon><Clock /></el-icon>
              <span>最近活动</span>
            </div>
          </template>
          
          <el-timeline>
            <el-timeline-item timestamp="2024-01-15 10:30" placement="top">
              <el-card>
                <h4>创建了新的商品分析报告</h4>
                <p>对"智能手机"类别进行了市场趋势分析</p>
              </el-card>
            </el-timeline-item>
            
            <el-timeline-item timestamp="2024-01-14 16:20" placement="top">
              <el-card>
                <h4>优化了配送路线</h4>
                <p>北京-上海线路成本降低了15%</p>
              </el-card>
            </el-timeline-item>
            
            <el-timeline-item timestamp="2024-01-13 14:15" placement="top">
              <el-card>
                <h4>添加了新商品</h4>
                <p>成功添加了5个新的电子产品</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>

    <!-- 编辑资料对话框 -->
    <el-dialog
      title="编辑个人资料"
      v-model="showEditDialog"
      width="500px"
    >
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="用户名">
          <el-input v-model="editForm.username" />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="editForm.email" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="handleUpdateProfile">保存</el-button>
      </template>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog
      title="修改密码"
      v-model="showPasswordDialog"
      width="400px"
    >
      <el-form :model="passwordForm" label-width="100px">
        <el-form-item label="当前密码">
          <el-input v-model="passwordForm.oldPassword" type="password" />
        </el-form-item>
        <el-form-item label="新密码">
          <el-input v-model="passwordForm.newPassword" type="password" />
        </el-form-item>
        <el-form-item label="确认密码">
          <el-input v-model="passwordForm.confirmPassword" type="password" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showPasswordDialog = false">取消</el-button>
        <el-button type="primary" @click="handleChangePassword">确认修改</el-button>
      </template>
    </el-dialog>

    <!-- 更换头像对话框 -->
    <el-dialog
      title="更换头像"
      v-model="showAvatarDialog"
      width="400px"
    >
      <div class="avatar-upload">
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          :auto-upload="false"
          :on-change="handleAvatarChange"
        >
          <img v-if="newAvatar" :src="newAvatar" class="avatar-preview" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
        <p class="upload-tip">点击上传新头像，支持 JPG、PNG 格式</p>
      </div>
      
      <template #footer>
        <el-button @click="showAvatarDialog = false">取消</el-button>
        <el-button type="primary" @click="handleUpdateAvatar">确认更换</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useAuthStore } from '../stores/auth'
import { ElMessage } from 'element-plus'

const authStore = useAuthStore()

const showEditDialog = ref(false)
const showPasswordDialog = ref(false)
const showAvatarDialog = ref(false)
const newAvatar = ref('')

const editForm = reactive({
  username: authStore.user?.username || '',
  email: authStore.user?.email || ''
})

const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const getRoleTagType = (role: string) => {
  const roleMap = {
    'admin': 'danger',
    'user': 'primary',
    'guest': 'info'
  }
  return roleMap[role] || 'info'
}

const getRoleText = (role: string) => {
  const roleMap = {
    'admin': '管理员',
    'user': '普通用户',
    'guest': '访客'
  }
  return roleMap[role] || role
}

const handleUpdateProfile = async () => {
  try {
    const result = await authStore.updateProfile(editForm)
    if (result.success) {
      ElMessage.success('资料更新成功')
      showEditDialog.value = false
    } else {
      ElMessage.error(result.message || '更新失败')
    }
  } catch (error) {
    ElMessage.error('更新失败')
  }
}

const handleChangePassword = async () => {
  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    ElMessage.error('两次输入的密码不一致')
    return
  }
  
  try {
    const result = await authStore.changePassword(
      passwordForm.oldPassword,
      passwordForm.newPassword
    )
    
    if (result.success) {
      ElMessage.success('密码修改成功')
      showPasswordDialog.value = false
      
      // 重置表单
      passwordForm.oldPassword = ''
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
    } else {
      ElMessage.error(result.message || '修改失败')
    }
  } catch (error) {
    ElMessage.error('修改失败')
  }
}

const handleAvatarChange = (file: any) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    newAvatar.value = e.target?.result as string
  }
  reader.readAsDataURL(file.raw)
}

const handleUpdateAvatar = () => {
  if (!newAvatar.value) {
    ElMessage.warning('请先选择头像')
    return
  }
  
  // 这里应该上传头像到服务器
  ElMessage.success('头像更换成功')
  showAvatarDialog.value = false
  newAvatar.value = ''
}
</script>

<style scoped>
.profile-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.profile-card {
  margin-bottom: 20px;
}

.profile-info {
  text-align: center;
}

.avatar-section {
  margin-bottom: 30px;
}

.profile-avatar {
  margin-bottom: 16px;
  border: 3px solid #409EFF;
}

.info-section {
  margin-bottom: 30px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  color: #909399;
  font-weight: 500;
}

.value {
  color: #2c3e50;
}

.action-section {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.stats-card {
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.stat-icon {
  margin-right: 16px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-label {
  color: #7f8c8d;
  font-size: 14px;
}

.activity-card {
  margin-bottom: 20px;
}

.avatar-upload {
  text-align: center;
}

.avatar-uploader {
  display: inline-block;
}

.avatar-uploader :deep(.el-upload) {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.avatar-uploader :deep(.el-upload:hover) {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}

.avatar-preview {
  width: 120px;
  height: 120px;
  display: block;
  object-fit: cover;
}

.upload-tip {
  margin-top: 12px;
  color: #7f8c8d;
  font-size: 14px;
}
</style>
