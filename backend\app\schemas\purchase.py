"""
采购订单相关的Pydantic模型
"""

from typing import List, Optional, Any
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field
from enum import Enum


class PurchaseOrderStatus(str, Enum):
    """采购订单状态"""
    DRAFT = "draft"
    SUBMITTED = "submitted"
    APPROVED = "approved"
    REJECTED = "rejected"
    PURCHASING = "purchasing"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class PurchaseReceiptStatus(str, Enum):
    """采购入库单状态"""
    DRAFT = "draft"           # 草稿
    SUBMITTED = "submitted"   # 已提交
    APPROVED = "approved"     # 已审核
    REJECTED = "rejected"     # 已拒绝
    CANCELLED = "cancelled"   # 已取消


class PurchaseOrderItemBase(BaseModel):
    """采购订单明细基础模型"""
    product_id: int = Field(..., description="商品ID")
    line_number: Optional[int] = Field(None, gt=0, description="行号")
    product_name: str = Field(..., description="商品名称")
    product_sku: Optional[str] = Field(None, description="商品SKU")
    quantity: int = Field(..., gt=0, description="采购数量")
    unit_price: float = Field(..., ge=0, description="单价")
    total_price: float = Field(..., ge=0, description="小计")


class PurchaseOrderItemCreate(PurchaseOrderItemBase):
    """创建采购订单明细"""
    pass


class PurchaseOrderItemUpdate(BaseModel):
    """更新采购订单明细"""
    product_id: Optional[int] = None
    line_number: Optional[int] = Field(None, gt=0)
    product_name: Optional[str] = None
    product_sku: Optional[str] = None
    quantity: Optional[int] = Field(None, gt=0)
    unit_price: Optional[float] = Field(None, ge=0)
    total_price: Optional[float] = Field(None, ge=0)


class PurchaseOrderItem(PurchaseOrderItemBase):
    """采购订单明细"""
    id: int
    order_id: int
    received_quantity: int = Field(default=0, description="已收货数量")
    created_at: datetime

    class Config:
        from_attributes = True


class PurchaseOrderBase(BaseModel):
    """采购订单基础模型"""
    supplier_id: int = Field(..., description="供应商ID")
    total_amount: float = Field(..., ge=0, description="订单总金额")
    status: PurchaseOrderStatus = Field(default=PurchaseOrderStatus.DRAFT, description="订单状态")
    expected_date: Optional[datetime] = Field(None, description="预期到货日期")
    created_by: str = Field(..., description="创建人")
    remark: Optional[str] = Field(None, description="备注")


class PurchaseOrderCreate(PurchaseOrderBase):
    """创建采购订单"""
    items: List[PurchaseOrderItemCreate] = Field(..., description="订单明细")


class PurchaseOrderUpdate(BaseModel):
    """更新采购订单"""
    supplier_id: Optional[int] = None
    total_amount: Optional[float] = Field(None, ge=0)
    status: Optional[PurchaseOrderStatus] = None
    expected_date: Optional[datetime] = None
    submitted_by: Optional[str] = None
    submitted_at: Optional[datetime] = None
    approved_by: Optional[str] = None
    approved_at: Optional[datetime] = None
    remark: Optional[str] = None
    items: Optional[List[PurchaseOrderItemCreate]] = None


class PurchaseOrder(PurchaseOrderBase):
    """采购订单"""
    id: int
    order_no: str = Field(..., description="采购订单号")
    submitted_by: Optional[str] = Field(None, description="提交人")
    submitted_at: Optional[datetime] = Field(None, description="提交时间")
    approved_by: Optional[str] = Field(None, description="审批人")
    approved_at: Optional[datetime] = Field(None, description="审批时间")
    created_at: datetime
    updated_at: Optional[datetime] = None
    items: List[PurchaseOrderItem] = Field(default=[], description="订单明细")
    
    # 供应商信息
    supplier_name: Optional[str] = Field(None, description="供应商名称")

    class Config:
        from_attributes = True


class PurchaseOrderQuery(BaseModel):
    """采购订单查询参数"""
    order_no: Optional[str] = None
    supplier_id: Optional[int] = None
    status: Optional[PurchaseOrderStatus] = None
    created_by: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None


class PurchaseOrderListResponse(BaseModel):
    """采购订单列表响应"""
    items: List[PurchaseOrder]
    total: int
    page: int
    page_size: int
    total_pages: int


class PurchaseOrderStats(BaseModel):
    """采购订单统计"""
    total_orders: int = Field(default=0, description="订单总数")
    pending_orders: int = Field(default=0, description="待处理订单")
    processing_orders: int = Field(default=0, description="处理中订单")
    completed_orders: int = Field(default=0, description="已完成订单")
    total_amount: float = Field(default=0, description="订单总金额")
    this_month_orders: int = Field(default=0, description="本月订单数")


# 采购入库单明细相关Schema
class PurchaseReceiptItemBase(BaseModel):
    product_id: int
    product_name: str
    product_sku: Optional[str] = None
    quantity: int = Field(..., ge=0, description="数量")
    batch_no: Optional[str] = Field(None, description="批次号")

class PurchaseReceiptItemCreate(PurchaseReceiptItemBase):
    pass

class PurchaseReceiptItemUpdate(BaseModel):
    product_id: int
    quantity: int = Field(..., ge=0, description="数量")
    batch_no: Optional[str] = Field(None, description="批次号")

class PurchaseReceiptItem(PurchaseReceiptItemBase):
    id: int
    receipt_id: int
    created_at: datetime

    class Config:
        from_attributes = True


# 采购入库单相关Schema
class PurchaseReceiptBase(BaseModel):
    purchase_order_id: Optional[int] = None
    purchase_order_no: Optional[str] = None
    supplier_id: int
    warehouse_id: int
    status: PurchaseReceiptStatus = PurchaseReceiptStatus.DRAFT
    receipt_date: Optional[datetime] = None
    created_by: str
    submitted_by: Optional[str] = None
    submitted_at: Optional[datetime] = None
    approved_by: Optional[str] = None
    approved_at: Optional[datetime] = None
    remark: Optional[str] = None

class PurchaseReceiptCreate(PurchaseReceiptBase):
    items: List[PurchaseReceiptItemCreate]

class PurchaseReceiptUpdate(BaseModel):
    supplier_id: Optional[int] = None
    warehouse_id: Optional[int] = None
    status: Optional[PurchaseReceiptStatus] = None
    receipt_date: Optional[datetime] = None
    submitted_by: Optional[str] = None
    submitted_at: Optional[datetime] = None
    approved_by: Optional[str] = None
    approved_at: Optional[datetime] = None
    remark: Optional[str] = None
    items: Optional[List[PurchaseReceiptItemCreate]] = None

class PurchaseReceipt(PurchaseReceiptBase):
    id: int
    receipt_no: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    items: List[PurchaseReceiptItem] = []

    # 动态添加的字段
    supplier_name: Optional[str] = None
    warehouse_name: Optional[str] = None

    class Config:
        from_attributes = True


# 采购入库单查询参数
class PurchaseReceiptQuery(BaseModel):
    receipt_no: Optional[str] = None
    purchase_order_no: Optional[str] = None
    supplier_id: Optional[int] = None
    warehouse_id: Optional[int] = None
    status: Optional[PurchaseReceiptStatus] = None
    created_by: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None


# 采购入库单列表响应
class PurchaseReceiptListResponse(BaseModel):
    items: List[PurchaseReceipt]
    total: int
    page: int
    page_size: int
    total_pages: int


# ==================== 采购退货单相关Schema ====================

class PurchaseReturnStatus(str, Enum):
    """采购退货单状态"""
    DRAFT = "draft"
    SUBMITTED = "submitted"
    APPROVED = "approved"
    REJECTED = "rejected"
    RETURNED = "returned"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class PurchaseReturnItemBase(BaseModel):
    """采购退货单明细基础模型"""
    product_id: int = Field(..., description="商品ID")
    product_name: str = Field(..., description="商品名称")
    product_sku: Optional[str] = Field(None, description="商品SKU")
    return_quantity: int = Field(..., gt=0, description="退货数量")
    unit_price: Decimal = Field(..., ge=0, description="单价")
    total_price: Decimal = Field(..., ge=0, description="小计金额")
    quality_issue: Optional[str] = Field(None, description="质量问题描述")


class PurchaseReturnItemCreate(PurchaseReturnItemBase):
    """创建采购退货单明细"""
    pass


class PurchaseReturnItemUpdate(BaseModel):
    """更新采购退货单明细"""
    product_id: Optional[int] = None
    product_name: Optional[str] = None
    product_sku: Optional[str] = None
    return_quantity: Optional[int] = Field(None, gt=0)
    unit_price: Optional[Decimal] = Field(None, ge=0)
    total_price: Optional[Decimal] = Field(None, ge=0)
    quality_issue: Optional[str] = None


class PurchaseReturnItem(PurchaseReturnItemBase):
    """采购退货单明细"""
    id: int
    purchase_return_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class PurchaseReturnBase(BaseModel):
    """采购退货单基础模型"""
    return_no: str = Field(..., description="退货单号")
    purchase_receipt_id: Optional[int] = Field(None, description="采购入库单ID")
    supplier_id: int = Field(..., description="供应商ID")
    return_date: datetime = Field(..., description="退货日期")
    reason: str = Field(..., description="退货原因")
    total_amount: Decimal = Field(..., ge=0, description="退货总金额")
    remark: Optional[str] = Field(None, description="备注")


class PurchaseReturnCreate(PurchaseReturnBase):
    """创建采购退货单"""
    items: List[PurchaseReturnItemCreate] = Field(..., description="退货明细")


class PurchaseReturnUpdate(BaseModel):
    """更新采购退货单"""
    return_no: Optional[str] = None
    purchase_receipt_id: Optional[int] = None
    supplier_id: Optional[int] = None
    return_date: Optional[datetime] = None
    reason: Optional[str] = None
    total_amount: Optional[Decimal] = Field(None, ge=0)
    remark: Optional[str] = None
    items: Optional[List[PurchaseReturnItemCreate]] = None


class PurchaseReturnStatusUpdate(BaseModel):
    """更新采购退货单状态"""
    status: PurchaseReturnStatus = Field(..., description="新状态")
    note: Optional[str] = Field(None, description="操作备注")


class PurchaseReturn(PurchaseReturnBase):
    """采购退货单"""
    id: int
    status: PurchaseReturnStatus

    # 审核信息
    submitted_at: Optional[datetime] = None
    submitted_by: Optional[str] = None
    approved_at: Optional[datetime] = None
    approved_by: Optional[str] = None
    approval_note: Optional[str] = None

    # 退货信息
    returned_at: Optional[datetime] = None
    returned_by: Optional[str] = None

    # 时间戳
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None

    # 关联信息
    supplier_name: Optional[str] = None
    purchase_receipt_no: Optional[str] = None

    # 明细
    items: List[PurchaseReturnItem] = []

    class Config:
        from_attributes = True


class PurchaseReturnStats(BaseModel):
    """采购退货单统计"""
    total_returns: int = Field(0, description="退货单总数")
    draft_returns: int = Field(0, description="草稿数量")
    submitted_returns: int = Field(0, description="已提交数量")
    approved_returns: int = Field(0, description="已审核数量")
    returned_returns: int = Field(0, description="已退货数量")
    completed_returns: int = Field(0, description="已完成数量")
    total_amount: Decimal = Field(0, description="退货总金额")


class PurchaseReturnQuery(BaseModel):
    """采购退货单查询参数"""
    return_no: Optional[str] = Field(None, description="退货单号")
    supplier_id: Optional[int] = Field(None, description="供应商ID")
    status: Optional[PurchaseReturnStatus] = Field(None, description="状态")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
