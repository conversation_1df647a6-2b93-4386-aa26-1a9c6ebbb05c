<template>
  <div class="inventory-query-view">
    <div class="page-header">
      <div class="header-left">
        <h2>库存查询</h2>
        <p class="page-description">实时查询商品库存信息，掌握库存动态</p>
      </div>
      <div class="header-right">
        <el-button @click="refreshInventory" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="exportInventory">
          <el-icon><Download /></el-icon>
          导出库存
        </el-button>
      </div>
    </div>

    <!-- 库存概览 -->
    <el-row :gutter="20" class="inventory-overview">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon total">
              <el-icon size="32"><Box /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ inventoryStats.total_products }}</div>
              <div class="overview-label">商品总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon normal">
              <el-icon size="32"><CircleCheck /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ inventoryStats.normal_stock }}</div>
              <div class="overview-label">库存正常</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon warning">
              <el-icon size="32"><Warning /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ inventoryStats.low_stock }}</div>
              <div class="overview-label">库存不足</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon danger">
              <el-icon size="32"><CircleClose /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ inventoryStats.out_of_stock }}</div>
              <div class="overview-label">缺货商品</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true">
        <el-form-item label="商品名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入商品名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="商品SKU">
          <el-input
            v-model="searchForm.sku"
            placeholder="请输入商品SKU"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        
        <el-form-item label="商品分类">
          <el-select v-model="searchForm.category" placeholder="选择分类" clearable style="width: 150px">
            <el-option label="电子产品" value="electronics" />
            <el-option label="服装鞋帽" value="clothing" />
            <el-option label="家居用品" value="home" />
            <el-option label="美妆护肤" value="beauty" />
            <el-option label="食品饮料" value="food" />
            <el-option label="运动户外" value="sports" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="库存状态">
          <el-select v-model="searchForm.stock_status" placeholder="选择状态" clearable style="width: 120px">
            <el-option label="库存充足" value="sufficient" />
            <el-option label="库存不足" value="low" />
            <el-option label="缺货" value="out" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="仓库">
          <el-select v-model="searchForm.warehouse" placeholder="选择仓库" clearable style="width: 120px">
            <el-option
              v-for="warehouse in warehouses"
              :key="warehouse.id"
              :label="warehouse.name"
              :value="warehouse.name"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="批次号">
          <el-input
            v-model="searchForm.batch_no"
            placeholder="请输入批次号"
            clearable
            style="width: 150px"
          />
        </el-form-item>

        <el-form-item label="生产日期">
          <el-date-picker
            v-model="searchForm.production_date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>

        <el-form-item label="到期日期">
          <el-date-picker
            v-model="searchForm.expiry_date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>

        <el-form-item label="过期状态">
          <el-select v-model="searchForm.expiry_status" placeholder="选择状态" clearable style="width: 120px">
            <el-option label="未过期" value="not_expired" />
            <el-option label="已过期" value="expired" />
            <el-option label="临近过期" value="near_expiry" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="searchInventory">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 功能标签页 -->
    <el-card class="tabs-card">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="库存查询" name="inventory">
          <!-- 库存列表 -->
    <el-card class="inventory-list-card">
      <template #header>
        <span>库存信息 (共 {{ total }} 条)</span>
        <div class="header-actions">
          <el-button-group>
            <el-button :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
              <el-icon><List /></el-icon>
              表格视图
            </el-button>
            <el-button :type="viewMode === 'card' ? 'primary' : ''" @click="viewMode = 'card'">
              <el-icon><Grid /></el-icon>
              卡片视图
            </el-button>
          </el-button-group>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'">
        <el-table :data="inventoryList" style="width: 100%" v-loading="loading">
          <el-table-column label="商品信息" min-width="250">
            <template #default="{ row }">
              <div class="product-info">
                <img :src="getImageUrl(row.product_image)" :alt="row.product_name" class="product-image" />
                <div class="product-details">
                  <div class="product-name">{{ row.product_name }}</div>
                  <div class="product-sku">SKU: {{ row.product_sku }}</div>
                  <div class="product-category">{{ getCategoryLabel(row.product_category) }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="warehouse_name" label="仓库" width="100" />

          <el-table-column prop="batch_no" label="批次号" width="120">
            <template #default="{ row }">
              <span v-if="row.batch_no" class="batch-no">{{ row.batch_no }}</span>
              <span v-else class="no-batch">-</span>
            </template>
          </el-table-column>

          <el-table-column label="生产日期" width="120">
            <template #default="{ row }">
              <span v-if="row.production_date" class="production-date">
                {{ formatDate(row.production_date) }}
              </span>
              <span v-else class="no-date">-</span>
            </template>
          </el-table-column>

          <el-table-column label="到期日期" width="120">
            <template #default="{ row }">
              <span v-if="row.expiry_date" :class="getExpiryClass(row.expiry_date)">
                {{ formatDate(row.expiry_date) }}
              </span>
              <span v-else class="no-date">-</span>
            </template>
          </el-table-column>

          <el-table-column prop="current_stock" label="当前库存" width="100">
            <template #default="{ row }">
              <span :class="getStockClass(row.current_stock, row.min_stock)">
                {{ row.current_stock }}
              </span>
            </template>
          </el-table-column>
          
          <el-table-column prop="min_stock" label="最低库存" width="100">
            <template #default="{ row }">
              <span class="min-stock">{{ row.min_stock }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="max_stock" label="最高库存" width="100">
            <template #default="{ row }">
              <span class="max-stock">{{ row.max_stock }}</span>
            </template>
          </el-table-column>
          
          <el-table-column label="库存状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStockStatusTagType(row.current_stock, row.min_stock)">
                {{ getStockStatusLabel(row.current_stock, row.min_stock) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="reserved_stock" label="预留库存" width="100">
            <template #default="{ row }">
              <span class="reserved-stock">{{ row.reserved_stock || 0 }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="available_stock" label="可用库存" width="100">
            <template #default="{ row }">
              <span class="available-stock">{{ row.current_stock - (row.reserved_stock || 0) }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="updated_at" label="更新时间" width="150">
            <template #default="{ row }">
              {{ formatDateTime(row.updated_at || row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="viewInventoryDetail(row)">详情</el-button>
              <el-button size="small" type="primary" @click="adjustStock(row)">调整</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <div class="inventory-grid">
          <div
            v-for="item in inventoryList"
            :key="`${item.product_id}-${item.warehouse_id}`"
            class="inventory-card"
          >
            <div class="card-header">
              <img :src="getImageUrl(item.product_image)" :alt="item.product_name" class="card-image" />
              <div class="card-status">
                <el-tag :type="getStockStatusTagType(item.current_stock, item.min_stock)" size="small">
                  {{ getStockStatusLabel(item.current_stock, item.min_stock) }}
                </el-tag>
              </div>
            </div>

            <div class="card-content">
              <h4 class="card-title">{{ item.product_name }}</h4>
              <p class="card-sku">SKU: {{ item.product_sku }}</p>
              <p class="card-warehouse">仓库: {{ item.warehouse_name }}</p>

              <!-- 批次信息 -->
              <div class="batch-info" v-if="item.batch_no || item.production_date || item.expiry_date">
                <div class="batch-row" v-if="item.batch_no">
                  <span class="label">批次号:</span>
                  <span class="batch-no">{{ item.batch_no }}</span>
                </div>
                <div class="batch-row" v-if="item.production_date">
                  <span class="label">生产日期:</span>
                  <span class="production-date">{{ formatDate(item.production_date) }}</span>
                </div>
                <div class="batch-row" v-if="item.expiry_date">
                  <span class="label">到期日期:</span>
                  <span :class="getExpiryClass(item.expiry_date)">{{ formatDate(item.expiry_date) }}</span>
                </div>
              </div>

              <div class="stock-info">
                <div class="stock-row">
                  <span class="label">当前库存:</span>
                  <span :class="getStockClass(item.current_stock, item.min_stock)">
                    {{ item.current_stock }}
                  </span>
                </div>
                <div class="stock-row">
                  <span class="label">最低库存:</span>
                  <span class="min-stock">{{ item.min_stock }}</span>
                </div>
                <div class="stock-row">
                  <span class="label">可用库存:</span>
                  <span class="available-stock">{{ item.current_stock - (item.reserved_stock || 0) }}</span>
                </div>
              </div>
              
              <div class="stock-progress">
                <div class="progress-label">库存水平</div>
                <el-progress 
                  :percentage="Math.min(100, (item.current_stock / item.max_stock) * 100)"
                  :color="getProgressColor(item.current_stock, item.min_stock, item.max_stock)"
                  :show-text="false"
                />
              </div>
            </div>
            
            <div class="card-actions">
              <el-button size="small" @click="viewInventoryDetail(item)">详情</el-button>
              <el-button size="small" type="primary" @click="adjustStock(item)">调整</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 库存详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="库存详情"
      width="600px"
    >
      <div v-if="selectedInventory" class="inventory-detail">
        <div class="detail-header">
          <img :src="getImageUrl(selectedInventory.product_image)" :alt="selectedInventory.product_name" class="detail-image" />
          <div class="detail-info">
            <h3>{{ selectedInventory.product_name }}</h3>
            <p><strong>SKU:</strong> {{ selectedInventory.product_sku }}</p>
            <p><strong>分类:</strong> {{ getCategoryLabel(selectedInventory.product_category) }}</p>
            <p><strong>仓库:</strong> {{ selectedInventory.warehouse_name }}</p>
          </div>
        </div>
        
        <div class="detail-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="当前库存">
              <span :class="getStockClass(selectedInventory.current_stock, selectedInventory.min_stock)">
                {{ selectedInventory.current_stock }}
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="预留库存">{{ selectedInventory.reserved_stock || 0 }}</el-descriptions-item>
            <el-descriptions-item label="可用库存">{{ selectedInventory.current_stock - (selectedInventory.reserved_stock || 0) }}</el-descriptions-item>
            <el-descriptions-item label="最低库存">{{ selectedInventory.min_stock }}</el-descriptions-item>
            <el-descriptions-item label="最高库存">{{ selectedInventory.max_stock }}</el-descriptions-item>
            <el-descriptions-item label="库存状态">
              <el-tag :type="getStockStatusTagType(selectedInventory.current_stock, selectedInventory.min_stock)">
                {{ getStockStatusLabel(selectedInventory.current_stock, selectedInventory.min_stock) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="批次号">
              <span v-if="selectedInventory.batch_no" class="batch-no">{{ selectedInventory.batch_no }}</span>
              <span v-else class="no-batch">-</span>
            </el-descriptions-item>
            <el-descriptions-item label="生产日期">
              <span v-if="selectedInventory.production_date" class="production-date">
                {{ formatDate(selectedInventory.production_date) }}
              </span>
              <span v-else class="no-date">-</span>
            </el-descriptions-item>
            <el-descriptions-item label="到期日期">
              <span v-if="selectedInventory.expiry_date" :class="getExpiryClass(selectedInventory.expiry_date)">
                {{ formatDate(selectedInventory.expiry_date) }}
              </span>
              <span v-else class="no-date">-</span>
            </el-descriptions-item>
            <el-descriptions-item label="最后更新" :span="2">{{ formatDateTime(selectedInventory.updated_at || selectedInventory.created_at) }}</el-descriptions-item>
          </el-descriptions>
          
          <div class="stock-chart">
            <h4>库存趋势</h4>
            <div class="chart-placeholder">
              <el-icon size="48" color="#ddd"><TrendCharts /></el-icon>
              <p>库存趋势图表</p>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
        </el-tab-pane>


      </el-tabs>
    </el-card>

    <!-- 库存调整对话框 -->
    <el-dialog
      v-model="showAdjustDialog"
      title="库存调整"
      width="500px"
    >
      <el-form :model="adjustForm" :rules="adjustRules" ref="adjustFormRef" label-width="100px">
        <el-form-item label="商品信息">
          <div v-if="adjustingInventory">
            <p><strong>{{ adjustingInventory.product_name }}</strong></p>
            <p>SKU: {{ adjustingInventory.product_sku }}</p>
            <p>当前库存: {{ adjustingInventory.current_stock }}</p>
          </div>
        </el-form-item>
        
        <el-form-item label="调整类型" prop="type">
          <el-radio-group v-model="adjustForm.type">
            <el-radio value="in">入库</el-radio>
            <el-radio value="out">出库</el-radio>
            <el-radio value="adjust">调整</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="调整数量" prop="quantity">
          <el-input-number
            v-model="adjustForm.quantity"
            :min="adjustForm.type === 'adjust' ? 0 : 1"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="调整原因" prop="reason">
          <el-select v-model="adjustForm.reason" placeholder="选择调整原因" style="width: 100%">
            <el-option label="采购入库" value="purchase" />
            <el-option label="销售出库" value="sale" />
            <el-option label="盘点调整" value="inventory" />
            <el-option label="损耗报废" value="damage" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="adjustForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入调整备注"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAdjustDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmAdjust" :loading="adjusting">确认调整</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { inventoryApi, type InventoryItem, type InventoryStats, type InventoryAdjustment, type Warehouse } from '@/api/inventory'
import {
  Refresh,
  Download,
  Box,
  CircleCheck,
  Warning,
  CircleClose,
  Search,
  List,
  Grid,
  TrendCharts
} from '@element-plus/icons-vue'

// 类型定义

interface SearchForm {
  name: string
  sku: string
  category: string
  stock_status: string
  warehouse: string
  batch_no: string
  production_date_range: string[]
  expiry_date_range: string[]
  expiry_status: string
}

interface AdjustForm {
  type: 'in' | 'out' | 'adjust'
  quantity: number
  reason: string
  remark: string
}

// 响应式数据
const inventoryList = ref<InventoryItem[]>([])
const warehouses = ref<Warehouse[]>([])
const loading = ref(false)
const adjusting = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const viewMode = ref<'table' | 'card'>('table')
const activeTab = ref('inventory')

const showDetailDialog = ref(false)
const showAdjustDialog = ref(false)
const selectedInventory = ref<InventoryItem | null>(null)
const adjustingInventory = ref<InventoryItem | null>(null)

const inventoryStats = ref<InventoryStats>({
  total_products: 0,
  total_warehouses: 0,
  normal_stock: 0,
  low_stock: 0,
  out_of_stock: 0,
  total_stock_value: 0,
  average_stock_level: 0
})

const searchForm = reactive<SearchForm>({
  name: '',
  sku: '',
  category: '',
  stock_status: '',
  warehouse: '',
  batch_no: '',
  production_date_range: [],
  expiry_date_range: [],
  expiry_status: ''
})

const adjustForm = reactive<AdjustForm>({
  type: 'in',
  quantity: 1,
  reason: '',
  remark: ''
})

const adjustFormRef = ref()

// 表单验证规则
const adjustRules = {
  type: [{ required: true, message: '请选择调整类型', trigger: 'change' }],
  quantity: [{ required: true, message: '请输入调整数量', trigger: 'blur' }],
  reason: [{ required: true, message: '请选择调整原因', trigger: 'change' }]
}

// 方法
const fetchInventoryData = async () => {
  loading.value = true

  try {
    // 构建查询参数
    const params: any = {
      page: currentPage.value,
      page_size: pageSize.value
    }

    // 添加搜索条件
    if (searchForm.name) {
      params.product_name = searchForm.name
    }
    if (searchForm.sku) {
      params.product_sku = searchForm.sku
    }
    if (searchForm.category) {
      params.product_category = searchForm.category
    }
    if (searchForm.stock_status) {
      params.stock_status = searchForm.stock_status
    }
    if (searchForm.warehouse) {
      // 根据仓库名称查找仓库ID
      const warehouse = warehouses.value.find(w => w.name === searchForm.warehouse)
      if (warehouse) {
        params.warehouse_id = warehouse.id
      }
    }
    if (searchForm.batch_no) {
      params.batch_no = searchForm.batch_no
    }
    if (searchForm.production_date_range && searchForm.production_date_range.length === 2) {
      params.production_date_start = searchForm.production_date_range[0]
      params.production_date_end = searchForm.production_date_range[1]
    }
    if (searchForm.expiry_date_range && searchForm.expiry_date_range.length === 2) {
      params.expiry_date_start = searchForm.expiry_date_range[0]
      params.expiry_date_end = searchForm.expiry_date_range[1]
    }
    if (searchForm.expiry_status) {
      if (searchForm.expiry_status === 'expired') {
        params.is_expired = true
      } else if (searchForm.expiry_status === 'not_expired') {
        params.is_expired = false
      } else if (searchForm.expiry_status === 'near_expiry') {
        params.is_near_expiry = true
      }
    }

    // 调用库存API
    const response = await inventoryApi.getInventoryList(params)

    if (response) {
      inventoryList.value = response.items || []
      total.value = response.total || 0
    } else {
      inventoryList.value = []
      total.value = 0
    }

    // 获取统计数据
    const statsResponse = await inventoryApi.getInventoryStats()
    if (statsResponse) {
      inventoryStats.value = statsResponse
    }
  } catch (error: any) {
    console.error('获取库存数据失败:', error)
    ElMessage.error('获取库存数据失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    loading.value = false
  }
}

const fetchWarehouses = async () => {
  try {
    const response = await inventoryApi.getWarehouses()
    if (response) {
      warehouses.value = response
    }
  } catch (error: any) {
    console.error('获取仓库列表失败:', error)
  }
}

const refreshInventory = () => {
  fetchInventoryData()
}

const searchInventory = () => {
  // 实现搜索逻辑
  fetchInventoryData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    sku: '',
    category: '',
    stock_status: '',
    warehouse: '',
    batch_no: '',
    production_date_range: [],
    expiry_date_range: [],
    expiry_status: ''
  })
  fetchInventoryData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchInventoryData()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchInventoryData()
}

const viewInventoryDetail = (item: InventoryItem) => {
  selectedInventory.value = item
  showDetailDialog.value = true
}

const adjustStock = (item: InventoryItem) => {
  adjustingInventory.value = item
  Object.assign(adjustForm, {
    type: 'in',
    quantity: 1,
    reason: '',
    remark: ''
  })
  showAdjustDialog.value = true
}

const confirmAdjust = async () => {
  if (!adjustFormRef.value) return

  try {
    await adjustFormRef.value.validate()
    adjusting.value = true

    if (adjustingInventory.value) {
      // 准备调整数据
      const adjustmentData: InventoryAdjustment = {
        inventory_id: adjustingInventory.value.id,
        adjustment_type: adjustForm.type,
        quantity: adjustForm.quantity,
        reason: adjustForm.reason,
        remark: adjustForm.remark,
        operator: '当前用户' // 实际应用中应该从用户状态获取
      }

      // 调用API调整库存
      await inventoryApi.adjustInventory(adjustmentData)

      ElMessage.success('库存调整成功')
      showAdjustDialog.value = false

      // 重新获取数据
      await fetchInventoryData()
    }
  } catch (error: any) {
    console.error('库存调整失败:', error)
    const errorMessage = error.response?.data?.detail || error.message || '库存调整失败'
    ElMessage.error(errorMessage)
  } finally {
    adjusting.value = false
  }
}

const exportInventory = () => {
  ElMessage.success('库存数据导出成功')
}

// 辅助方法
const getImageUrl = (imageUrl?: string) => {
  if (imageUrl) return imageUrl
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmN2ZhIiBzdHJva2U9IiNlNGU3ZWQiIHN0cm9rZS13aWR0aD0iMiIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LXNpemU9IjE2IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIiBmaWxsPSIjOTA5Mzk5Ij7llYblk4Hlm77niYc8L3RleHQ+PC9zdmc+'
}

const getCategoryLabel = (category?: string) => {
  if (!category) return '未分类'
  const labelMap: Record<string, string> = {
    'electronics': '电子产品',
    'clothing': '服装鞋帽',
    'home': '家居用品',
    'beauty': '美妆护肤',
    'food': '食品饮料',
    'sports': '运动户外'
  }
  return labelMap[category] || category
}

const getStockClass = (currentStock: number, minStock: number) => {
  if (currentStock === 0) return 'out-of-stock'
  if (currentStock < minStock) return 'low-stock'
  return 'normal-stock'
}

const getStockStatusLabel = (currentStock: number, minStock: number) => {
  if (currentStock === 0) return '缺货'
  if (currentStock < minStock) return '库存不足'
  return '库存充足'
}

const getStockStatusTagType = (currentStock: number, minStock: number) => {
  if (currentStock === 0) return 'danger'
  if (currentStock < minStock) return 'warning'
  return 'success'
}

const getProgressColor = (currentStock: number, minStock: number, maxStock: number) => {
  if (currentStock === 0) return '#F56C6C'
  if (currentStock < minStock) return '#E6A23C'
  if (currentStock > maxStock * 0.8) return '#67C23A'
  return '#409EFF'
}

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const getExpiryClass = (expiryDate: string) => {
  const today = new Date()
  const expiry = new Date(expiryDate)
  const diffTime = expiry.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays < 0) {
    return 'expired-date'  // 已过期
  } else if (diffDays <= 7) {
    return 'near-expiry-date'  // 临近过期
  } else {
    return 'normal-date'  // 正常
  }
}

const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  if (tabName === 'inventory') {
    fetchInventoryData()
  }
}

onMounted(() => {
  fetchWarehouses()
  fetchInventoryData()
})
</script>

<style>
.inventory-query-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
}

.page-description {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 库存概览 */
.inventory-overview {
  margin-bottom: 12px;
}

/* 标签页样式 */
.tabs-card {
  margin-bottom: 20px;
}



.batch-management-content p {
  margin-bottom: 20px;
  font-size: 16px;
}

.overview-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.overview-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.overview-icon {
  padding: 12px;
  border-radius: 12px;
  flex-shrink: 0;
}

.overview-icon.total {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-icon.normal {
  background: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.overview-icon.warning {
  background: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.overview-icon.danger {
  background: rgba(245, 108, 108, 0.1);
  color: #F56C6C;
}

.overview-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 14px;
  color: #7f8c8d;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 12px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 库存列表卡片 */
.inventory-list-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}



/* 调整卡片头部的内边距并设置布局 */
.inventory-list-card .el-card__header {
  padding: 14px 20px !important;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 商品信息 */
.product-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
  border: 1px solid #e4e7ed;
}

.product-details {
  flex: 1;
}

.product-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.product-sku {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.product-category {
  font-size: 12px;
  color: #606266;
}

/* 库存状态样式 */
.normal-stock {
  color: #67C23A;
  font-weight: 600;
}

.low-stock {
  color: #E6A23C;
  font-weight: 600;
}

.out-of-stock {
  color: #F56C6C;
  font-weight: 600;
}

.min-stock {
  color: #909399;
}

.max-stock {
  color: #606266;
}

.reserved-stock {
  color: #E6A23C;
}

.available-stock {
  color: #67C23A;
  font-weight: 600;
}

/* 批次相关样式 */
.batch-no {
  color: #409EFF;
  font-weight: 500;
}

.no-batch {
  color: #C0C4CC;
  font-style: italic;
}

.production-date {
  color: #606266;
}

.no-date {
  color: #C0C4CC;
  font-style: italic;
}

.normal-date {
  color: #67C23A;
}

.near-expiry-date {
  color: #E6A23C;
  font-weight: 600;
}

.expired-date {
  color: #F56C6C;
  font-weight: 600;
}

/* 卡片视图 */
.card-view {
  margin-top: 20px;
}

.inventory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.inventory-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.inventory-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
  position: relative;
  height: 150px;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-status {
  position: absolute;
  top: 10px;
  right: 10px;
}

.card-content {
  padding: 16px;
}

.card-title {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.card-sku,
.card-warehouse {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #909399;
}

.batch-info {
  margin: 8px 0 12px 0;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #409EFF;
}

.batch-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.batch-row:last-child {
  margin-bottom: 0;
}

.batch-row .label {
  color: #606266;
  font-weight: 500;
}

.stock-info {
  margin: 12px 0;
}

.stock-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 14px;
}

.stock-row .label {
  color: #606266;
}

.stock-progress {
  margin-top: 12px;
}

.progress-label {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.card-actions {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 8px;
}

/* 分页 */
.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 库存详情 */
.inventory-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.detail-image {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  object-fit: cover;
  border: 1px solid #e4e7ed;
}

.detail-info h3 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 20px;
}

.detail-info p {
  margin: 0 0 8px 0;
  color: #606266;
}

.detail-content {
  margin-top: 20px;
}

.stock-chart {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.stock-chart h4 {
  margin: 0 0 16px 0;
  color: #2c3e50;
}

.chart-placeholder {
  color: #909399;
}

.chart-placeholder p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 20px;
}

/* 按钮样式 */
.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .inventory-overview .el-col {
    margin-bottom: 16px;
  }

  .inventory-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .inventory-query-view {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    justify-content: flex-start;
  }

  .inventory-grid {
    grid-template-columns: 1fr;
  }

  .product-info {
    flex-direction: column;
    text-align: center;
  }

  .detail-header {
    flex-direction: column;
    text-align: center;
  }
}
</style>
