# 电子商务决策系统

一个前后端分离的电子商务决策系统，提供选品决策和物流配送功能。

## 项目结构

```
电子商务决策系统/
├── frontend/          # Vue3 + TypeScript + Element Plus 前端项目
├── backend/           # Python FastAPI 后端项目
├── docs/             # 项目文档
└── README.md         # 项目说明
```

## 技术栈

### 前端
- Vue 3
- TypeScript
- Element Plus (饿了么UI组件库)
- Vite (构建工具)
- Axios (HTTP客户端)

### 后端
- Python 3.8+
- FastAPI (Web框架)
- SQLAlchemy (ORM)
- Pydantic (数据验证)
- Uvicorn (ASGI服务器)

## 主要功能

### 1. 选品决策功能
- 商品数据分析
- 市场趋势分析
- 竞品对比
- 销售预测
- 智能推荐算法

### 2. 物流配送功能
- 配送路线规划
- 配送成本计算
- 配送时效预估
- 仓库管理
- 运力调度

## 快速开始

### 后端启动
```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload
```

### 前端启动
```bash
cd frontend
npm install
npm run dev
```

## 开发计划

- [x] 项目结构规划与初始化
- [ ] 后端Python API开发
- [ ] 前端Vue3项目初始化
- [ ] 选品决策功能开发
- [ ] 物流配送功能开发
- [ ] 系统集成与测试

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
