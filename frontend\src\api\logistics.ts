import api from './index'

export interface LogisticsRoute {
  id: number
  route_name: string
  origin: string
  destination: string
  distance_km: number
  estimated_time_hours: number
  base_cost: number
  cost_per_km: number
  fuel_cost?: number
  toll_cost?: number
  is_active: string
  route_details?: any
  created_at: string
  updated_at?: string
}

export interface DeliveryOrder {
  id: number
  order_number: string
  pickup_address: string
  delivery_address: string
  route_id: number
  weight_kg: number
  volume_m3?: number
  package_count: number
  calculated_cost?: number
  estimated_delivery_time?: string
  actual_delivery_time?: string
  status: string
  special_requirements?: string
  delivery_notes?: string
  created_at: string
  updated_at?: string
}

export interface CostCalculationRequest {
  pickup_address: string
  delivery_address: string
  weight_kg: number
  volume_m3?: number
  package_count?: number
  delivery_type?: string
}

export interface CostCalculationResponse {
  total_cost: number
  base_cost: number
  distance_cost: number
  weight_cost: number
  additional_fees: Record<string, number>
  estimated_delivery_time: string
  recommended_route: LogisticsRoute
}

export interface RouteOptimizationRequest {
  pickup_points: string[]
  delivery_points: string[]
  vehicle_capacity: number
  optimization_type?: string
}

export interface RouteOptimizationResponse {
  optimized_routes: any[]
  total_distance: number
  total_cost: number
  total_time: number
  efficiency_score: number
}

// 物流API
export const logisticsApi = {
  // 获取路线列表
  getRoutes: (params?: { skip?: number; limit?: number; origin?: string; destination?: string }) => {
    return api.get<LogisticsRoute[]>('/api/logistics/routes', { params })
  },

  // 创建路线
  createRoute: (data: Omit<LogisticsRoute, 'id' | 'created_at' | 'updated_at'>) => {
    return api.post<LogisticsRoute>('/api/logistics/routes', data)
  },

  // 获取单个路线
  getRoute: (id: number) => {
    return api.get<LogisticsRoute>(`/api/logistics/routes/${id}`)
  },

  // 计算配送成本
  calculateCost: (data: CostCalculationRequest) => {
    return api.post<CostCalculationResponse>('/api/logistics/calculate-cost', data)
  },

  // 路线优化
  optimizeRoute: (data: RouteOptimizationRequest) => {
    return api.post<RouteOptimizationResponse>('/api/logistics/optimize-route', data)
  },

  // 获取配送订单列表
  getDeliveryOrders: (params?: { skip?: number; limit?: number; status?: string }) => {
    return api.get<DeliveryOrder[]>('/api/logistics/orders', { params })
  },

  // 创建配送订单
  createDeliveryOrder: (data: Omit<DeliveryOrder, 'id' | 'order_number' | 'created_at' | 'updated_at'>) => {
    return api.post<DeliveryOrder>('/api/logistics/orders', data)
  },

  // 获取配送订单详情
  getDeliveryOrder: (id: number) => {
    return api.get<DeliveryOrder>(`/api/logistics/orders/${id}`)
  },

  // 更新订单状态
  updateOrderStatus: (id: number, status: string, notes?: string) => {
    return api.put(`/api/logistics/orders/${id}/status`, { status, notes })
  },

  // 获取物流性能分析
  getPerformanceAnalytics: (params?: { time_period?: string }) => {
    return api.get('/api/logistics/analytics/performance', { params })
  },

  // 预估配送时间
  estimateDeliveryTime: (params: { pickup_address: string; delivery_address: string; delivery_type?: string }) => {
    return api.get('/api/logistics/estimate-time', { params })
  }
}
