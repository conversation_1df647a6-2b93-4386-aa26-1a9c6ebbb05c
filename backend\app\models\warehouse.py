from sqlalchemy import Column, Integer, String, Float, DateTime, Text, JSON, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class Warehouse(Base):
    """仓库模型"""
    __tablename__ = "warehouses"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, comment="仓库名称")
    address = Column(String(500), nullable=False, comment="仓库地址")
    
    # 容量信息
    total_capacity_m3 = Column(Float, nullable=False, comment="总容量(立方米)")
    used_capacity_m3 = Column(Float, default=0, comment="已用容量(立方米)")
    
    # 位置信息
    latitude = Column(Float, nullable=True, comment="纬度")
    longitude = Column(Float, nullable=True, comment="经度")
    
    # 运营信息
    operating_hours = Column(JSON, nullable=True, comment="营业时间")
    contact_info = Column(JSON, nullable=True, comment="联系信息")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否启用")
    status = Column(String(20), default="active", comment="仓库状态")  # active, inactive, maintenance
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")

    # 关联关系
    inventory_items = relationship("Inventory", back_populates="warehouse")

    # 注释掉调拨单关系，因为数据库表不存在
    # TODO: 创建inventory_transfers表后取消注释
    # # 调出的调拨单
    # outbound_transfers = relationship(
    #     "InventoryTransfer",
    #     foreign_keys="InventoryTransfer.from_warehouse_id",
    #     back_populates="from_warehouse"
    # )
    #
    # # 调入的调拨单
    # inbound_transfers = relationship(
    #     "InventoryTransfer",
    #     foreign_keys="InventoryTransfer.to_warehouse_id",
    #     back_populates="to_warehouse"
    # )

    def __repr__(self):
        return f"<Warehouse(id={self.id}, name='{self.name}', status='{self.status}')>"
    
    @property
    def available_capacity_m3(self) -> float:
        """可用容量"""
        return self.total_capacity_m3 - self.used_capacity_m3
    
    @property
    def utilization_rate(self) -> float:
        """利用率百分比"""
        if self.total_capacity_m3 <= 0:
            return 0.0
        return (self.used_capacity_m3 / self.total_capacity_m3) * 100
    
    def can_accommodate(self, volume: float) -> bool:
        """检查是否能容纳指定体积的货物"""
        return self.available_capacity_m3 >= volume
    
    def update_capacity(self, volume_change: float) -> bool:
        """更新已用容量"""
        new_used_capacity = self.used_capacity_m3 + volume_change
        if new_used_capacity < 0 or new_used_capacity > self.total_capacity_m3:
            return False
        self.used_capacity_m3 = new_used_capacity
        return True
