"""
供应商API路由
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import math
from app.core.database import get_db
from app.schemas.supplier import (
    Supplier, SupplierCreate, SupplierUpdate, SupplierListResponse,
    SupplierQuery, SupplierStatus, CreditRating
)
from app.services.supplier_service import SupplierService

router = APIRouter()


@router.get("/", response_model=SupplierListResponse)
async def get_suppliers(
    name: Optional[str] = Query(None, description="供应商名称"),
    code: Optional[str] = Query(None, description="供应商编码"),
    category: Optional[str] = Query(None, description="供应商类别"),
    status: Optional[SupplierStatus] = Query(None, description="供应商状态"),
    credit_rating: Optional[CreditRating] = Query(None, description="信用等级"),
    contact_person: Optional[str] = Query(None, description="联系人"),
    address: Optional[str] = Query(None, description="地址/地区"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取供应商列表"""
    service = SupplierService(db)
    
    # 构建查询参数
    query_params = SupplierQuery(
        name=name,
        code=code,
        category=category,
        status=status,
        credit_rating=credit_rating,
        contact_person=contact_person,
        address=address,
        page=page,
        page_size=page_size
    )
    
    suppliers, total = service.get_suppliers_paginated(query_params)
    total_pages = math.ceil(total / page_size)
    
    return SupplierListResponse(
        items=suppliers,
        total=total,
        page=page,
        page_size=page_size,
        total_pages=total_pages
    )


@router.get("/{supplier_id}", response_model=Supplier)
async def get_supplier(supplier_id: int, db: Session = Depends(get_db)):
    """获取单个供应商"""
    service = SupplierService(db)
    supplier = service.get_supplier(supplier_id)
    if not supplier:
        raise HTTPException(status_code=404, detail="供应商不存在")
    return supplier


@router.post("/", response_model=Supplier)
async def create_supplier(supplier_data: SupplierCreate, db: Session = Depends(get_db)):
    """创建供应商"""
    service = SupplierService(db)
    try:
        return service.create_supplier(supplier_data)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{supplier_id}", response_model=Supplier)
async def update_supplier(
    supplier_id: int, 
    supplier_data: SupplierUpdate, 
    db: Session = Depends(get_db)
):
    """更新供应商"""
    service = SupplierService(db)
    try:
        supplier = service.update_supplier(supplier_id, supplier_data)
        if not supplier:
            raise HTTPException(status_code=404, detail="供应商不存在")
        return supplier
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/{supplier_id}")
async def delete_supplier(supplier_id: int, db: Session = Depends(get_db)):
    """删除供应商"""
    service = SupplierService(db)
    success = service.delete_supplier(supplier_id)
    if not success:
        raise HTTPException(status_code=404, detail="供应商不存在")
    return {"message": "供应商删除成功"}


@router.get("/categories/list", response_model=List[str])
async def get_supplier_categories(db: Session = Depends(get_db)):
    """获取所有供应商类别"""
    service = SupplierService(db)
    return service.get_supplier_categories()


@router.get("/active/list", response_model=List[Supplier])
async def get_active_suppliers(db: Session = Depends(get_db)):
    """获取所有活跃的供应商"""
    service = SupplierService(db)
    return service.get_active_suppliers()


@router.get("/search/{keyword}", response_model=List[Supplier])
async def search_suppliers(keyword: str, db: Session = Depends(get_db)):
    """搜索供应商"""
    service = SupplierService(db)
    return service.search_suppliers(keyword)


@router.put("/{supplier_id}/balance", response_model=Supplier)
async def update_supplier_balance(
    supplier_id: int, 
    amount: float, 
    db: Session = Depends(get_db)
):
    """更新供应商余额"""
    service = SupplierService(db)
    supplier = service.update_supplier_balance(supplier_id, amount)
    if not supplier:
        raise HTTPException(status_code=404, detail="供应商不存在")
    return supplier


@router.get("/credit-rating/{rating}", response_model=List[Supplier])
async def get_suppliers_by_credit_rating(rating: CreditRating, db: Session = Depends(get_db)):
    """根据信用等级获取供应商"""
    service = SupplierService(db)
    return service.get_suppliers_by_credit_rating(rating)
