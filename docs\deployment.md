# 电子商务决策系统部署指南

## 系统概述

电子商务决策系统是一个前后端分离的Web应用，提供选品决策和物流配送功能。

### 技术栈
- **前端**: Vue 3 + TypeScript + Element Plus + Vite
- **后端**: Python + FastAPI + SQLAlchemy + SQLite
- **开发工具**: Node.js + npm, Python 3.8+

## 环境要求

### 后端环境
- Python 3.8 或更高版本
- pip 包管理器

### 前端环境
- Node.js 16 或更高版本
- npm 或 yarn 包管理器

## 安装部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd 电子商务决策系统
```

### 2. 后端部署

#### 2.1 进入后端目录
```bash
cd backend
```

#### 2.2 安装Python依赖
```bash
pip install -r requirements.txt
pip install pydantic-settings
```

#### 2.3 初始化数据库
```bash
python init_db.py
```

#### 2.4 启动后端服务
```bash
python main.py
```

后端服务将在 `http://localhost:8000` 启动

### 3. 前端部署

#### 3.1 进入前端目录
```bash
cd frontend
```

#### 3.2 安装Node.js依赖
```bash
npm install
```

#### 3.3 启动开发服务器
```bash
npm run dev
```

前端应用将在 `http://localhost:5173` 启动

## 生产环境部署

### 后端生产部署

#### 使用Gunicorn部署
```bash
pip install gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

#### 使用Docker部署
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 前端生产部署

#### 构建生产版本
```bash
npm run build
```

#### 使用Nginx部署
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        root /path/to/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 配置说明

### 后端配置
编辑 `backend/app/core/config.py` 文件：

```python
class Settings(BaseSettings):
    app_name: str = "电子商务决策系统"
    debug: bool = False  # 生产环境设为False
    database_url: str = "sqlite:///./ecommerce_decision.db"
    secret_key: str = "your-production-secret-key"
    
    class Config:
        env_file = ".env"
```

### 前端配置
编辑 `frontend/src/api/index.ts` 文件：

```typescript
const api = axios.create({
  baseURL: 'https://your-api-domain.com',  // 生产环境API地址
  timeout: 10000
})
```

## API文档

后端启动后，可以访问以下地址查看API文档：
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## 主要功能

### 选品决策功能
- 商品管理：增删改查商品信息
- 商品分析：市场趋势、竞争分析、销售表现分析
- 智能推荐：基于多维度评分的商品推荐

### 物流配送功能
- 配送路线管理：路线的增删改查和优化
- 成本计算：智能计算配送成本和时效
- 订单管理：配送订单的创建和状态跟踪

## 故障排除

### 常见问题

#### 1. 后端启动失败
- 检查Python版本是否符合要求
- 确认所有依赖包已正确安装
- 检查端口8000是否被占用

#### 2. 前端启动失败
- 检查Node.js版本是否符合要求
- 删除node_modules文件夹，重新运行npm install
- 检查端口5173是否被占用

#### 3. 前后端连接失败
- 确认后端服务正常运行
- 检查前端API配置是否正确
- 确认CORS配置是否正确

### 日志查看
- 后端日志：控制台输出
- 前端日志：浏览器开发者工具Console

## 性能优化

### 后端优化
- 使用数据库连接池
- 添加Redis缓存
- 使用异步处理长时间任务

### 前端优化
- 启用代码分割
- 使用CDN加速静态资源
- 启用Gzip压缩

## 安全建议

1. 更改默认的secret_key
2. 使用HTTPS协议
3. 定期更新依赖包
4. 实施API访问限制
5. 添加用户认证和授权

## 监控和维护

### 健康检查
- 后端健康检查：`GET /health`
- 前端可用性：访问首页

### 备份策略
- 定期备份SQLite数据库文件
- 备份配置文件和代码

### 更新流程
1. 备份当前版本
2. 更新代码
3. 安装新依赖
4. 重启服务
5. 验证功能正常

## 联系支持

如遇到问题，请检查：
1. 系统日志
2. 网络连接
3. 配置文件
4. 依赖版本

技术支持：请提供详细的错误信息和系统环境信息。
