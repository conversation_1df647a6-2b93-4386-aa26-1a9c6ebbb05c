#!/usr/bin/env python3
"""
测试库存API
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

import asyncio
from fastapi.testclient import TestClient
from backend.main import app

def test_inventory_api():
    """测试库存API"""
    client = TestClient(app)
    
    try:
        # 测试获取库存列表
        print("测试获取库存列表...")
        response = client.get("/api/inventory/")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
            
            if isinstance(data, dict) and 'items' in data:
                print(f"库存记录数量: {len(data['items'])}")
                print(f"总数: {data.get('total', 'N/A')}")
                
                if data['items']:
                    print(f"第一条记录: {data['items'][0]}")
            else:
                print(f"返回数据: {data}")
        else:
            print(f"错误响应: {response.text}")
            
        # 测试获取库存统计
        print("\n测试获取库存统计...")
        response = client.get("/api/inventory/stats")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"统计数据: {data}")
        else:
            print(f"错误响应: {response.text}")
            
        # 测试获取仓库列表
        print("\n测试获取仓库列表...")
        response = client.get("/api/inventory/warehouses")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"仓库数量: {len(data) if isinstance(data, list) else 'N/A'}")
            if isinstance(data, list) and data:
                print(f"第一个仓库: {data[0]}")
        else:
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f'测试API时出错: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_inventory_api()
