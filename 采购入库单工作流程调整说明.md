# 采购入库单工作流程调整说明

## 调整目标

将采购入库单从直接入库模式改为提交-审核工作流程模式，删除入库功能，添加提交和审核功能。

## 主要更改

### 1. 状态流程调整

#### 原有状态流程
```
pending (待入库) -> partial (部分入库) -> completed (已完成)
                                    -> cancelled (已取消)
```

#### 新的状态流程
```
draft (草稿) -> submitted (已提交) -> approved (已审核)
                                -> rejected (已拒绝) -> draft (重新编辑)
            -> cancelled (已取消)
```

#### 状态说明
- **draft**: 草稿状态，可以编辑和提交
- **submitted**: 已提交状态，等待审核，可以撤回
- **approved**: 已审核通过，流程完成
- **rejected**: 审核拒绝，可以重新编辑
- **cancelled**: 已取消，终止状态

### 2. 数据库模型调整

**文件**: `backend/app/models/purchase.py`

#### 2.1 状态枚举更新
```python
# 修改前
status = Column(String(20), default="pending", comment="状态: pending, partial, completed, cancelled")

# 修改后  
status = Column(String(20), default="draft", comment="状态: draft, submitted, approved, rejected, cancelled")
```

#### 2.2 审批流程字段
已在之前版本中添加：
- `submitted_by` - 提交人
- `submitted_at` - 提交时间
- `approved_by` - 审批人
- `approved_at` - 审批时间

### 3. Schema定义调整

**文件**: `backend/app/schemas/purchase.py`

```python
class PurchaseReceiptStatus(str, Enum):
    """采购入库单状态"""
    DRAFT = "draft"           # 草稿
    SUBMITTED = "submitted"   # 已提交
    APPROVED = "approved"     # 已审核
    REJECTED = "rejected"     # 已拒绝
    CANCELLED = "cancelled"   # 已取消
```

### 4. 服务逻辑调整

**文件**: `backend/app/services/purchase_receipt_service.py`

#### 4.1 新增提交功能
```python
def submit_purchase_receipt(self, receipt_id: int, submitted_by: str) -> Optional[PurchaseReceipt]:
    """提交采购入库单"""
    # 验证状态和数据完整性
    # 更新状态为submitted，记录提交人和时间
```

#### 4.2 新增审核功能
```python
def approve_purchase_receipt(self, receipt_id: int, approved_by: str, approved: bool = True, remark: str = None) -> Optional[PurchaseReceipt]:
    """审核采购入库单"""
    # 审核通过：状态改为approved
    # 审核拒绝：状态改为rejected
```

#### 4.3 新增撤回功能
```python
def recall_purchase_receipt(self, receipt_id: int) -> Optional[PurchaseReceipt]:
    """撤回采购入库单（从已提交状态撤回到草稿状态）"""
    # 清除提交信息，状态改为draft
```

#### 4.4 统计数据调整
```python
# 修改前
pending_receipts, partial_receipts, completed_receipts

# 修改后
draft_receipts, submitted_receipts, approved_receipts, rejected_receipts
```

### 5. API路由调整

**文件**: `backend/app/api/purchase_receipt.py`

#### 5.1 删除入库确认端点
```python
# 删除
@router.put("/{receipt_id}/confirm")
async def confirm_purchase_receipt(...)
```

#### 5.2 新增工作流程端点
```python
# 提交
@router.put("/{receipt_id}/submit")
async def submit_purchase_receipt(...)

# 审核
@router.put("/{receipt_id}/approve") 
async def approve_purchase_receipt(...)

# 撤回
@router.put("/{receipt_id}/recall")
async def recall_purchase_receipt(...)
```

### 6. 前端界面调整

**文件**: `frontend/src/views/PurchaseReceiptView.vue`

#### 6.1 状态显示调整
```typescript
const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    'draft': '草稿',
    'submitted': '已提交', 
    'approved': '已审核',
    'rejected': '已拒绝',
    'cancelled': '已取消'
  }
  return labelMap[status] || status
}
```

#### 6.2 操作按钮调整
- **草稿状态**: 编辑、提交
- **已提交状态**: 撤回、审核通过、审核拒绝
- **已拒绝状态**: 重新编辑
- **已审核/已取消**: 只能查看

#### 6.3 统计卡片调整
```vue
<!-- 修改前 -->
<div class="overview-label">待入库</div>
<div class="overview-label">部分入库</div>
<div class="overview-label">已完成</div>

<!-- 修改后 -->
<div class="overview-label">草稿</div>
<div class="overview-label">已提交</div>
<div class="overview-label">已审核</div>
```

#### 6.4 删除入库处理功能
- 删除入库处理对话框
- 删除相关的入库确认函数
- 删除入库进度计算逻辑

### 7. API接口调整

**文件**: `frontend/src/api/purchase-receipt.ts`

#### 7.1 状态枚举更新
```typescript
export enum PurchaseReceiptStatus {
  DRAFT = 'draft',         // 草稿
  SUBMITTED = 'submitted', // 已提交
  APPROVED = 'approved',   // 已审核
  REJECTED = 'rejected',   // 已拒绝
  CANCELLED = 'cancelled'  // 已取消
}
```

#### 7.2 新增API方法
```typescript
// 提交
submitPurchaseReceipt: (id: number, submittedBy: string) => Promise<any>

// 审核
approvePurchaseReceipt: (id: number, approvedBy: string, approved: boolean, remark?: string) => Promise<any>

// 撤回
recallPurchaseReceipt: (id: number) => Promise<any>
```

## 数据库迁移

### 迁移脚本
**文件**: `backend/migrate_receipt_status_v3.py`

### 迁移内容
1. **状态值映射**:
   - `pending` → `draft`
   - `partial` → `submitted`
   - `completed` → `approved`
   - `cancelled` → `cancelled` (保持不变)

2. **默认状态更新**: 将表的默认状态改为 `draft`

### 迁移结果
```
✅ 更新 pending -> draft: 3 条记录
✅ 更新 partial -> submitted: 2 条记录  
✅ 更新 completed -> approved: 2 条记录
✅ 默认状态已更新为 draft
```

## 业务流程变更

### 原有流程
1. 创建入库单 → 待入库状态
2. 执行入库操作 → 部分入库/已完成状态
3. 确认入库完成

### 新的流程
1. **创建**: 用户创建入库单 → 草稿状态
2. **提交**: 用户完善信息后提交 → 已提交状态
3. **审核**: 管理员审核
   - 通过 → 已审核状态 (流程完成)
   - 拒绝 → 已拒绝状态 (可重新编辑)
4. **撤回**: 已提交状态可以撤回到草稿状态

### 权限控制
- **普通用户**: 可以创建、编辑、提交、撤回入库单
- **管理员**: 可以审核入库单 (通过/拒绝)
- **系统管理员**: 可以取消任何状态的入库单

## 影响分析

### 1. 正面影响
- **规范流程**: 建立了标准的提交-审核工作流程
- **提升控制**: 增强了对入库单的审核控制
- **责任追踪**: 记录了提交人和审核人信息
- **状态清晰**: 状态含义更加明确

### 2. 功能变更
- **删除**: 直接入库功能
- **新增**: 提交、审核、撤回功能
- **保留**: 创建、编辑、查看、取消功能

### 3. 用户体验
- **简化操作**: 去掉了复杂的入库数量处理
- **明确流程**: 用户清楚知道每个状态的含义和可执行操作
- **提升效率**: 批量审核功能提升处理效率

## 测试建议

### 1. 状态流转测试
- 测试草稿 → 提交 → 审核通过流程
- 测试草稿 → 提交 → 审核拒绝 → 重新编辑流程
- 测试提交 → 撤回 → 重新提交流程

### 2. 权限测试
- 测试不同角色的操作权限
- 测试状态限制逻辑

### 3. 数据完整性测试
- 测试提交时的数据验证
- 测试审核信息的记录

## 部署步骤

1. **备份数据库**
2. **部署后端代码**
3. **执行状态迁移脚本**
4. **部署前端代码**
5. **验证功能正常**
6. **培训用户新流程**

## 文件清单

### 后端文件
- `backend/app/models/purchase.py` - 数据模型
- `backend/app/schemas/purchase.py` - API Schema
- `backend/app/services/purchase_receipt_service.py` - 业务逻辑
- `backend/app/api/purchase_receipt.py` - API路由
- `backend/migrate_receipt_status_v3.py` - 状态迁移脚本

### 前端文件
- `frontend/src/api/purchase-receipt.ts` - API接口定义
- `frontend/src/views/PurchaseReceiptView.vue` - 主界面组件

### 文档文件
- `采购入库单工作流程调整说明.md` - 本文档

## 总结

本次调整成功将采购入库单从直接入库模式转换为标准的提交-审核工作流程模式：

1. ✅ **删除入库功能**: 移除了复杂的入库数量处理逻辑
2. ✅ **添加提交功能**: 用户可以提交入库单等待审核
3. ✅ **添加审核功能**: 管理员可以审核通过或拒绝入库单
4. ✅ **状态流程优化**: 建立了清晰的状态流转机制
5. ✅ **数据库平滑迁移**: 现有数据成功转换为新状态
6. ✅ **前后端同步更新**: 界面和逻辑完全适配新流程

系统现在具备了更规范的工作流程和更强的审核控制能力，为企业的采购管理提供了更好的支持。
