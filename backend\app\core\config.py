from pydantic_settings import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    # 应用配置
    app_name: str = "电子商务决策系统"
    debug: bool = True

    # 数据库配置 - 使用MySQL
    database_url: str = os.getenv("DATABASE_URL", "mysql+pymysql://root:password@localhost:3306/ecommerce_decision?charset=utf8mb4")

    # JWT配置
    secret_key: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30

    # API配置
    api_v1_str: str = "/api"

    class Config:
        env_file = ".env"
        extra = "ignore"  # 忽略额外的环境变量

settings = Settings()
