<template>
  <div class="cost-calculator-view">
    <div class="page-header">
      <h2>配送成本计算器</h2>
    </div>

    <el-row :gutter="20">
      <!-- 计算表单 -->
      <el-col :span="12">
        <el-card class="calculator-form">
          <template #header>
            <div class="card-header">
              <el-icon><Calculator /></el-icon>
              <span>配送信息</span>
            </div>
          </template>
          
          <el-form :model="calculationForm" label-width="120px">
            <el-form-item label="取货地址" required>
              <el-input 
                v-model="calculationForm.pickup_address" 
                placeholder="请输入取货地址"
              />
            </el-form-item>
            
            <el-form-item label="配送地址" required>
              <el-input 
                v-model="calculationForm.delivery_address" 
                placeholder="请输入配送地址"
              />
            </el-form-item>
            
            <el-form-item label="货物重量" required>
              <el-input-number 
                v-model="calculationForm.weight_kg" 
                :min="0.1" 
                :step="0.1"
                :precision="1"
                placeholder="公斤"
              />
              <span class="unit">公斤</span>
            </el-form-item>
            
            <el-form-item label="货物体积">
              <el-input-number 
                v-model="calculationForm.volume_m3" 
                :min="0" 
                :step="0.1"
                :precision="2"
                placeholder="立方米"
              />
              <span class="unit">立方米</span>
            </el-form-item>
            
            <el-form-item label="包裹数量">
              <el-input-number 
                v-model="calculationForm.package_count" 
                :min="1" 
                :step="1"
              />
              <span class="unit">件</span>
            </el-form-item>
            
            <el-form-item label="配送类型">
              <el-radio-group v-model="calculationForm.delivery_type">
                <el-radio value="standard">标准配送</el-radio>
                <el-radio value="express">加急配送</el-radio>
                <el-radio value="same_day">当日达</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item>
              <el-button 
                type="primary" 
                @click="calculateCost"
                :loading="loading"
                :disabled="!isFormValid"
              >
                计算成本
              </el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <!-- 计算结果 -->
      <el-col :span="12">
        <el-card class="calculation-result" v-if="calculationResult">
          <template #header>
            <div class="card-header">
              <el-icon><Money /></el-icon>
              <span>计算结果</span>
            </div>
          </template>
          
          <div class="result-summary">
            <div class="total-cost">
              <span class="label">总费用:</span>
              <span class="value">¥{{ calculationResult.total_cost }}</span>
            </div>
            
            <div class="delivery-time">
              <span class="label">预计送达:</span>
              <span class="value">{{ formatDeliveryTime(calculationResult.estimated_delivery_time) }}</span>
            </div>
          </div>
          
          <el-divider />
          
          <div class="cost-breakdown">
            <h4>费用明细</h4>
            
            <div class="cost-item">
              <span class="item-label">基础费用:</span>
              <span class="item-value">¥{{ calculationResult.base_cost }}</span>
            </div>
            
            <div class="cost-item">
              <span class="item-label">距离费用:</span>
              <span class="item-value">¥{{ calculationResult.distance_cost }}</span>
            </div>
            
            <div class="cost-item">
              <span class="item-label">重量费用:</span>
              <span class="item-value">¥{{ calculationResult.weight_cost }}</span>
            </div>
            
            <div 
              v-for="(fee, name) in calculationResult.additional_fees" 
              :key="name"
              class="cost-item"
            >
              <span class="item-label">{{ name }}:</span>
              <span class="item-value">¥{{ fee }}</span>
            </div>
          </div>
          
          <el-divider />
          
          <div class="recommended-route" v-if="calculationResult.recommended_route">
            <h4>推荐路线</h4>
            <div class="route-info">
              <p><strong>路线名称:</strong> {{ calculationResult.recommended_route.route_name }}</p>
              <p><strong>起点:</strong> {{ calculationResult.recommended_route.origin }}</p>
              <p><strong>终点:</strong> {{ calculationResult.recommended_route.destination }}</p>
              <p><strong>距离:</strong> {{ calculationResult.recommended_route.distance_km }} 公里</p>
              <p><strong>预计时间:</strong> {{ calculationResult.recommended_route.estimated_time_hours }} 小时</p>
            </div>
          </div>
          
          <div class="result-actions">
            <el-button type="primary" @click="createOrder">创建订单</el-button>
            <el-button @click="saveCalculation">保存计算</el-button>
          </div>
        </el-card>
        
        <!-- 历史计算记录 -->
        <el-card class="calculation-history" v-if="calculationHistory.length">
          <template #header>
            <div class="card-header">
              <el-icon><Clock /></el-icon>
              <span>历史记录</span>
            </div>
          </template>
          
          <div class="history-list">
            <div 
              v-for="(record, index) in calculationHistory" 
              :key="index"
              class="history-item"
              @click="loadHistoryRecord(record)"
            >
              <div class="history-route">
                {{ record.pickup_address }} → {{ record.delivery_address }}
              </div>
              <div class="history-cost">¥{{ record.total_cost }}</div>
              <div class="history-time">{{ formatDate(record.timestamp) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 批量计算 -->
    <el-card class="batch-calculator">
      <template #header>
        <div class="card-header">
          <el-icon><Grid /></el-icon>
          <span>批量计算</span>
        </div>
      </template>
      
      <p>上传CSV文件进行批量配送成本计算</p>
      
      <el-upload
        class="upload-demo"
        drag
        action="#"
        :auto-upload="false"
        :on-change="handleFileUpload"
        accept=".csv"
      >
        <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
        <div class="el-upload__text">
          将CSV文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            CSV格式: 取货地址,配送地址,重量(kg),体积(m³),包裹数量,配送类型
          </div>
        </template>
      </el-upload>
      
      <el-button 
        type="primary" 
        @click="processBatchCalculation"
        :disabled="!batchFile"
        :loading="batchLoading"
      >
        开始批量计算
      </el-button>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useLogisticsStore } from '../stores/logistics'
import { ElMessage } from 'element-plus'

const logisticsStore = useLogisticsStore()

const calculationForm = ref({
  pickup_address: '',
  delivery_address: '',
  weight_kg: 1,
  volume_m3: 0,
  package_count: 1,
  delivery_type: 'standard'
})

const calculationResult = ref(null)
const calculationHistory = ref([])
const loading = ref(false)
const batchFile = ref(null)
const batchLoading = ref(false)

const isFormValid = computed(() => {
  return calculationForm.value.pickup_address && 
         calculationForm.value.delivery_address && 
         calculationForm.value.weight_kg > 0
})

const calculateCost = async () => {
  if (!isFormValid.value) {
    ElMessage.warning('请填写完整的配送信息')
    return
  }
  
  loading.value = true
  try {
    const result = await logisticsStore.calculateCost(calculationForm.value)
    
    if (result) {
      calculationResult.value = result
      
      // 添加到历史记录
      const historyRecord = {
        ...calculationForm.value,
        total_cost: result.total_cost,
        timestamp: new Date().toISOString()
      }
      calculationHistory.value.unshift(historyRecord)
      
      // 只保留最近10条记录
      if (calculationHistory.value.length > 10) {
        calculationHistory.value = calculationHistory.value.slice(0, 10)
      }
      
      ElMessage.success('计算完成')
    }
  } catch (error) {
    ElMessage.error('计算失败')
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  calculationForm.value = {
    pickup_address: '',
    delivery_address: '',
    weight_kg: 1,
    volume_m3: 0,
    package_count: 1,
    delivery_type: 'standard'
  }
  calculationResult.value = null
}

const createOrder = async () => {
  if (!calculationResult.value) return
  
  try {
    const orderData = {
      ...calculationForm.value,
      calculated_cost: calculationResult.value.total_cost,
      estimated_delivery_time: calculationResult.value.estimated_delivery_time
    }
    
    const order = await logisticsStore.createDeliveryOrder(orderData)
    
    if (order) {
      ElMessage.success('订单创建成功')
      // 可以跳转到订单详情页
    }
  } catch (error) {
    ElMessage.error('创建订单失败')
  }
}

const saveCalculation = () => {
  // 保存计算结果到本地存储或服务器
  ElMessage.success('计算结果已保存')
}

const loadHistoryRecord = (record) => {
  calculationForm.value = {
    pickup_address: record.pickup_address,
    delivery_address: record.delivery_address,
    weight_kg: record.weight_kg,
    volume_m3: record.volume_m3,
    package_count: record.package_count,
    delivery_type: record.delivery_type
  }
}

const handleFileUpload = (file) => {
  batchFile.value = file.raw
}

const processBatchCalculation = () => {
  if (!batchFile.value) {
    ElMessage.warning('请先上传CSV文件')
    return
  }
  
  batchLoading.value = true
  
  // 模拟批量处理
  setTimeout(() => {
    batchLoading.value = false
    ElMessage.success('批量计算完成')
  }, 3000)
}

const formatDeliveryTime = (timeString) => {
  if (!timeString) return ''
  return new Date(timeString).toLocaleString('zh-CN')
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style scoped>
.cost-calculator-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.calculator-form {
  margin-bottom: 20px;
}

.unit {
  margin-left: 8px;
  color: #909399;
  font-size: 14px;
}

.calculation-result {
  margin-bottom: 20px;
}

.result-summary {
  text-align: center;
  margin-bottom: 20px;
}

.total-cost {
  margin-bottom: 10px;
}

.total-cost .label {
  font-size: 16px;
  color: #606266;
}

.total-cost .value {
  font-size: 24px;
  font-weight: bold;
  color: #E6A23C;
  margin-left: 10px;
}

.delivery-time .label {
  color: #606266;
}

.delivery-time .value {
  color: #409EFF;
  margin-left: 10px;
}

.cost-breakdown h4 {
  margin: 0 0 16px 0;
  color: #2c3e50;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.item-label {
  color: #606266;
}

.item-value {
  color: #2c3e50;
  font-weight: 500;
}

.recommended-route h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
}

.route-info p {
  margin: 4px 0;
  color: #606266;
}

.result-actions {
  margin-top: 20px;
  text-align: center;
}

.calculation-history {
  margin-bottom: 20px;
}

.history-list {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.history-item:hover {
  background-color: #f5f7fa;
}

.history-route {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.history-cost {
  color: #E6A23C;
  font-weight: bold;
}

.history-time {
  color: #909399;
  font-size: 12px;
}

.batch-calculator {
  margin-bottom: 20px;
}

.upload-demo {
  margin: 20px 0;
}
</style>
