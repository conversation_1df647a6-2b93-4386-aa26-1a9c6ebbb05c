#!/usr/bin/env python3
"""
添加批次相关字段到库存表
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from backend.config import config
import pymysql

def add_batch_fields():
    """添加批次相关字段到 inventory 表"""
    try:
        # 创建数据库连接
        connection = pymysql.connect(
            host=config.DB_HOST,
            port=config.DB_PORT,
            user=config.DB_USER,
            password=config.DB_PASSWORD,
            database=config.DB_NAME,
            charset=config.DB_CHARSET
        )
        
        with connection.cursor() as cursor:
            print("开始添加批次相关字段到库存表...")
            
            # 要添加的字段列表
            fields_to_add = [
                {
                    'name': 'batch_no',
                    'definition': 'VARCHAR(100) NULL COMMENT "批次号"'
                },
                {
                    'name': 'production_date',
                    'definition': 'DATE NULL COMMENT "生产日期"'
                },
                {
                    'name': 'expiry_date',
                    'definition': 'DATE NULL COMMENT "到期日期"'
                }
            ]
            
            for field in fields_to_add:
                try:
                    # 检查字段是否已存在
                    check_column_sql = """
                        SELECT COUNT(*) as count 
                        FROM information_schema.columns 
                        WHERE table_schema = %s 
                        AND table_name = 'inventory' 
                        AND column_name = %s
                    """
                    
                    cursor.execute(check_column_sql, (config.DB_NAME, field['name']))
                    result = cursor.fetchone()
                    
                    if result[0] == 0:
                        # 字段不存在，添加字段
                        add_column_sql = f"""
                            ALTER TABLE inventory 
                            ADD COLUMN {field['name']} {field['definition']}
                        """
                        
                        print(f"正在添加 {field['name']} 字段...")
                        cursor.execute(add_column_sql)
                        print(f"✓ {field['name']} 字段添加成功")
                    else:
                        print(f"✓ {field['name']} 字段已存在，跳过")
                        
                except Exception as e:
                    print(f"添加 {field['name']} 字段时出错: {e}")
            
            # 提交更改
            connection.commit()
            print("✓ 批次字段添加完成")
            
    except Exception as e:
        print(f"添加批次字段时出错: {e}")
        if 'connection' in locals():
            connection.rollback()
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    add_batch_fields()
