#!/usr/bin/env python3
"""
删除批次管理相关表和字段
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from backend.config import config
import pymysql

def remove_batch_management():
    """删除批次管理相关的表和字段"""
    try:
        # 创建数据库连接
        connection = pymysql.connect(
            host=config.DB_HOST,
            port=config.DB_PORT,
            user=config.DB_USER,
            password=config.DB_PASSWORD,
            database=config.DB_NAME,
            charset=config.DB_CHARSET
        )
        
        with connection.cursor() as cursor:
            print("开始删除批次管理相关功能...")
            
            # 删除批次相关表
            tables_to_drop = [
                'batch_transactions',
                'batch_inventories', 
                'batches'
            ]
            
            for table in tables_to_drop:
                try:
                    # 检查表是否存在
                    check_table_sql = """
                        SELECT COUNT(*) as count 
                        FROM information_schema.tables 
                        WHERE table_schema = %s 
                        AND table_name = %s
                    """
                    cursor.execute(check_table_sql, (config.DB_NAME, table))
                    result = cursor.fetchone()
                    
                    if result[0] > 0:
                        cursor.execute(f"DROP TABLE {table}")
                        print(f"✓ 删除表 {table}")
                    else:
                        print(f"✓ 表 {table} 不存在，跳过")
                        
                except Exception as e:
                    print(f"删除表 {table} 时出错: {e}")
            
            # 删除 inventory 表中的批次管理字段
            try:
                # 检查字段是否存在
                check_column_sql = """
                    SELECT COUNT(*) as count 
                    FROM information_schema.columns 
                    WHERE table_schema = %s 
                    AND table_name = 'inventory' 
                    AND column_name = 'enable_batch_management'
                """
                
                cursor.execute(check_column_sql, (config.DB_NAME,))
                result = cursor.fetchone()
                
                if result[0] > 0:
                    # 字段存在，删除字段
                    drop_column_sql = """
                        ALTER TABLE inventory 
                        DROP COLUMN enable_batch_management
                    """
                    
                    print("正在删除 inventory 表中的 enable_batch_management 字段...")
                    cursor.execute(drop_column_sql)
                    print("✓ 删除 enable_batch_management 字段")
                else:
                    print("✓ enable_batch_management 字段不存在")
                    
            except Exception as e:
                print(f"删除 enable_batch_management 字段时出错: {e}")
            
            # 删除 inventory_transactions 表中的批次相关字段
            try:
                batch_fields = ['batch_id', 'batch_no']
                
                for field in batch_fields:
                    # 检查字段是否存在
                    check_column_sql = """
                        SELECT COUNT(*) as count 
                        FROM information_schema.columns 
                        WHERE table_schema = %s 
                        AND table_name = 'inventory_transactions' 
                        AND column_name = %s
                    """
                    
                    cursor.execute(check_column_sql, (config.DB_NAME, field))
                    result = cursor.fetchone()
                    
                    if result[0] > 0:
                        # 字段存在，删除字段
                        drop_column_sql = f"""
                            ALTER TABLE inventory_transactions 
                            DROP COLUMN {field}
                        """
                        
                        print(f"正在删除 inventory_transactions 表中的 {field} 字段...")
                        cursor.execute(drop_column_sql)
                        print(f"✓ 删除 {field} 字段")
                    else:
                        print(f"✓ {field} 字段不存在")
                        
            except Exception as e:
                print(f"删除 inventory_transactions 表中批次字段时出错: {e}")
            
            # 提交更改
            connection.commit()
            print("✓ 批次管理功能删除完成")
            
    except Exception as e:
        print(f"删除批次管理功能时出错: {e}")
        if 'connection' in locals():
            connection.rollback()
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    remove_batch_management()
