"""
供应商数据模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, Boolean, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base


class Supplier(Base):
    """供应商模型"""
    __tablename__ = "suppliers"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(200), nullable=False, comment="供应商名称")
    code = Column(String(50), unique=True, nullable=False, comment="供应商编码")
    contact_person = Column(String(100), comment="联系人")
    phone = Column(String(20), comment="联系电话")
    email = Column(String(100), comment="邮箱")
    address = Column(Text, comment="地址")
    
    # 业务信息
    category = Column(String(100), comment="供应商类别")
    region = Column(String(100), comment="所在地区")
    credit_rating = Column(String(20), default="A", comment="信用等级")
    payment_terms = Column(String(100), comment="付款条件")
    delivery_terms = Column(String(100), comment="交货条件")
    cooperation_years = Column(Integer, default=0, comment="合作年限")
    main_products = Column(Text, comment="主营产品")
    
    # 财务信息
    credit_limit = Column(Float, default=0.0, comment="信用额度")
    current_balance = Column(Float, default=0.0, comment="当前余额")
    
    # 状态
    status = Column(String(20), default="active", comment="状态: active, inactive, suspended")
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 扩展信息
    business_license = Column(String(100), comment="营业执照号")
    tax_number = Column(String(100), comment="税号")
    bank_account = Column(JSON, comment="银行账户信息")
    certificates = Column(JSON, comment="资质证书")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    
    # 备注
    remark = Column(Text, comment="备注")

    # 关系
    purchase_orders = relationship("PurchaseOrder", back_populates="supplier")
    purchase_receipts = relationship("PurchaseReceipt", back_populates="supplier")
    purchase_returns = relationship("PurchaseReturn", back_populates="supplier")

    def __repr__(self):
        return f"<Supplier(id={self.id}, name='{self.name}', code='{self.code}')>"
