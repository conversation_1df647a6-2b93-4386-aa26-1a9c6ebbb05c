from sqlalchemy import Column, Integer, String, Float, DateTime, Text, JSON, Boolean
from sqlalchemy.sql import func
from app.core.database import Base

class LogisticsRoute(Base):
    __tablename__ = "logistics_routes"
    
    id = Column(Integer, primary_key=True, index=True)
    route_name = Column(String(255), nullable=False)
    origin = Column(String(255), nullable=False)
    destination = Column(String(255), nullable=False)
    
    # 距离和时间
    distance_km = Column(Float, nullable=False)
    estimated_time_hours = Column(Float, nullable=False)
    
    # 成本信息
    base_cost = Column(Float, nullable=False)
    cost_per_km = Column(Float, nullable=False)
    fuel_cost = Column(Float)
    toll_cost = Column(Float)
    
    # 路线状态
    is_active = Column(Boolean, default=True)  # 是否启用
    status = Column(String(20), default="active")  # active, inactive, maintenance
    
    # 额外信息
    route_details = Column(JSON)  # 详细路线信息
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class DeliveryOrder(Base):
    __tablename__ = "delivery_orders"
    
    id = Column(Integer, primary_key=True, index=True)
    order_number = Column(String(100), unique=True, nullable=False, index=True)
    
    # 配送信息
    pickup_address = Column(String(500), nullable=False)
    delivery_address = Column(String(500), nullable=False)
    route_id = Column(Integer, nullable=False)
    
    # 货物信息
    weight_kg = Column(Float, nullable=False)
    volume_m3 = Column(Float)
    package_count = Column(Integer, default=1)
    
    # 成本和时间
    calculated_cost = Column(Float)
    estimated_delivery_time = Column(DateTime)
    actual_delivery_time = Column(DateTime)
    
    # 状态
    status = Column(String(50), default="pending")  # pending, in_transit, delivered, cancelled
    
    # 额外信息
    special_requirements = Column(Text)
    delivery_notes = Column(Text)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


