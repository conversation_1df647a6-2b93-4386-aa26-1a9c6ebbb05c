# 采购订单子表行号字段添加说明

## 需求概述

为采购订单子表增加行号字段，方便收货单对应导入。行号字段只在后端数据库中存储，前端页面不需要展示。

## 实现方案

### 1. 数据库结构变更

#### 添加字段
在 `purchase_order_items` 表中添加 `line_number` 字段：
```sql
ALTER TABLE purchase_order_items 
ADD COLUMN line_number INT NOT NULL COMMENT '行号';
```

#### 字段说明
- **字段名**: `line_number`
- **类型**: `INT`
- **约束**: `NOT NULL`
- **注释**: 行号
- **用途**: 标识采购订单中每个商品明细的行号，方便收货单导入时进行对应

### 2. 后端代码变更

#### 数据模型更新
在 `backend/app/models/purchase.py` 中的 `PurchaseOrderItem` 模型添加行号字段：
```python
# 行号
line_number = Column(Integer, nullable=False, comment="行号")
```

#### Schema更新
在 `backend/app/schemas/purchase.py` 中更新相关Schema：

**PurchaseOrderItemBase**:
```python
line_number: int = Field(..., gt=0, description="行号")
```

**PurchaseOrderItemUpdate**:
```python
line_number: Optional[int] = Field(None, gt=0)
```

#### 服务层更新
在 `backend/app/services/purchase_service.py` 中更新创建和更新方法：

**创建采购订单**:
```python
# 使用提供的行号，如果没有提供则使用索引
line_number = getattr(item_data, 'line_number', index)

db_item = PurchaseOrderItem(
    order_id=db_order.id,
    product_id=item_data.product_id,
    line_number=line_number,
    # ... 其他字段
)
```

### 3. 前端代码变更

#### API类型定义
在 `frontend/src/api/purchase.ts` 中更新 `PurchaseOrderItem` 接口：
```typescript
export interface PurchaseOrderItem {
  id?: number
  order_id?: number
  product_id: number
  line_number: number  // 新增行号字段
  product_name: string
  // ... 其他字段
}
```

#### 前端逻辑更新
在 `frontend/src/views/PurchaseOrderView.vue` 中更新 `addPurchaseItem` 方法：
```javascript
const addPurchaseItem = () => {
  orderForm.items.push({
    product_id: 0,
    line_number: orderForm.items.length + 1,  // 自动设置行号
    product_name: '',
    product_sku: '',
    quantity: 1,
    unit_price: 0,
    total_price: 0
  })
}
```

## 数据迁移

### 现有数据处理
对于已存在的采购订单明细数据，需要为其设置行号：

```sql
-- 为现有数据按订单分组设置行号
SET @row_number = 0;
UPDATE purchase_order_items 
SET line_number = (@row_number := @row_number + 1)
WHERE order_id = [订单ID]
ORDER BY id;
```

### 迁移脚本
创建了以下迁移脚本：
- `backend/add_line_number_to_purchase_order_items.py` - 完整的迁移脚本
- `backend/add_line_number_direct.py` - 直接执行的迁移脚本
- `backend/quick_add_line_number.py` - 快速迁移脚本

## 业务逻辑

### 行号生成规则
1. **新建订单**: 按添加顺序自动生成行号（1, 2, 3...）
2. **编辑订单**: 保持现有行号，新增明细使用下一个可用行号
3. **删除明细**: 不重新排序行号，保持原有编号
4. **导入数据**: 可以指定行号，如未指定则自动生成

### 行号用途
1. **收货单导入**: 收货单可以通过行号精确对应采购订单的具体明细
2. **数据追溯**: 便于追踪采购订单明细的变更历史
3. **业务对接**: 与外部系统对接时提供明确的行标识

## 注意事项

### 1. 前端显示
- 行号字段仅在后端存储，前端页面不显示
- 前端在添加商品时自动设置行号
- 用户无需手动输入或编辑行号

### 2. 数据一致性
- 行号在同一订单内必须唯一
- 行号一旦设置不建议修改，以保持数据追溯性
- 删除明细时不重新排序，避免影响已关联的收货单

### 3. 兼容性
- 新增字段不影响现有功能
- 现有API接口保持兼容
- 前端界面无变化，用户体验不受影响

## 测试建议

### 1. 功能测试
- 测试新建采购订单时行号的自动生成
- 测试编辑订单时行号的保持和新增
- 测试删除明细时行号的处理

### 2. 数据测试
- 验证现有数据的行号迁移
- 测试行号的唯一性约束
- 验证与收货单的关联功能

### 3. 接口测试
- 测试创建订单API的行号处理
- 测试更新订单API的行号逻辑
- 验证返回数据中包含正确的行号

## 部署步骤

1. **数据库迁移**: 执行SQL脚本添加行号字段
2. **现有数据处理**: 运行迁移脚本为现有数据设置行号
3. **后端部署**: 部署包含行号字段的后端代码
4. **前端部署**: 部署更新后的前端代码
5. **功能验证**: 验证行号功能正常工作

## 总结

通过添加行号字段，采购订单子表现在具备了更好的数据标识能力，为收货单对应导入提供了便利。该实现方案保持了系统的向后兼容性，不影响现有功能的正常使用。
