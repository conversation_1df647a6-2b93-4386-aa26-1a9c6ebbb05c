"""
采购管理服务 - 包含采购订单、采购入库单
"""

from typing import List, Tuple, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from app.models.purchase import (
    PurchaseOrder, PurchaseOrderItem,
    PurchaseReceipt, PurchaseReceiptItem
)
from app.models.supplier import Supplier
from app.models.warehouse import Warehouse
from app.models.product import Product
from app.schemas.purchase import (
    PurchaseOrderCreate, PurchaseOrderUpdate, PurchaseOrderQuery,
    PurchaseOrderStats, PurchaseOrderStatus,
    PurchaseReceiptCreate, PurchaseReceiptUpdate, PurchaseReceiptQuery,
    PurchaseReceiptStatus
)


class PurchaseService:
    """采购管理服务 - 包含采购订单、采购入库单"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_purchase_orders(
        self, 
        skip: int = 0, 
        limit: int = 20, 
        query: Optional[PurchaseOrderQuery] = None
    ) -> <PERSON><PERSON>[List[PurchaseOrder], int]:
        """获取采购订单列表"""
        
        # 构建查询
        db_query = self.db.query(PurchaseOrder).options(
            joinedload(PurchaseOrder.supplier),
            joinedload(PurchaseOrder.items).joinedload(PurchaseOrderItem.product)
        )
        
        # 应用筛选条件
        if query:
            if query.order_no:
                db_query = db_query.filter(PurchaseOrder.order_no.ilike(f"%{query.order_no}%"))
            if query.supplier_id:
                db_query = db_query.filter(PurchaseOrder.supplier_id == query.supplier_id)
            if query.status:
                db_query = db_query.filter(PurchaseOrder.status == query.status)
            if query.created_by:
                db_query = db_query.filter(PurchaseOrder.created_by.ilike(f"%{query.created_by}%"))
            if query.start_date:
                db_query = db_query.filter(PurchaseOrder.created_at >= query.start_date)
            if query.end_date:
                db_query = db_query.filter(PurchaseOrder.created_at <= query.end_date)
        
        # 获取总数
        total = db_query.count()
        
        # 分页和排序
        orders = db_query.order_by(desc(PurchaseOrder.created_at)).offset(skip).limit(limit).all()
        
        return orders, total
    
    def get_purchase_order(self, order_id: int) -> Optional[PurchaseOrder]:
        """获取单个采购订单"""
        return self.db.query(PurchaseOrder).options(
            joinedload(PurchaseOrder.supplier),
            joinedload(PurchaseOrder.items).joinedload(PurchaseOrderItem.product)
        ).filter(PurchaseOrder.id == order_id).first()
    
    def create_purchase_order(self, order_data: PurchaseOrderCreate) -> PurchaseOrder:
        """创建采购订单"""
        
        # 生成订单号
        order_no = self._generate_order_no()
        
        # 创建订单
        db_order = PurchaseOrder(
            order_no=order_no,
            supplier_id=order_data.supplier_id,
            total_amount=order_data.total_amount,
            status=order_data.status,
            expected_date=order_data.expected_date,
            created_by=order_data.created_by,
            remark=order_data.remark
        )
        
        self.db.add(db_order)
        self.db.flush()  # 获取订单ID
        
        # 创建订单明细
        for index, item_data in enumerate(order_data.items, 1):
            # 获取商品信息
            product = self.db.query(Product).filter(Product.id == item_data.product_id).first()
            if not product:
                raise ValueError(f"商品ID {item_data.product_id} 不存在")

            # 创建订单明细，自动生成行号
            db_item = PurchaseOrderItem(
                order_id=db_order.id,
                product_id=item_data.product_id,
                line_number=index,  # 自动生成行号
                product_name=product.name,
                product_sku=product.sku,
                quantity=item_data.quantity,
                unit_price=item_data.unit_price,
                total_price=item_data.total_price
            )
            self.db.add(db_item)
        
        self.db.commit()
        self.db.refresh(db_order)
        
        return db_order
    
    def update_purchase_order(self, order_id: int, order_data: PurchaseOrderUpdate) -> Optional[PurchaseOrder]:
        """更新采购订单"""
        
        db_order = self.db.query(PurchaseOrder).filter(PurchaseOrder.id == order_id).first()
        if not db_order:
            return None
        
        # 更新订单基本信息
        update_data = order_data.dict(exclude_unset=True, exclude={'items'})
        for field, value in update_data.items():
            setattr(db_order, field, value)
        
        # 更新订单明细
        if order_data.items is not None:
            # 删除现有明细
            self.db.query(PurchaseOrderItem).filter(PurchaseOrderItem.order_id == order_id).delete()
            
            # 添加新明细
            for index, item_data in enumerate(order_data.items, 1):
                product = self.db.query(Product).filter(Product.id == item_data.product_id).first()
                if not product:
                    raise ValueError(f"商品ID {item_data.product_id} 不存在")

                # 更新时重新生成行号
                db_item = PurchaseOrderItem(
                    order_id=order_id,
                    product_id=item_data.product_id,
                    line_number=index,  # 重新生成行号
                    product_name=product.name,
                    product_sku=product.sku,
                    quantity=item_data.quantity,
                    unit_price=item_data.unit_price,
                    total_price=item_data.total_price
                )
                self.db.add(db_item)
        
        self.db.commit()
        self.db.refresh(db_order)
        
        return db_order
    
    def delete_purchase_order(self, order_id: int) -> bool:
        """删除采购订单"""
        
        db_order = self.db.query(PurchaseOrder).filter(PurchaseOrder.id == order_id).first()
        if not db_order:
            return False
        
        # 只能删除草稿状态的订单
        if db_order.status != PurchaseOrderStatus.DRAFT:
            raise ValueError("只能删除草稿状态的订单")
        
        self.db.delete(db_order)
        self.db.commit()
        
        return True
    
    def get_purchase_order_stats(self) -> PurchaseOrderStats:
        """获取采购订单统计"""
        
        # 基础统计
        total_orders = self.db.query(PurchaseOrder).count()
        pending_orders = self.db.query(PurchaseOrder).filter(
            PurchaseOrder.status.in_([PurchaseOrderStatus.DRAFT, PurchaseOrderStatus.SUBMITTED])
        ).count()
        processing_orders = self.db.query(PurchaseOrder).filter(
            PurchaseOrder.status.in_([PurchaseOrderStatus.APPROVED, PurchaseOrderStatus.PURCHASING])
        ).count()
        completed_orders = self.db.query(PurchaseOrder).filter(
            PurchaseOrder.status == PurchaseOrderStatus.COMPLETED
        ).count()
        
        # 总金额
        total_amount_result = self.db.query(func.sum(PurchaseOrder.total_amount)).scalar()
        total_amount = total_amount_result or 0
        
        # 本月订单数
        current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        this_month_orders = self.db.query(PurchaseOrder).filter(
            PurchaseOrder.created_at >= current_month_start
        ).count()
        
        return PurchaseOrderStats(
            total_orders=total_orders,
            pending_orders=pending_orders,
            processing_orders=processing_orders,
            completed_orders=completed_orders,
            total_amount=total_amount,
            this_month_orders=this_month_orders
        )
    
    def _generate_order_no(self) -> str:
        """生成订单号"""
        today = datetime.now().strftime("%Y%m%d")
        
        # 查找今天的最大订单号
        latest_order = self.db.query(PurchaseOrder).filter(
            PurchaseOrder.order_no.like(f"PO{today}%")
        ).order_by(desc(PurchaseOrder.order_no)).first()
        
        if latest_order:
            # 提取序号并加1
            sequence = int(latest_order.order_no[-4:]) + 1
        else:
            sequence = 1
        
        return f"PO{today}{sequence:04d}"

    # ==================== 采购入库单相关方法 ====================

    def get_purchase_receipts(
        self,
        skip: int = 0,
        limit: int = 20,
        query: Optional[PurchaseReceiptQuery] = None
    ) -> Tuple[List[PurchaseReceipt], int]:
        """获取采购入库单列表"""

        # 构建查询
        db_query = self.db.query(PurchaseReceipt).options(
            joinedload(PurchaseReceipt.supplier),
            joinedload(PurchaseReceipt.warehouse),
            joinedload(PurchaseReceipt.purchase_order),
            joinedload(PurchaseReceipt.items).joinedload(PurchaseReceiptItem.product)
        )

        # 应用筛选条件
        if query:
            if query.receipt_no:
                db_query = db_query.filter(PurchaseReceipt.receipt_no.ilike(f"%{query.receipt_no}%"))
            if query.purchase_order_no:
                db_query = db_query.filter(PurchaseReceipt.purchase_order_no.ilike(f"%{query.purchase_order_no}%"))
            if query.supplier_id:
                db_query = db_query.filter(PurchaseReceipt.supplier_id == query.supplier_id)
            if query.warehouse_id:
                db_query = db_query.filter(PurchaseReceipt.warehouse_id == query.warehouse_id)
            if query.status:
                db_query = db_query.filter(PurchaseReceipt.status == query.status)
            if query.created_by:
                db_query = db_query.filter(PurchaseReceipt.created_by.ilike(f"%{query.created_by}%"))
            if query.start_date:
                db_query = db_query.filter(PurchaseReceipt.created_at >= query.start_date)
            if query.end_date:
                db_query = db_query.filter(PurchaseReceipt.created_at <= query.end_date)

        # 获取总数
        total = db_query.count()

        # 分页和排序
        receipts = db_query.order_by(desc(PurchaseReceipt.created_at)).offset(skip).limit(limit).all()

        # 设置动态字段并清除关联对象以避免序列化问题
        for receipt in receipts:
            if receipt.supplier:
                receipt.supplier_name = receipt.supplier.name
                receipt.supplier = None  # 清除关联对象
            if receipt.warehouse:
                receipt.warehouse_name = receipt.warehouse.name
                receipt.warehouse = None  # 清除关联对象
            receipt.purchase_order = None  # 清除关联对象

        return receipts, total

    def get_purchase_receipt(self, receipt_id: int) -> Optional[PurchaseReceipt]:
        """获取单个采购入库单"""
        receipt = self.db.query(PurchaseReceipt).options(
            joinedload(PurchaseReceipt.supplier),
            joinedload(PurchaseReceipt.warehouse),
            joinedload(PurchaseReceipt.purchase_order),
            joinedload(PurchaseReceipt.items).joinedload(PurchaseReceiptItem.product)
        ).filter(PurchaseReceipt.id == receipt_id).first()

        # 设置动态字段并清除关联对象以避免序列化问题
        if receipt:
            if receipt.supplier:
                receipt.supplier_name = receipt.supplier.name
                receipt.supplier = None  # 清除关联对象
            if receipt.warehouse:
                receipt.warehouse_name = receipt.warehouse.name
                receipt.warehouse = None  # 清除关联对象
            receipt.purchase_order = None  # 清除关联对象

        return receipt

    def create_purchase_receipt(self, receipt_data: PurchaseReceiptCreate) -> PurchaseReceipt:
        """创建采购入库单"""

        # 生成入库单号
        receipt_no = self._generate_receipt_no()

        # 创建入库单
        db_receipt = PurchaseReceipt(
            receipt_no=receipt_no,
            purchase_order_id=receipt_data.purchase_order_id,
            purchase_order_no=receipt_data.purchase_order_no,
            supplier_id=receipt_data.supplier_id,
            warehouse_id=receipt_data.warehouse_id,
            status=receipt_data.status,
            receipt_date=receipt_data.receipt_date,
            created_by=receipt_data.created_by,
            remark=receipt_data.remark
        )

        self.db.add(db_receipt)
        self.db.flush()  # 获取入库单ID

        # 创建入库单明细
        for item_data in receipt_data.items:
            # 获取商品信息
            product = self.db.query(Product).filter(Product.id == item_data.product_id).first()
            if not product:
                raise ValueError(f"商品ID {item_data.product_id} 不存在")

            db_item = PurchaseReceiptItem(
                receipt_id=db_receipt.id,
                product_id=item_data.product_id,
                product_name=product.name,
                product_sku=product.sku,
                quantity=item_data.quantity,
                batch_no=item_data.batch_no
            )
            self.db.add(db_item)

        self.db.commit()
        self.db.refresh(db_receipt)

        return db_receipt

    def update_purchase_receipt(self, receipt_id: int, receipt_data: PurchaseReceiptUpdate) -> Optional[PurchaseReceipt]:
        """更新采购入库单"""

        db_receipt = self.db.query(PurchaseReceipt).filter(PurchaseReceipt.id == receipt_id).first()
        if not db_receipt:
            return None

        # 更新入库单基本信息
        update_data = receipt_data.dict(exclude_unset=True, exclude={'items'})
        for field, value in update_data.items():
            setattr(db_receipt, field, value)

        # 更新入库单明细
        if receipt_data.items is not None:
            # 删除现有明细
            self.db.query(PurchaseReceiptItem).filter(PurchaseReceiptItem.receipt_id == receipt_id).delete()

            # 添加新明细
            for item_data in receipt_data.items:
                product = self.db.query(Product).filter(Product.id == item_data.product_id).first()
                if not product:
                    raise ValueError(f"商品ID {item_data.product_id} 不存在")

                db_item = PurchaseReceiptItem(
                    receipt_id=receipt_id,
                    product_id=item_data.product_id,
                    product_name=product.name,
                    product_sku=product.sku,
                    quantity=item_data.quantity,
                    batch_no=item_data.batch_no
                )
                self.db.add(db_item)

        self.db.commit()
        self.db.refresh(db_receipt)

        return db_receipt

    def delete_purchase_receipt(self, receipt_id: int) -> bool:
        """删除采购入库单"""

        db_receipt = self.db.query(PurchaseReceipt).filter(PurchaseReceipt.id == receipt_id).first()
        if not db_receipt:
            return False

        # 只能删除草稿状态的入库单
        if db_receipt.status not in [PurchaseReceiptStatus.DRAFT, PurchaseReceiptStatus.REJECTED]:
            raise ValueError("只能删除草稿或已拒绝状态的入库单")

        self.db.delete(db_receipt)
        self.db.commit()

        return True

    def submit_purchase_receipt(self, receipt_id: int, submitted_by: str) -> Optional[PurchaseReceipt]:
        """提交采购入库单"""

        db_receipt = self.db.query(PurchaseReceipt).filter(PurchaseReceipt.id == receipt_id).first()
        if not db_receipt:
            return None

        # 只能提交草稿状态的入库单
        if db_receipt.status != PurchaseReceiptStatus.DRAFT:
            raise ValueError("只能提交草稿状态的入库单")

        # 验证入库单数据完整性
        if not db_receipt.items:
            raise ValueError("入库单必须包含商品明细")

        for item in db_receipt.items:
            if item.quantity <= 0:
                raise ValueError(f"商品 {item.product_name} 的数量必须大于0")

        # 更新状态和提交信息
        db_receipt.status = PurchaseReceiptStatus.SUBMITTED
        db_receipt.submitted_by = submitted_by
        db_receipt.submitted_at = func.now()

        self.db.commit()
        self.db.refresh(db_receipt)

        return db_receipt

    def approve_purchase_receipt(self, receipt_id: int, approved_by: str, approved: bool = True, remark: str = None) -> Optional[PurchaseReceipt]:
        """审核采购入库单"""

        db_receipt = self.db.query(PurchaseReceipt).filter(PurchaseReceipt.id == receipt_id).first()
        if not db_receipt:
            return None

        # 只能审核已提交状态的入库单
        if db_receipt.status != PurchaseReceiptStatus.SUBMITTED:
            raise ValueError("只能审核已提交状态的入库单")

        # 更新状态和审核信息
        if approved:
            db_receipt.status = PurchaseReceiptStatus.APPROVED

            # 如果审核通过且基于采购订单，更新采购订单的已收货数量
            if db_receipt.purchase_order_id:
                self._update_purchase_order_received_quantity(db_receipt)
        else:
            db_receipt.status = PurchaseReceiptStatus.REJECTED

        db_receipt.approved_by = approved_by
        db_receipt.approved_at = func.now()

        if remark:
            db_receipt.remark = remark

        self.db.commit()
        self.db.refresh(db_receipt)

        return db_receipt

    def recall_purchase_receipt(self, receipt_id: int) -> Optional[PurchaseReceipt]:
        """撤回采购入库单（从已提交状态撤回到草稿状态）"""

        db_receipt = self.db.query(PurchaseReceipt).filter(PurchaseReceipt.id == receipt_id).first()
        if not db_receipt:
            return None

        # 只能撤回已提交状态的入库单
        if db_receipt.status != PurchaseReceiptStatus.SUBMITTED:
            raise ValueError("只能撤回已提交状态的入库单")

        # 更新状态，清除提交信息
        db_receipt.status = PurchaseReceiptStatus.DRAFT
        db_receipt.submitted_by = None
        db_receipt.submitted_at = None

        self.db.commit()
        self.db.refresh(db_receipt)

        return db_receipt

    def cancel_purchase_receipt(self, receipt_id: int) -> Optional[PurchaseReceipt]:
        """取消采购入库单"""

        db_receipt = self.db.query(PurchaseReceipt).filter(PurchaseReceipt.id == receipt_id).first()
        if not db_receipt:
            return None

        # 只能取消草稿或已提交状态的入库单
        if db_receipt.status not in [PurchaseReceiptStatus.DRAFT, PurchaseReceiptStatus.SUBMITTED]:
            raise ValueError("只能取消草稿或已提交状态的入库单")

        # 直接更新状态，不影响其他字段
        db_receipt.status = PurchaseReceiptStatus.CANCELLED

        self.db.commit()
        self.db.refresh(db_receipt)

        return db_receipt

    def _update_purchase_order_received_quantity(self, receipt: PurchaseReceipt):
        """更新采购订单的已收货数量"""

        # 获取采购订单明细
        order_items = self.db.query(PurchaseOrderItem).filter(
            PurchaseOrderItem.order_id == receipt.purchase_order_id
        ).all()

        # 为每个入库单明细更新对应的采购订单明细的已收货数量
        for receipt_item in receipt.items:
            # 找到对应的采购订单明细
            order_item = next(
                (item for item in order_items if item.product_id == receipt_item.product_id),
                None
            )

            if order_item:
                # 增加已收货数量
                order_item.received_quantity = (order_item.received_quantity or 0) + receipt_item.quantity

                # 确保已收货数量不超过采购数量
                if order_item.received_quantity > order_item.quantity:
                    order_item.received_quantity = order_item.quantity

    def get_purchase_receipt_stats(self):
        """获取采购入库单统计"""

        # 基础统计
        total_receipts = self.db.query(PurchaseReceipt).count()
        draft_receipts = self.db.query(PurchaseReceipt).filter(
            PurchaseReceipt.status == PurchaseReceiptStatus.DRAFT
        ).count()
        submitted_receipts = self.db.query(PurchaseReceipt).filter(
            PurchaseReceipt.status == PurchaseReceiptStatus.SUBMITTED
        ).count()
        approved_receipts = self.db.query(PurchaseReceipt).filter(
            PurchaseReceipt.status == PurchaseReceiptStatus.APPROVED
        ).count()
        rejected_receipts = self.db.query(PurchaseReceipt).filter(
            PurchaseReceipt.status == PurchaseReceiptStatus.REJECTED
        ).count()

        # 本月入库单数
        current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        this_month_receipts = self.db.query(PurchaseReceipt).filter(
            PurchaseReceipt.created_at >= current_month_start
        ).count()

        return {
            "total_receipts": total_receipts,
            "draft_receipts": draft_receipts,
            "submitted_receipts": submitted_receipts,
            "approved_receipts": approved_receipts,
            "rejected_receipts": rejected_receipts,
            "this_month_receipts": this_month_receipts
        }

    def create_receipt_from_order(self, order_id: int, warehouse_id: int, created_by: str) -> PurchaseReceipt:
        """从采购订单创建入库单"""

        # 获取采购订单
        order = self.db.query(PurchaseOrder).options(
            joinedload(PurchaseOrder.items)
        ).filter(PurchaseOrder.id == order_id).first()

        if not order:
            raise ValueError(f"采购订单ID {order_id} 不存在")

        # 创建入库单数据
        receipt_data = PurchaseReceiptCreate(
            purchase_order_id=order.id,
            purchase_order_no=order.order_no,
            supplier_id=order.supplier_id,
            warehouse_id=warehouse_id,
            status=PurchaseReceiptStatus.PENDING,
            created_by=created_by,
            items=[
                {
                    "product_id": item.product_id,
                    "product_name": item.product_name,
                    "product_sku": item.product_sku,
                    "quantity": 0,  # 初始数量为0
                    "batch_no": None  # 初始批次号为空
                }
                for item in order.items
            ]
        )

        return self.create_purchase_receipt(receipt_data)

    def _generate_receipt_no(self) -> str:
        """生成入库单号"""
        today = datetime.now().strftime("%Y%m%d")

        # 查找今天的最大入库单号
        latest_receipt = self.db.query(PurchaseReceipt).filter(
            PurchaseReceipt.receipt_no.like(f"PR{today}%")
        ).order_by(desc(PurchaseReceipt.receipt_no)).first()

        if latest_receipt:
            # 提取序号并加1
            sequence = int(latest_receipt.receipt_no[-4:]) + 1
        else:
            sequence = 1

        return f"PR{today}{sequence:04d}"
