import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { 
  logisticsApi, 
  type LogisticsRoute, 
  type DeliveryOrder, 
  type CostCalculationResponse 
} from '../api/logistics'

export const useLogisticsStore = defineStore('logistics', () => {
  // 状态
  const routes = ref<LogisticsRoute[]>([])
  const deliveryOrders = ref<DeliveryOrder[]>([])
  const currentRoute = ref<LogisticsRoute | null>(null)
  const currentOrder = ref<DeliveryOrder | null>(null)
  const costCalculation = ref<CostCalculationResponse | null>(null)
  const performanceData = ref<any>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const activeRoutes = computed(() => {
    return routes.value.filter(route => route.is_active === 'active')
  })

  const routesByOrigin = computed(() => {
    const origins: Record<string, LogisticsRoute[]> = {}
    routes.value.forEach(route => {
      if (!origins[route.origin]) {
        origins[route.origin] = []
      }
      origins[route.origin].push(route)
    })
    return origins
  })

  const ordersByStatus = computed(() => {
    const statuses: Record<string, DeliveryOrder[]> = {}
    deliveryOrders.value.forEach(order => {
      if (!statuses[order.status]) {
        statuses[order.status] = []
      }
      statuses[order.status].push(order)
    })
    return statuses
  })

  const pendingOrders = computed(() => {
    return deliveryOrders.value.filter(order => order.status === 'pending')
  })

  const inTransitOrders = computed(() => {
    return deliveryOrders.value.filter(order => order.status === 'in_transit')
  })

  // 操作方法
  const fetchRoutes = async (params?: { skip?: number; limit?: number; origin?: string; destination?: string }) => {
    try {
      loading.value = true
      error.value = null
      const data = await logisticsApi.getRoutes(params)
      routes.value = data
    } catch (err) {
      error.value = '获取路线列表失败'
      console.error(err)
    } finally {
      loading.value = false
    }
  }

  const fetchRoute = async (id: number) => {
    try {
      loading.value = true
      error.value = null
      const data = await logisticsApi.getRoute(id)
      currentRoute.value = data
      return data
    } catch (err) {
      error.value = '获取路线详情失败'
      console.error(err)
      return null
    } finally {
      loading.value = false
    }
  }

  const createRoute = async (routeData: Omit<LogisticsRoute, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      loading.value = true
      error.value = null
      const data = await logisticsApi.createRoute(routeData)
      routes.value.push(data)
      return data
    } catch (err) {
      error.value = '创建路线失败'
      console.error(err)
      return null
    } finally {
      loading.value = false
    }
  }

  const calculateCost = async (params: any) => {
    try {
      loading.value = true
      error.value = null
      const data = await logisticsApi.calculateCost(params)
      costCalculation.value = data
      return data
    } catch (err) {
      error.value = '计算成本失败'
      console.error(err)
      return null
    } finally {
      loading.value = false
    }
  }

  const optimizeRoute = async (params: any) => {
    try {
      loading.value = true
      error.value = null
      const data = await logisticsApi.optimizeRoute(params)
      return data
    } catch (err) {
      error.value = '路线优化失败'
      console.error(err)
      return null
    } finally {
      loading.value = false
    }
  }

  const fetchDeliveryOrders = async (params?: { skip?: number; limit?: number; status?: string }) => {
    try {
      loading.value = true
      error.value = null
      const data = await logisticsApi.getDeliveryOrders(params)
      deliveryOrders.value = data
    } catch (err) {
      error.value = '获取订单列表失败'
      console.error(err)
    } finally {
      loading.value = false
    }
  }

  const fetchDeliveryOrder = async (id: number) => {
    try {
      loading.value = true
      error.value = null
      const data = await logisticsApi.getDeliveryOrder(id)
      currentOrder.value = data
      return data
    } catch (err) {
      error.value = '获取订单详情失败'
      console.error(err)
      return null
    } finally {
      loading.value = false
    }
  }

  const createDeliveryOrder = async (orderData: any) => {
    try {
      loading.value = true
      error.value = null
      const data = await logisticsApi.createDeliveryOrder(orderData)
      deliveryOrders.value.push(data)
      return data
    } catch (err) {
      error.value = '创建订单失败'
      console.error(err)
      return null
    } finally {
      loading.value = false
    }
  }

  const updateOrderStatus = async (id: number, status: string, notes?: string) => {
    try {
      loading.value = true
      error.value = null
      await logisticsApi.updateOrderStatus(id, status, notes)
      
      // 更新本地状态
      const order = deliveryOrders.value.find(o => o.id === id)
      if (order) {
        order.status = status
        if (notes) {
          order.delivery_notes = notes
        }
      }
      
      if (currentOrder.value?.id === id) {
        currentOrder.value.status = status
        if (notes) {
          currentOrder.value.delivery_notes = notes
        }
      }
      
      return true
    } catch (err) {
      error.value = '更新订单状态失败'
      console.error(err)
      return false
    } finally {
      loading.value = false
    }
  }

  const fetchPerformanceData = async (timePeriod?: string) => {
    try {
      loading.value = true
      error.value = null
      const data = await logisticsApi.getPerformanceAnalytics({ time_period: timePeriod })
      performanceData.value = data
      return data
    } catch (err) {
      error.value = '获取性能数据失败'
      console.error(err)
      return null
    } finally {
      loading.value = false
    }
  }

  const estimateDeliveryTime = async (params: any) => {
    try {
      loading.value = true
      error.value = null
      const data = await logisticsApi.estimateDeliveryTime(params)
      return data
    } catch (err) {
      error.value = '预估配送时间失败'
      console.error(err)
      return null
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    routes,
    deliveryOrders,
    currentRoute,
    currentOrder,
    costCalculation,
    performanceData,
    loading,
    error,
    
    // 计算属性
    activeRoutes,
    routesByOrigin,
    ordersByStatus,
    pendingOrders,
    inTransitOrders,
    
    // 方法
    fetchRoutes,
    fetchRoute,
    createRoute,
    calculateCost,
    optimizeRoute,
    fetchDeliveryOrders,
    fetchDeliveryOrder,
    createDeliveryOrder,
    updateOrderStatus,
    fetchPerformanceData,
    estimateDeliveryTime,
    clearError
  }
})
