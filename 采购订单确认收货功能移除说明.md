# 采购订单确认收货功能移除说明

## 变更概述

根据业务需求，移除了采购订单中的确认收货按钮及其相关功能。

## 变更内容

### 1. 前端界面变更

#### 表格视图
- **移除前**: 在"采购中"状态的订单的"更多"下拉菜单中显示"确认收货"选项
- **移除后**: "更多"下拉菜单中不再显示"确认收货"选项

#### 卡片视图
- **移除前**: 在"采购中"状态的订单的"更多"下拉菜单中显示"确认收货"选项
- **移除后**: "更多"下拉菜单中不再显示"确认收货"选项

### 2. 功能逻辑变更

#### 操作处理逻辑
- **移除前**: `handleOrderAction` 方法中包含 `receive` 命令的处理逻辑
- **移除后**: 移除了 `receive` 命令的处理逻辑

#### 状态流转变更
- **移除前**: 采购中 → 确认收货 → 已完成
- **移除后**: 采购中状态的订单无法通过前端界面直接变更为已完成状态

## 影响分析

### 1. 用户界面
- 采购中状态的订单操作选项减少
- 界面更加简洁，减少了不必要的操作步骤

### 2. 业务流程
- 采购订单的状态流转需要通过其他方式完成
- 可能需要通过后台管理或其他业务流程来标记订单完成

### 3. 数据一致性
- 后端API接口保持不变，仍支持状态更新
- 只是前端界面不再提供确认收货的操作入口

## 技术实现

### 修改的文件
- `frontend/src/views/PurchaseOrderView.vue`

### 具体变更
1. **删除表格视图中的确认收货选项**
   ```vue
   <!-- 移除了这行 -->
   <el-dropdown-item command="receive" v-if="row.status === 'purchasing'">确认收货</el-dropdown-item>
   ```

2. **删除卡片视图中的确认收货选项**
   ```vue
   <!-- 移除了这行 -->
   <el-dropdown-item command="receive" v-if="order.status === 'purchasing'">确认收货</el-dropdown-item>
   ```

3. **删除确认收货的处理逻辑**
   ```javascript
   // 移除了这段代码
   case 'receive':
     await ElMessageBox.confirm(
       '确定已收到货物吗？',
       '确认收货',
       {
         confirmButtonText: '确定',
         cancelButtonText: '取消',
         type: 'warning'
       }
     )
     await purchaseApi.updateOrderStatus(order.id, PurchaseOrderStatus.COMPLETED, '当前用户')
     ElMessage.success('确认收货成功')
     break
   ```

## 当前状态流转

移除确认收货功能后，采购订单的状态流转如下：

```
草稿 → 已提交 → 已审核 → 采购中
  ↑      ↓         ↓
  └─── 撤回    审批拒绝 → 已拒绝
                        ↓
                    重新编辑 → 草稿
```

**注意**: 采购中状态的订单无法通过前端界面直接变更为已完成状态。

## 后续建议

1. **业务流程优化**: 考虑通过其他业务流程（如入库单审核通过）来自动更新采购订单状态
2. **权限管理**: 如果需要手动完成订单，可以考虑为特定角色提供后台管理功能
3. **状态监控**: 建议添加对长期处于"采购中"状态订单的监控和提醒机制

## 测试验证

建议测试以下场景：
1. 确认"采购中"状态的订单不再显示"确认收货"选项
2. 确认其他状态的订单操作不受影响
3. 确认订单状态流转的其他功能正常工作
4. 确认界面布局和用户体验没有异常

## 回滚方案

如果需要恢复确认收货功能，可以：
1. 恢复被删除的前端代码
2. 重新测试功能完整性
3. 更新相关文档
