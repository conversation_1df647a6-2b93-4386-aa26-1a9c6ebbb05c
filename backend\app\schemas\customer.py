"""
客户相关的Pydantic模型
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, EmailStr
from enum import Enum


class CustomerType(str, Enum):
    """客户类型枚举"""
    INDIVIDUAL = "individual"  # 个人客户
    ENTERPRISE = "enterprise"  # 企业客户
    DISTRIBUTOR = "distributor"  # 分销商


class CustomerLevel(str, Enum):
    """客户等级枚举"""
    VIP = "vip"  # VIP客户
    NORMAL = "normal"  # 普通客户
    POTENTIAL = "potential"  # 潜在客户


class CustomerStatus(str, Enum):
    """客户状态枚举"""
    ACTIVE = "active"  # 活跃
    INACTIVE = "inactive"  # 不活跃
    BLACKLIST = "blacklist"  # 黑名单


class CustomerBase(BaseModel):
    name: str = Field(..., description="客户名称")
    code: str = Field(..., description="客户编码")
    contact_person: Optional[str] = Field(None, description="联系人")
    phone: Optional[str] = Field(None, description="联系电话")
    email: Optional[EmailStr] = Field(None, description="邮箱")
    address: Optional[str] = Field(None, description="地址")
    customer_type: CustomerType = Field(default=CustomerType.INDIVIDUAL, description="客户类型")
    level: CustomerLevel = Field(default=CustomerLevel.NORMAL, description="客户等级")
    credit_limit: float = Field(default=0.0, ge=0, description="信用额度")
    status: CustomerStatus = Field(default=CustomerStatus.ACTIVE, description="客户状态")
    default_delivery_address: Optional[str] = Field(None, description="默认配送地址")
    remark: Optional[str] = Field(None, description="备注")


class CustomerCreate(CustomerBase):
    platform_accounts: Optional[Dict[str, Any]] = Field(None, description="平台账户信息")
    delivery_preferences: Optional[Dict[str, Any]] = Field(None, description="配送偏好")


class CustomerUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    contact_person: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    address: Optional[str] = None
    customer_type: Optional[CustomerType] = None
    level: Optional[CustomerLevel] = None
    credit_limit: Optional[float] = Field(None, ge=0)
    status: Optional[CustomerStatus] = None
    default_delivery_address: Optional[str] = None
    platform_accounts: Optional[Dict[str, Any]] = None
    delivery_preferences: Optional[Dict[str, Any]] = None
    remark: Optional[str] = None


class Customer(CustomerBase):
    id: int
    current_balance: Optional[float] = 0.0
    total_orders: Optional[int] = 0
    total_amount: Optional[float] = 0.0
    is_active: Optional[bool] = True
    platform_accounts: Optional[Dict[str, Any]] = None
    delivery_preferences: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_order_date: Optional[datetime] = None

    class Config:
        from_attributes = True


class CustomerListResponse(BaseModel):
    """客户列表响应"""
    items: List[Customer]
    total: int
    page: int
    page_size: int
    total_pages: int


class CustomerQuery(BaseModel):
    """客户查询参数"""
    name: Optional[str] = Field(None, description="客户名称")
    code: Optional[str] = Field(None, description="客户编码")
    customer_type: Optional[CustomerType] = Field(None, description="客户类型")
    level: Optional[CustomerLevel] = Field(None, description="客户等级")
    status: Optional[CustomerStatus] = Field(None, description="客户状态")
    phone: Optional[str] = Field(None, description="联系电话")
    email: Optional[str] = Field(None, description="邮箱")
    address: Optional[str] = Field(None, description="地址")


class CustomerStats(BaseModel):
    """客户统计信息"""
    total_customers: int = 0
    active_customers: int = 0
    vip_customers: int = 0
    new_customers: int = 0
    total_orders: int = 0
    total_amount: float = 0.0
