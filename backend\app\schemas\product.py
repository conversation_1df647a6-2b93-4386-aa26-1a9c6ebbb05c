from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum

class ProductStatus(str, Enum):
    """商品状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DISCONTINUED = "discontinued"

class ProductBase(BaseModel):
    name: str = Field(..., description="商品名称")
    sku: Optional[str] = Field(None, description="商品SKU")
    category: str = Field(..., description="商品类别")
    price: float = Field(..., ge=0, description="销售价格")
    cost: float = Field(..., ge=0, description="成本价格")
    stock: int = Field(default=0, ge=0, description="库存数量")
    description: Optional[str] = Field(None, description="商品描述")
    image: Optional[str] = Field(None, description="商品图片URL")
    status: ProductStatus = Field(default=ProductStatus.ACTIVE, description="商品状态")

class ProductCreate(ProductBase):
    brand: Optional[str] = Field(None, description="品牌")
    specifications: Optional[str] = Field(None, description="规格参数")
    sales_data: Optional[Dict[str, Any]] = None

class ProductUpdate(BaseModel):
    name: Optional[str] = None
    sku: Optional[str] = None
    category: Optional[str] = None
    price: Optional[float] = Field(None, ge=0)
    cost: Optional[float] = Field(None, ge=0)
    stock: Optional[int] = Field(None, ge=0)
    description: Optional[str] = None
    image: Optional[str] = None
    status: Optional[ProductStatus] = None
    brand: Optional[str] = None
    specifications: Optional[str] = None
    sales_data: Optional[Dict[str, Any]] = None

class Product(ProductBase):
    id: int
    brand: Optional[str] = None
    specifications: Optional[str] = None
    is_active: bool = True
    market_trend: Optional[float] = None
    competition_level: Optional[float] = None
    profit_margin: Optional[float] = None
    recommendation_score: Optional[float] = None
    sales_data: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class ProductListResponse(BaseModel):
    """商品列表响应"""
    items: List[Product]
    total: int
    page: int
    page_size: int
    total_pages: int

class ProductQuery(BaseModel):
    """商品查询参数"""
    name: Optional[str] = Field(None, description="商品名称")
    category: Optional[str] = Field(None, description="商品类别")
    brand: Optional[str] = Field(None, description="品牌")
    status: Optional[ProductStatus] = Field(None, description="商品状态")
    min_price: Optional[float] = Field(None, ge=0, description="最低价格")
    max_price: Optional[float] = Field(None, ge=0, description="最高价格")
    low_stock: Optional[bool] = Field(None, description="是否只显示低库存商品")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")

class ProductAnalysisRequest(BaseModel):
    product_ids: List[int]
    analysis_type: str  # "trend", "competition", "sales", "recommendation"
    parameters: Optional[Dict[str, Any]] = None

class ProductAnalysisResponse(BaseModel):
    product_id: int
    analysis_type: str
    analysis_result: str
    confidence_score: float
    analysis_data: Dict[str, Any]

class ProductRecommendationRequest(BaseModel):
    category: Optional[str] = None
    price_range: Optional[List[float]] = None
    market_conditions: Optional[Dict[str, Any]] = None
    limit: int = 10

class ProductRecommendationResponse(BaseModel):
    products: List[Product]
    recommendation_reasons: Dict[int, str]  # product_id -> reason
