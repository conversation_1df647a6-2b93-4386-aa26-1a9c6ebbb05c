# 环境配置文件示例
# 复制此文件为 .env 并修改相应的配置值

# 环境类型: development, testing, production
ENVIRONMENT=development

# 数据库配置
DB_HOST=*********
DB_PORT=3306
DB_NAME=ecommerce_decision
DB_USER=root
DB_PASSWORD=123456
DB_CHARSET=utf8mb4

# 应用配置
SECRET_KEY=your-secret-key-change-in-production
DEBUG=true

# Redis配置（可选）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
