<template>
  <div class="product-analysis-view">
    <div class="page-header">
      <h2>商品分析</h2>
    </div>

    <!-- 分析配置 -->
    <el-card class="analysis-config">
      <template #header>
        <div class="card-header">
          <el-icon><Setting /></el-icon>
          <span>分析配置</span>
        </div>
      </template>
      
      <el-form :model="analysisForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="选择商品">
              <el-select
                v-model="analysisForm.product_ids"
                multiple
                placeholder="选择要分析的商品"
                style="width: 100%"
              >
                <el-option
                  v-for="product in products"
                  :key="product.id"
                  :label="product.name"
                  :value="product.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="分析类型">
              <el-select v-model="analysisForm.analysis_type" placeholder="选择分析类型">
                <el-option label="市场趋势分析" value="trend" />
                <el-option label="竞争分析" value="competition" />
                <el-option label="销售表现分析" value="sales" />
                <el-option label="推荐潜力分析" value="recommendation" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="">
              <el-button 
                type="primary" 
                @click="startAnalysis"
                :loading="loading"
                :disabled="!analysisForm.product_ids.length || !analysisForm.analysis_type"
              >
                开始分析
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 分析结果 -->
    <el-card class="analysis-results" v-if="analysisResults.length">
      <template #header>
        <div class="card-header">
          <el-icon><DataAnalysis /></el-icon>
          <span>分析结果</span>
        </div>
      </template>
      
      <div class="results-grid">
        <div 
          v-for="result in analysisResults" 
          :key="`${result.product_id}-${result.analysis_type}`"
          class="result-card"
        >
          <div class="result-header">
            <h4>{{ getProductName(result.product_id) }}</h4>
            <el-tag :type="getConfidenceTagType(result.confidence_score)">
              置信度: {{ (result.confidence_score * 100).toFixed(1) }}%
            </el-tag>
          </div>
          
          <div class="result-content">
            <p class="result-text">{{ result.analysis_result }}</p>
            
            <div class="result-data" v-if="result.analysis_data">
              <h5>详细数据:</h5>
              <div class="data-items">
                <div 
                  v-for="(value, key) in result.analysis_data" 
                  :key="key"
                  class="data-item"
                >
                  <span class="data-label">{{ formatDataLabel(key) }}:</span>
                  <span class="data-value">{{ formatDataValue(key, value) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 市场趋势总览 -->
    <el-card class="market-trends">
      <template #header>
        <div class="card-header">
          <el-icon><TrendCharts /></el-icon>
          <span>市场趋势总览</span>
        </div>
      </template>
      
      <el-row :gutter="20" v-if="marketTrends">
        <el-col :span="8">
          <div class="trend-item">
            <div class="trend-label">总商品数</div>
            <div class="trend-value">{{ marketTrends.total_products }}</div>
          </div>
        </el-col>
        
        <el-col :span="8">
          <div class="trend-item">
            <div class="trend-label">平均价格</div>
            <div class="trend-value">¥{{ marketTrends.average_price?.toFixed(2) }}</div>
          </div>
        </el-col>
        
        <el-col :span="8">
          <div class="trend-item">
            <div class="trend-label">价格趋势</div>
            <div class="trend-value">
              <el-tag :type="getTrendTagType(marketTrends.price_trend)">
                {{ marketTrends.price_trend }}
              </el-tag>
            </div>
          </div>
        </el-col>
      </el-row>
      
      <div class="recommendations" v-if="marketTrends?.recommendations">
        <h4>市场建议:</h4>
        <ul>
          <li v-for="(rec, index) in marketTrends.recommendations" :key="index">
            {{ rec }}
          </li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useProductStore } from '../stores/products'
import { ElMessage } from 'element-plus'

const productStore = useProductStore()

const analysisForm = ref({
  product_ids: [],
  analysis_type: ''
})

const products = ref([])
const analysisResults = ref([])
const marketTrends = ref(null)
const loading = ref(false)

const fetchProducts = async () => {
  try {
    await productStore.fetchProducts()
    products.value = productStore.products
  } catch (error) {
    ElMessage.error('获取商品列表失败')
  }
}

const fetchMarketTrends = async () => {
  try {
    const data = await productStore.productApi.getMarketTrends()
    marketTrends.value = data
  } catch (error) {
    console.error('获取市场趋势失败:', error)
  }
}

const startAnalysis = async () => {
  if (!analysisForm.value.product_ids.length || !analysisForm.value.analysis_type) {
    ElMessage.warning('请选择商品和分析类型')
    return
  }
  
  loading.value = true
  try {
    const results = await productStore.analyzeProducts(
      analysisForm.value.product_ids,
      analysisForm.value.analysis_type
    )
    
    if (results) {
      analysisResults.value = results
      ElMessage.success('分析完成')
    }
  } catch (error) {
    ElMessage.error('分析失败')
  } finally {
    loading.value = false
  }
}

const getProductName = (productId: number) => {
  const product = products.value.find(p => p.id === productId)
  return product?.name || `商品 ${productId}`
}

const getConfidenceTagType = (score: number) => {
  if (score >= 0.8) return 'success'
  if (score >= 0.6) return 'warning'
  return 'danger'
}

const getTrendTagType = (trend: string) => {
  if (trend === '上升') return 'success'
  if (trend === '下降') return 'danger'
  return 'info'
}

const formatDataLabel = (key: string) => {
  const labelMap = {
    'trend_score': '趋势评分',
    'market_demand': '市场需求',
    'seasonal_factor': '季节因子',
    'growth_potential': '增长潜力',
    'competition_level': '竞争程度',
    'competitor_count': '竞争对手数量',
    'price_advantage': '价格优势',
    'market_share': '市场份额',
    'performance_score': '表现评分',
    'monthly_sales': '月销量',
    'conversion_rate': '转化率',
    'customer_rating': '客户评分',
    'potential_score': '潜力评分',
    'profit_margin': '利润率',
    'stock_level': '库存水平',
    'category_popularity': '分类热度'
  }
  return labelMap[key] || key
}

const formatDataValue = (key: string, value: any) => {
  if (typeof value === 'number') {
    if (key.includes('rate') || key.includes('margin') || key.includes('share')) {
      return `${(value * 100).toFixed(2)}%`
    }
    if (key.includes('score') || key.includes('factor')) {
      return value.toFixed(2)
    }
    return value.toString()
  }
  return value
}

onMounted(() => {
  fetchProducts()
  fetchMarketTrends()
})
</script>

<style scoped>
.product-analysis-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.analysis-config {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.analysis-results {
  margin-bottom: 20px;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.result-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.result-header h4 {
  margin: 0;
  color: #2c3e50;
}

.result-text {
  margin-bottom: 16px;
  color: #606266;
  line-height: 1.6;
}

.result-data h5 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 14px;
}

.data-items {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 8px;
  background: white;
  border-radius: 4px;
  font-size: 13px;
}

.data-label {
  color: #909399;
}

.data-value {
  color: #2c3e50;
  font-weight: 500;
}

.market-trends {
  margin-bottom: 20px;
}

.trend-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.trend-label {
  color: #7f8c8d;
  margin-bottom: 8px;
}

.trend-value {
  font-size: 1.5em;
  font-weight: bold;
  color: #2c3e50;
}

.recommendations {
  margin-top: 20px;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
}

.recommendations h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
}

.recommendations ul {
  margin: 0;
  padding-left: 20px;
}

.recommendations li {
  margin-bottom: 8px;
  color: #606266;
}
</style>
