import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '../api/index'

export interface User {
  id: number
  username: string
  email: string
  avatar?: string
  role: string
  permissions: string[]
}

export interface LoginCredentials {
  username: string
  password: string
  remember?: boolean
}

export interface RegisterData {
  username: string
  email: string
  password: string
  confirmPassword: string
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const isLoggedIn = ref<boolean>(!!localStorage.getItem('token'))
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const userRole = computed(() => user.value?.role || 'guest')
  const userPermissions = computed(() => user.value?.permissions || [])
  const isAdmin = computed(() => userRole.value === 'admin')
  const isUser = computed(() => userRole.value === 'user')

  // 操作方法
  const login = async (credentials: LoginCredentials) => {
    try {
      loading.value = true
      error.value = null

      // 调用后端登录API
      const response = await api.post('/api/auth/login', {
        username: credentials.username,
        password: credentials.password
      })

      if (response.success) {
        // 保存token和用户信息
        token.value = response.token
        user.value = response.user
        isLoggedIn.value = true

        // 持久化存储
        localStorage.setItem('token', response.token)
        localStorage.setItem('user', JSON.stringify(response.user))

        if (credentials.remember) {
          localStorage.setItem('remember', 'true')
        }

        // 设置API默认header
        api.defaults.headers.common['Authorization'] = `Bearer ${response.token}`

        return { success: true, user: response.user }
      } else {
        error.value = response.message
        return { success: false, message: response.message }
      }
    } catch (err: any) {
      // 如果后端API不可用，回退到模拟登录
      console.warn('后端API不可用，使用模拟登录:', err.message)
      const response = await mockLogin(credentials)

      if (response.success) {
        token.value = response.token
        user.value = response.user
        isLoggedIn.value = true

        localStorage.setItem('token', response.token)
        localStorage.setItem('user', JSON.stringify(response.user))

        if (credentials.remember) {
          localStorage.setItem('remember', 'true')
        }

        api.defaults.headers.common['Authorization'] = `Bearer ${response.token}`

        return { success: true, user: response.user }
      } else {
        error.value = response.message
        return { success: false, message: response.message }
      }
    } finally {
      loading.value = false
    }
  }

  const register = async (data: RegisterData) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await mockRegister(data)
      
      if (response.success) {
        return { success: true, message: '注册成功，请登录' }
      } else {
        error.value = response.message
        return { success: false, message: response.message }
      }
    } catch (err) {
      error.value = '注册失败，请重试'
      return { success: false, message: '注册失败，请重试' }
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    try {
      loading.value = true
      
      // 清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      localStorage.removeItem('remember')
      
      // 清除状态
      token.value = null
      user.value = null
      isLoggedIn.value = false
      
      // 清除API header
      delete api.defaults.headers.common['Authorization']
      
      return { success: true }
    } catch (err) {
      return { success: false, message: '登出失败' }
    } finally {
      loading.value = false
    }
  }

  const refreshToken = async () => {
    try {
      if (!token.value) return { success: false }
      
      // 模拟刷新token
      const response = await mockRefreshToken(token.value)
      
      if (response.success) {
        token.value = response.token
        localStorage.setItem('token', response.token)
        api.defaults.headers.common['Authorization'] = `Bearer ${response.token}`
        return { success: true }
      } else {
        await logout()
        return { success: false }
      }
    } catch (err) {
      await logout()
      return { success: false }
    }
  }

  const fetchUserProfile = async () => {
    try {
      if (!token.value) return { success: false }
      
      loading.value = true
      
      // 模拟获取用户信息
      const response = await mockGetUserProfile(token.value)
      
      if (response.success) {
        user.value = response.user
        localStorage.setItem('user', JSON.stringify(response.user))
        return { success: true, user: response.user }
      } else {
        return { success: false, message: response.message }
      }
    } catch (err) {
      return { success: false, message: '获取用户信息失败' }
    } finally {
      loading.value = false
    }
  }

  const updateProfile = async (profileData: Partial<User>) => {
    try {
      loading.value = true
      error.value = null
      
      // 模拟更新用户信息
      const response = await mockUpdateProfile(profileData)
      
      if (response.success) {
        user.value = { ...user.value, ...response.user }
        localStorage.setItem('user', JSON.stringify(user.value))
        return { success: true, user: user.value }
      } else {
        error.value = response.message
        return { success: false, message: response.message }
      }
    } catch (err) {
      error.value = '更新失败，请重试'
      return { success: false, message: '更新失败，请重试' }
    } finally {
      loading.value = false
    }
  }

  const changePassword = async (oldPassword: string, newPassword: string) => {
    try {
      loading.value = true
      error.value = null
      
      // 模拟修改密码
      const response = await mockChangePassword(oldPassword, newPassword)
      
      if (response.success) {
        return { success: true, message: '密码修改成功' }
      } else {
        error.value = response.message
        return { success: false, message: response.message }
      }
    } catch (err) {
      error.value = '修改密码失败，请重试'
      return { success: false, message: '修改密码失败，请重试' }
    } finally {
      loading.value = false
    }
  }

  const resetPassword = async (email: string) => {
    try {
      loading.value = true
      error.value = null
      
      // 模拟重置密码
      const response = await mockResetPassword(email)
      
      if (response.success) {
        return { success: true, message: '重置链接已发送到您的邮箱' }
      } else {
        error.value = response.message
        return { success: false, message: response.message }
      }
    } catch (err) {
      error.value = '发送重置链接失败，请重试'
      return { success: false, message: '发送重置链接失败，请重试' }
    } finally {
      loading.value = false
    }
  }

  const checkPermission = (permission: string) => {
    return userPermissions.value.includes(permission) || isAdmin.value
  }

  const initializeAuth = () => {
    const storedToken = localStorage.getItem('token')
    const storedUser = localStorage.getItem('user')
    
    if (storedToken && storedUser) {
      token.value = storedToken
      user.value = JSON.parse(storedUser)
      isLoggedIn.value = true
      api.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    user,
    token,
    isLoggedIn,
    loading,
    error,
    
    // 计算属性
    userRole,
    userPermissions,
    isAdmin,
    isUser,
    
    // 方法
    login,
    register,
    logout,
    refreshToken,
    fetchUserProfile,
    updateProfile,
    changePassword,
    resetPassword,
    checkPermission,
    initializeAuth,
    clearError
  }
})

// 模拟API函数
const mockLogin = async (credentials: LoginCredentials) => {
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 模拟用户数据
  const users = [
    {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      password: '123456',
      role: 'admin',
      permissions: ['read', 'write', 'delete', 'admin'],
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    }
  ]
  
  const user = users.find(u => 
    (u.username === credentials.username || u.email === credentials.username) && 
    u.password === credentials.password
  )
  
  if (user) {
    const { password, ...userWithoutPassword } = user
    return {
      success: true,
      token: `mock-token-${user.id}-${Date.now()}`,
      user: userWithoutPassword
    }
  } else {
    return {
      success: false,
      message: '用户名或密码错误'
    }
  }
}

const mockRegister = async (data: RegisterData) => {
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  if (data.password !== data.confirmPassword) {
    return { success: false, message: '两次输入的密码不一致' }
  }
  
  return { success: true, message: '注册成功' }
}

const mockRefreshToken = async (token: string) => {
  await new Promise(resolve => setTimeout(resolve, 500))
  return {
    success: true,
    token: `refreshed-${token}-${Date.now()}`
  }
}

const mockGetUserProfile = async (token: string) => {
  await new Promise(resolve => setTimeout(resolve, 500))
  return {
    success: true,
    user: {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin',
      permissions: ['read', 'write', 'delete', 'admin'],
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    }
  }
}

const mockUpdateProfile = async (profileData: Partial<User>) => {
  await new Promise(resolve => setTimeout(resolve, 500))
  return {
    success: true,
    user: profileData
  }
}

const mockChangePassword = async (oldPassword: string, newPassword: string) => {
  await new Promise(resolve => setTimeout(resolve, 500))
  return { success: true }
}

const mockResetPassword = async (email: string) => {
  await new Promise(resolve => setTimeout(resolve, 500))
  return { success: true }
}
