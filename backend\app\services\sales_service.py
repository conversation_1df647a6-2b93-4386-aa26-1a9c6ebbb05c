"""
销售管理服务 - 包含销售订单、销售出库单
"""

from typing import List, Tuple, Optional
from datetime import datetime
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from app.models.sales import (
    SalesOrder, SalesOrderItem,
    SalesOutbound, SalesOutboundItem
)
from app.models.customer import Customer
from app.models.product import Product
from app.models.warehouse import Warehouse
from app.schemas.sales import (
    SalesOrderCreate, SalesOrderUpdate, SalesOrderQuery, SalesOrderStats,
    SalesOutboundCreate, SalesOutboundStatus
)


class SalesService:
    """销售管理服务 - 包含销售订单、销售出库单"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_sales_orders(
        self, 
        skip: int = 0, 
        limit: int = 20, 
        query: Optional[SalesOrderQuery] = None
    ) -> Tuple[List[SalesOrder], int]:
        """获取销售订单列表"""
        
        # 构建查询
        db_query = self.db.query(SalesOrder).options(
            joinedload(SalesOrder.customer),
            joinedload(SalesOrder.items).joinedload(SalesOrderItem.product)
        )
        
        # 应用筛选条件
        if query:
            if query.order_no:
                db_query = db_query.filter(SalesOrder.order_no.ilike(f"%{query.order_no}%"))
            if query.customer_id:
                db_query = db_query.filter(SalesOrder.customer_id == query.customer_id)
            if query.status:
                db_query = db_query.filter(SalesOrder.status == query.status)
            if query.sales_person:
                db_query = db_query.filter(SalesOrder.sales_person.ilike(f"%{query.sales_person}%"))
            if query.platform:
                db_query = db_query.filter(SalesOrder.platform == query.platform)
            if query.start_date:
                db_query = db_query.filter(SalesOrder.created_at >= query.start_date)
            if query.end_date:
                db_query = db_query.filter(SalesOrder.created_at <= query.end_date)
        
        # 获取总数
        total = db_query.count()
        
        # 分页和排序
        orders = db_query.order_by(desc(SalesOrder.created_at)).offset(skip).limit(limit).all()
        
        return orders, total
    
    def get_sales_order(self, order_id: int) -> Optional[SalesOrder]:
        """获取单个销售订单"""
        return self.db.query(SalesOrder).options(
            joinedload(SalesOrder.customer),
            joinedload(SalesOrder.items).joinedload(SalesOrderItem.product)
        ).filter(SalesOrder.id == order_id).first()
    
    def create_sales_order(self, order_data: SalesOrderCreate) -> SalesOrder:
        """创建销售订单"""
        
        # 生成订单号
        order_no = self._generate_order_no()
        
        # 验证客户是否存在
        customer = self.db.query(Customer).filter(Customer.id == order_data.customer_id).first()
        if not customer:
            raise ValueError(f"客户ID {order_data.customer_id} 不存在")
        
        # 创建订单
        db_order = SalesOrder(
            order_no=order_no,
            customer_id=order_data.customer_id,
            total_amount=order_data.total_amount,
            status=order_data.status,
            delivery_date=order_data.delivery_date,
            delivery_address=order_data.delivery_address,
            sales_person=order_data.sales_person,
            platform=order_data.platform,
            original_order_no=order_data.original_order_no,
            remark=order_data.remark
        )
        
        self.db.add(db_order)
        self.db.flush()  # 获取订单ID
        
        # 创建订单明细
        for item_data in order_data.items:
            # 验证商品是否存在
            product = self.db.query(Product).filter(Product.id == item_data.product_id).first()
            if not product:
                raise ValueError(f"商品ID {item_data.product_id} 不存在")
            
            # 创建订单明细
            db_item = SalesOrderItem(
                order_id=db_order.id,
                product_id=item_data.product_id,
                product_name=product.name,
                product_sku=product.sku,
                quantity=item_data.quantity,
                unit_price=item_data.unit_price,
                total_price=item_data.total_price
            )
            self.db.add(db_item)
        
        self.db.commit()
        self.db.refresh(db_order)
        
        return db_order
    
    def update_sales_order(self, order_id: int, order_data: SalesOrderUpdate) -> Optional[SalesOrder]:
        """更新销售订单（仅更新基本信息，不包括明细）"""

        db_order = self.db.query(SalesOrder).filter(SalesOrder.id == order_id).first()
        if not db_order:
            return None

        # 更新字段
        update_data = order_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_order, field, value)

        db_order.updated_at = datetime.now()

        self.db.commit()
        self.db.refresh(db_order)

        return db_order

    def update_sales_order_with_items(self, order_id: int, order_data: SalesOrderCreate) -> Optional[SalesOrder]:
        """更新销售订单（包括明细）"""

        db_order = self.db.query(SalesOrder).filter(SalesOrder.id == order_id).first()
        if not db_order:
            return None

        # 检查订单状态，只有待处理状态的订单才能修改明细
        if db_order.status not in ["pending"]:
            raise ValueError("只有待处理状态的订单才能修改明细")

        # 验证客户是否存在
        customer = self.db.query(Customer).filter(Customer.id == order_data.customer_id).first()
        if not customer:
            raise ValueError(f"客户ID {order_data.customer_id} 不存在")

        # 更新订单基本信息
        db_order.customer_id = order_data.customer_id
        db_order.total_amount = order_data.total_amount
        db_order.status = order_data.status
        db_order.delivery_date = order_data.delivery_date
        db_order.delivery_address = order_data.delivery_address
        db_order.sales_person = order_data.sales_person
        db_order.platform = order_data.platform
        db_order.original_order_no = order_data.original_order_no
        db_order.remark = order_data.remark
        db_order.updated_at = datetime.now()

        # 删除原有明细
        self.db.query(SalesOrderItem).filter(SalesOrderItem.order_id == order_id).delete()

        # 创建新的订单明细
        for item_data in order_data.items:
            # 验证商品是否存在
            product = self.db.query(Product).filter(Product.id == item_data.product_id).first()
            if not product:
                raise ValueError(f"商品ID {item_data.product_id} 不存在")

            # 创建订单明细
            db_item = SalesOrderItem(
                order_id=db_order.id,
                product_id=item_data.product_id,
                product_name=product.name,
                product_sku=product.sku,
                quantity=item_data.quantity,
                unit_price=item_data.unit_price,
                total_price=item_data.total_price
            )
            self.db.add(db_item)

        self.db.commit()
        self.db.refresh(db_order)

        return db_order
    
    def delete_sales_order(self, order_id: int) -> bool:
        """删除销售订单"""
        
        db_order = self.db.query(SalesOrder).filter(SalesOrder.id == order_id).first()
        if not db_order:
            return False
        
        # 检查订单状态，只有待处理状态的订单才能删除
        if db_order.status not in ["pending"]:
            raise ValueError("只有待处理状态的订单才能删除")
        
        self.db.delete(db_order)
        self.db.commit()
        
        return True
    
    def update_order_status(self, order_id: int, status: str) -> Optional[SalesOrder]:
        """更新订单状态"""
        
        db_order = self.db.query(SalesOrder).filter(SalesOrder.id == order_id).first()
        if not db_order:
            return None
        
        db_order.status = status
        db_order.updated_at = datetime.now()
        
        self.db.commit()
        self.db.refresh(db_order)
        
        return db_order
    
    def get_sales_stats(self) -> SalesOrderStats:
        """获取销售订单统计"""
        
        # 基础统计
        total_orders = self.db.query(SalesOrder).count()
        
        # 按状态统计
        pending_orders = self.db.query(SalesOrder).filter(SalesOrder.status == "pending").count()
        processing_orders = self.db.query(SalesOrder).filter(
            SalesOrder.status.in_(["confirmed", "producing", "ready_to_ship"])
        ).count()
        completed_orders = self.db.query(SalesOrder).filter(SalesOrder.status == "completed").count()
        cancelled_orders = self.db.query(SalesOrder).filter(SalesOrder.status == "cancelled").count()
        
        # 金额统计
        total_amount_result = self.db.query(func.sum(SalesOrder.total_amount)).filter(
            SalesOrder.status != "cancelled"
        ).scalar()
        total_amount = total_amount_result or 0
        
        # 平均订单金额
        average_order_amount = total_amount / max(1, total_orders - cancelled_orders)
        
        return SalesOrderStats(
            total_orders=total_orders,
            pending_orders=pending_orders,
            processing_orders=processing_orders,
            completed_orders=completed_orders,
            cancelled_orders=cancelled_orders,
            total_amount=total_amount,
            average_order_amount=average_order_amount
        )
    
    def _generate_order_no(self) -> str:
        """生成订单号"""
        today = datetime.now().strftime("%Y%m%d")
        
        # 查询今天已有的订单数量
        count = self.db.query(SalesOrder).filter(
            SalesOrder.order_no.like(f"SO{today}%")
        ).count()
        
        # 生成新的订单号
        sequence = str(count + 1).zfill(4)
        return f"SO{today}{sequence}"

    # ==================== 销售出库单相关方法 ====================

    def get_sales_outbounds(
        self,
        skip: int = 0,
        limit: int = 20,
        outbound_no: Optional[str] = None,
        customer_id: Optional[int] = None,
        warehouse_id: Optional[int] = None,
        status: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Tuple[List[SalesOutbound], int]:
        """获取销售出库单列表"""

        # 构建查询
        db_query = self.db.query(SalesOutbound).options(
            joinedload(SalesOutbound.customer),
            joinedload(SalesOutbound.warehouse),
            joinedload(SalesOutbound.sales_order),
            joinedload(SalesOutbound.items).joinedload(SalesOutboundItem.product)
        )

        # 应用筛选条件
        if outbound_no:
            db_query = db_query.filter(SalesOutbound.outbound_no.ilike(f"%{outbound_no}%"))
        if customer_id:
            db_query = db_query.filter(SalesOutbound.customer_id == customer_id)
        if warehouse_id:
            db_query = db_query.filter(SalesOutbound.warehouse_id == warehouse_id)
        if status:
            db_query = db_query.filter(SalesOutbound.status == status)
        if start_date:
            db_query = db_query.filter(SalesOutbound.created_at >= start_date)
        if end_date:
            db_query = db_query.filter(SalesOutbound.created_at <= end_date)

        # 获取总数
        total = db_query.count()

        # 分页和排序
        outbounds = db_query.order_by(desc(SalesOutbound.created_at)).offset(skip).limit(limit).all()

        return outbounds, total

    def get_sales_outbound(self, outbound_id: int) -> Optional[SalesOutbound]:
        """获取单个销售出库单"""
        return self.db.query(SalesOutbound).options(
            joinedload(SalesOutbound.customer),
            joinedload(SalesOutbound.warehouse),
            joinedload(SalesOutbound.sales_order),
            joinedload(SalesOutbound.items).joinedload(SalesOutboundItem.product)
        ).filter(SalesOutbound.id == outbound_id).first()

    def create_sales_outbound(self, outbound_data: SalesOutboundCreate, created_by: str) -> SalesOutbound:
        """创建销售出库单"""

        # 生成出库单号
        outbound_no = self._generate_outbound_no()

        # 验证客户是否存在
        customer = self.db.query(Customer).filter(Customer.id == outbound_data.customer_id).first()
        if not customer:
            raise ValueError(f"客户ID {outbound_data.customer_id} 不存在")

        # 验证仓库是否存在
        warehouse = self.db.query(Warehouse).filter(Warehouse.id == outbound_data.warehouse_id).first()
        if not warehouse:
            raise ValueError(f"仓库ID {outbound_data.warehouse_id} 不存在")

        # 如果指定了销售订单，验证订单是否存在
        sales_order = None
        if outbound_data.sales_order_id:
            sales_order = self.db.query(SalesOrder).filter(SalesOrder.id == outbound_data.sales_order_id).first()
            if not sales_order:
                raise ValueError(f"销售订单ID {outbound_data.sales_order_id} 不存在")

        # 创建出库单
        db_outbound = SalesOutbound(
            outbound_no=outbound_no,
            sales_order_id=outbound_data.sales_order_id,
            sales_order_no=outbound_data.sales_order_no,
            customer_id=outbound_data.customer_id,
            warehouse_id=outbound_data.warehouse_id,
            status=outbound_data.status,
            outbound_date=outbound_data.outbound_date,
            created_by=created_by,
            remark=outbound_data.remark
        )

        self.db.add(db_outbound)
        self.db.flush()  # 获取出库单ID

        # 创建出库单明细
        for item_data in outbound_data.items:
            # 验证商品是否存在
            product = self.db.query(Product).filter(Product.id == item_data.product_id).first()
            if not product:
                raise ValueError(f"商品ID {item_data.product_id} 不存在")

            # 创建出库单明细
            db_item = SalesOutboundItem(
                outbound_id=db_outbound.id,
                product_id=item_data.product_id,
                product_name=product.name,
                product_sku=product.sku,
                quantity=item_data.quantity,
                outbound_quantity=0  # 初始实出数量为0
            )
            self.db.add(db_item)

        self.db.commit()
        self.db.refresh(db_outbound)

        return db_outbound

    def update_sales_outbound(self, outbound_id: int, outbound_data: SalesOutboundCreate) -> Optional[SalesOutbound]:
        """更新销售出库单"""

        db_outbound = self.db.query(SalesOutbound).filter(SalesOutbound.id == outbound_id).first()
        if not db_outbound:
            return None

        # 检查出库单状态，只有草稿状态的出库单才能修改
        if db_outbound.status not in ["pending"]:
            raise ValueError("只有草稿状态的出库单才能修改")

        # 验证客户是否存在
        customer = self.db.query(Customer).filter(Customer.id == outbound_data.customer_id).first()
        if not customer:
            raise ValueError(f"客户ID {outbound_data.customer_id} 不存在")

        # 验证仓库是否存在
        warehouse = self.db.query(Warehouse).filter(Warehouse.id == outbound_data.warehouse_id).first()
        if not warehouse:
            raise ValueError(f"仓库ID {outbound_data.warehouse_id} 不存在")

        # 更新出库单基本信息
        db_outbound.sales_order_id = outbound_data.sales_order_id
        db_outbound.sales_order_no = outbound_data.sales_order_no
        db_outbound.customer_id = outbound_data.customer_id
        db_outbound.warehouse_id = outbound_data.warehouse_id
        db_outbound.status = outbound_data.status
        db_outbound.outbound_date = outbound_data.outbound_date
        db_outbound.remark = outbound_data.remark
        db_outbound.updated_at = datetime.now()

        # 删除原有明细
        self.db.query(SalesOutboundItem).filter(SalesOutboundItem.outbound_id == outbound_id).delete()

        # 创建新的出库单明细
        for item_data in outbound_data.items:
            # 验证商品是否存在
            product = self.db.query(Product).filter(Product.id == item_data.product_id).first()
            if not product:
                raise ValueError(f"商品ID {item_data.product_id} 不存在")

            # 创建出库单明细
            db_item = SalesOutboundItem(
                outbound_id=db_outbound.id,
                product_id=item_data.product_id,
                product_name=product.name,
                product_sku=product.sku,
                quantity=item_data.quantity,
                outbound_quantity=0  # 初始实出数量为0
            )
            self.db.add(db_item)

        self.db.commit()
        self.db.refresh(db_outbound)

        return db_outbound

    def delete_sales_outbound(self, outbound_id: int) -> bool:
        """删除销售出库单"""

        db_outbound = self.db.query(SalesOutbound).filter(SalesOutbound.id == outbound_id).first()
        if not db_outbound:
            return False

        # 检查出库单状态，只有草稿状态的出库单才能删除
        if db_outbound.status not in ["pending"]:
            raise ValueError("只有草稿状态的出库单才能删除")

        self.db.delete(db_outbound)
        self.db.commit()

        return True

    def submit_sales_outbound(self, outbound_id: int, submitted_by: str) -> Optional[SalesOutbound]:
        """提交销售出库单"""

        db_outbound = self.db.query(SalesOutbound).filter(SalesOutbound.id == outbound_id).first()
        if not db_outbound:
            return None

        # 只能提交草稿状态的出库单
        if db_outbound.status != "pending":
            raise ValueError("只能提交草稿状态的出库单")

        # 验证出库单数据完整性
        if not db_outbound.items:
            raise ValueError("出库单必须包含商品明细")

        for item in db_outbound.items:
            if item.quantity <= 0:
                raise ValueError(f"商品 {item.product_name} 的数量必须大于0")

        # 更新状态
        db_outbound.status = "submitted"
        db_outbound.updated_at = datetime.now()

        self.db.commit()
        self.db.refresh(db_outbound)

        return db_outbound

    def approve_sales_outbound(self, outbound_id: int, approved_by: str) -> Optional[SalesOutbound]:
        """审核通过销售出库单"""

        db_outbound = self.db.query(SalesOutbound).filter(SalesOutbound.id == outbound_id).first()
        if not db_outbound:
            return None

        # 只能审核已提交状态的出库单
        if db_outbound.status != "submitted":
            raise ValueError("只能审核已提交状态的出库单")

        # 更新状态
        db_outbound.status = "approved"
        db_outbound.updated_at = datetime.now()

        self.db.commit()
        self.db.refresh(db_outbound)

        return db_outbound

    def reject_sales_outbound(self, outbound_id: int, rejected_by: str, reject_reason: str = None) -> Optional[SalesOutbound]:
        """审核拒绝销售出库单"""

        db_outbound = self.db.query(SalesOutbound).filter(SalesOutbound.id == outbound_id).first()
        if not db_outbound:
            return None

        # 只能审核已提交状态的出库单
        if db_outbound.status != "submitted":
            raise ValueError("只能审核已提交状态的出库单")

        # 更新状态
        db_outbound.status = "rejected"
        if reject_reason:
            db_outbound.remark = f"{db_outbound.remark or ''}\n拒绝原因: {reject_reason}".strip()
        db_outbound.updated_at = datetime.now()

        self.db.commit()
        self.db.refresh(db_outbound)

        return db_outbound

    def get_available_sales_orders(self, customer_id: Optional[int] = None) -> List[SalesOrder]:
        """获取可用的销售订单（已确认且未完全出库的订单）"""

        query = self.db.query(SalesOrder).options(
            joinedload(SalesOrder.customer),
            joinedload(SalesOrder.items)
        ).filter(SalesOrder.status.in_(["confirmed", "producing", "ready_to_ship"]))

        if customer_id:
            query = query.filter(SalesOrder.customer_id == customer_id)

        return query.order_by(desc(SalesOrder.created_at)).all()

    def _generate_outbound_no(self) -> str:
        """生成出库单号"""
        today = datetime.now().strftime("%Y%m%d")

        # 查询今天已有的出库单数量
        count = self.db.query(SalesOutbound).filter(
            SalesOutbound.outbound_no.like(f"SO{today}%")
        ).count()

        # 生成新的出库单号
        sequence = str(count + 1).zfill(4)
        return f"SO{today}{sequence}"
