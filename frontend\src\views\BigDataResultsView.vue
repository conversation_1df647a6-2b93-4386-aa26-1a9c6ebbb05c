<template>
  <div class="bigdata-results-view">
    <div class="page-header">
      <div class="header-left">
        <h2>大数据分析结果</h2>
        <p class="analysis-info">
          分析时间: {{ analysisInfo.analysis_time }} | 
          数据源: {{ analysisInfo.data_sources.join(', ') }} | 
          商品数量: {{ analysisInfo.total_products.toLocaleString() }}
        </p>
      </div>
      <div class="header-right">
        <el-button @click="exportResults">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
        <el-button type="primary" @click="$router.push('/products/bigdata-analysis')">
          <el-icon><Refresh /></el-icon>
          重新分析
        </el-button>
      </div>
    </div>

    <!-- 分析概览 -->
    <el-row :gutter="20" class="analysis-overview">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon success">
              <el-icon size="32"><TrendCharts /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ overviewStats.trending_products }}</div>
              <div class="overview-label">热门商品</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon warning">
              <el-icon size="32"><Star /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ overviewStats.recommended_products }}</div>
              <div class="overview-label">推荐商品</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon danger">
              <el-icon size="32"><Warning /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ overviewStats.risk_products }}</div>
              <div class="overview-label">风险商品</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon info">
              <el-icon size="32"><DataAnalysis /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ overviewStats.analysis_score.toFixed(1) }}</div>
              <div class="overview-label">综合评分</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 热门商品排行 -->
    <el-card class="trending-products-card">
      <template #header>
        <div class="card-header">
          <el-icon><TrendCharts /></el-icon>
          <span>热门商品排行榜</span>
        </div>
      </template>
      
      <el-table :data="trendingProducts" style="width: 100%">
        <el-table-column type="index" label="排名" width="80">
          <template #default="{ $index }">
            <div class="rank-badge" :class="getRankClass($index)">
              {{ $index + 1 }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="name" label="商品名称" min-width="200">
          <template #default="{ row }">
            <div class="product-info">
              <img :src="row.image" :alt="row.name" class="product-image" />
              <div class="product-details">
                <div class="product-name">{{ row.name }}</div>
                <div class="product-category">{{ row.category }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="price" label="价格" width="120">
          <template #default="{ row }">
            <span class="price">¥{{ row.price.toFixed(2) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="trend_score" label="热度评分" width="120">
          <template #default="{ row }">
            <el-progress 
              :percentage="row.trend_score" 
              :color="getScoreColor(row.trend_score)"
              :show-text="false"
              style="width: 80px"
            />
            <span class="score-text">{{ row.trend_score }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="sales_growth" label="销量增长" width="120">
          <template #default="{ row }">
            <el-tag :type="row.sales_growth > 0 ? 'success' : 'danger'">
              {{ row.sales_growth > 0 ? '+' : '' }}{{ row.sales_growth.toFixed(1) }}%
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="competition_level" label="竞争程度" width="120">
          <template #default="{ row }">
            <el-tag :type="getCompetitionTagType(row.competition_level)">
              {{ getCompetitionLabel(row.competition_level) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="data_sources" label="数据源" width="150">
          <template #default="{ row }">
            <div class="data-sources">
              <el-tag 
                v-for="source in row.data_sources" 
                :key="source"
                size="small"
                class="source-tag"
              >
                {{ getSourceLabel(source) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="viewProductDetails(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 市场洞察 -->
    <el-row :gutter="20" class="market-insights">
      <el-col :span="12">
        <el-card class="insights-card">
          <template #header>
            <div class="card-header">
              <el-icon><DataBoard /></el-icon>
              <span>价格趋势分析</span>
            </div>
          </template>
          
          <div class="price-trends">
            <div class="trend-item" v-for="trend in priceTrends" :key="trend.category">
              <div class="trend-header">
                <span class="category-name">{{ trend.category }}</span>
                <el-tag :type="trend.trend === 'up' ? 'success' : trend.trend === 'down' ? 'danger' : 'info'">
                  {{ trend.trend === 'up' ? '上涨' : trend.trend === 'down' ? '下跌' : '稳定' }}
                </el-tag>
              </div>
              <div class="trend-details">
                <span>平均价格: ¥{{ trend.avg_price.toFixed(2) }}</span>
                <span>变化幅度: {{ trend.change_rate > 0 ? '+' : '' }}{{ trend.change_rate.toFixed(1) }}%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="insights-card">
          <template #header>
            <div class="card-header">
              <el-icon><Warning /></el-icon>
              <span>风险预警</span>
            </div>
          </template>
          
          <div class="risk-alerts">
            <div class="alert-item" v-for="alert in riskAlerts" :key="alert.id">
              <div class="alert-header">
                <el-icon :color="getRiskColor(alert.level)">
                  <Warning />
                </el-icon>
                <span class="alert-title">{{ alert.title }}</span>
                <el-tag :type="getRiskTagType(alert.level)" size="small">
                  {{ getRiskLabel(alert.level) }}
                </el-tag>
              </div>
              <div class="alert-content">
                {{ alert.description }}
              </div>
              <div class="alert-suggestion">
                <strong>建议:</strong> {{ alert.suggestion }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 智能推荐 -->
    <el-card class="recommendations-card">
      <template #header>
        <div class="card-header">
          <el-icon><Star /></el-icon>
          <span>AI智能推荐</span>
        </div>
      </template>
      
      <div class="recommendations-grid">
        <div 
          v-for="recommendation in recommendations" 
          :key="recommendation.id"
          class="recommendation-card"
        >
          <div class="recommendation-header">
            <img :src="recommendation.image" :alt="recommendation.name" class="recommendation-image" />
            <div class="recommendation-info">
              <h4 class="recommendation-name">{{ recommendation.name }}</h4>
              <p class="recommendation-category">{{ recommendation.category }}</p>
              <div class="recommendation-price">¥{{ recommendation.price.toFixed(2) }}</div>
            </div>
          </div>
          
          <div class="recommendation-metrics">
            <div class="metric">
              <span class="metric-label">推荐指数</span>
              <el-progress 
                :percentage="recommendation.recommendation_score" 
                color="#67C23A"
                :show-text="false"
              />
              <span class="metric-value">{{ recommendation.recommendation_score }}/100</span>
            </div>
            
            <div class="metric">
              <span class="metric-label">利润预期</span>
              <span class="metric-value profit">{{ recommendation.profit_margin.toFixed(1) }}%</span>
            </div>
            
            <div class="metric">
              <span class="metric-label">市场需求</span>
              <span class="metric-value">{{ recommendation.market_demand }}</span>
            </div>
          </div>
          
          <div class="recommendation-reason">
            <strong>推荐理由:</strong>
            <p>{{ recommendation.reason }}</p>
          </div>
          
          <div class="recommendation-actions">
            <el-button size="small" @click="addToWatchlist(recommendation)">
              加入关注
            </el-button>
            <el-button size="small" type="primary" @click="viewRecommendationDetails(recommendation)">
              查看详情
            </el-button>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Download,
  Refresh,
  TrendCharts,
  Star,
  Warning,
  DataAnalysis,
  DataBoard
} from '@element-plus/icons-vue'

// 响应式数据
const analysisInfo = ref({
  analysis_time: '2024-07-18 15:30:00',
  data_sources: ['淘宝', '京东', '拼多多'],
  total_products: 4240000
})

const overviewStats = ref({
  trending_products: 1250,
  recommended_products: 89,
  risk_products: 23,
  analysis_score: 8.7
})

const trendingProducts = ref([])
const priceTrends = ref([])
const riskAlerts = ref([])
const recommendations = ref([])

// 方法
const fetchAnalysisResults = async () => {
  // 模拟获取分析结果数据
  trendingProducts.value = [
    {
      id: 1,
      name: 'iPhone 15 Pro Max',
      category: '电子产品',
      price: 9999,
      trend_score: 95,
      sales_growth: 23.5,
      competition_level: 'high',
      data_sources: ['taobao', 'jd', 'pdd'],
      image: 'https://picsum.photos/60/60?random=1'
    },
    {
      id: 2,
      name: '小米14 Ultra',
      category: '电子产品',
      price: 5999,
      trend_score: 88,
      sales_growth: 18.2,
      competition_level: 'medium',
      data_sources: ['taobao', 'jd'],
      image: 'https://picsum.photos/60/60?random=2'
    },
    // 更多数据...
  ]

  priceTrends.value = [
    {
      category: '电子产品',
      trend: 'up',
      avg_price: 3250.50,
      change_rate: 5.2
    },
    {
      category: '服装鞋帽',
      trend: 'down',
      avg_price: 189.99,
      change_rate: -2.1
    },
    // 更多数据...
  ]

  riskAlerts.value = [
    {
      id: 1,
      level: 'high',
      title: '电子产品价格波动异常',
      description: '检测到电子产品类别价格在过去7天内出现异常波动，平均涨幅超过15%',
      suggestion: '建议暂缓进货，观察市场走势'
    },
    // 更多数据...
  ]

  recommendations.value = [
    {
      id: 1,
      name: '无线蓝牙耳机',
      category: '电子配件',
      price: 299.99,
      recommendation_score: 92,
      profit_margin: 35.5,
      market_demand: '高',
      reason: '基于大数据分析，该商品在多个平台销量持续增长，用户评价良好，竞争适中，具有较高的盈利潜力。',
      image: 'https://picsum.photos/120/120?random=3'
    },
    // 更多数据...
  ]
}

const getRankClass = (index: number) => {
  if (index === 0) return 'rank-gold'
  if (index === 1) return 'rank-silver'
  if (index === 2) return 'rank-bronze'
  return 'rank-normal'
}

const getScoreColor = (score: number) => {
  if (score >= 80) return '#67C23A'
  if (score >= 60) return '#E6A23C'
  return '#F56C6C'
}

const getCompetitionTagType = (level: string) => {
  const typeMap = {
    'low': 'success',
    'medium': 'warning',
    'high': 'danger'
  }
  return typeMap[level] || 'info'
}

const getCompetitionLabel = (level: string) => {
  const labelMap = {
    'low': '低',
    'medium': '中',
    'high': '高'
  }
  return labelMap[level] || level
}

const getSourceLabel = (source: string) => {
  const labelMap = {
    'taobao': '淘宝',
    'jd': '京东',
    'pdd': '拼多多',
    'tmall': '天猫'
  }
  return labelMap[source] || source
}

const getRiskColor = (level: string) => {
  const colorMap = {
    'low': '#67C23A',
    'medium': '#E6A23C',
    'high': '#F56C6C'
  }
  return colorMap[level] || '#909399'
}

const getRiskTagType = (level: string) => {
  const typeMap = {
    'low': 'success',
    'medium': 'warning',
    'high': 'danger'
  }
  return typeMap[level] || 'info'
}

const getRiskLabel = (level: string) => {
  const labelMap = {
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险'
  }
  return labelMap[level] || level
}

const exportResults = () => {
  ElMessage.success('分析报告导出成功')
}

const viewProductDetails = (product: any) => {
  ElMessage.info(`查看商品详情: ${product.name}`)
}

const addToWatchlist = (product: any) => {
  ElMessage.success(`已将 ${product.name} 加入关注列表`)
}

const viewRecommendationDetails = (recommendation: any) => {
  ElMessage.info(`查看推荐详情: ${recommendation.name}`)
}

onMounted(() => {
  fetchAnalysisResults()
})
</script>

<style scoped>
.bigdata-results-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
}

.analysis-info {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 分析概览 */
.analysis-overview {
  margin-bottom: 24px;
}

.overview-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.overview-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.overview-icon {
  padding: 12px;
  border-radius: 12px;
  flex-shrink: 0;
}

.overview-icon.success {
  background: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.overview-icon.warning {
  background: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.overview-icon.danger {
  background: rgba(245, 108, 108, 0.1);
  color: #F56C6C;
}

.overview-icon.info {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 14px;
  color: #7f8c8d;
}

/* 卡片样式 */
.trending-products-card,
.insights-card,
.recommendations-card {
  margin-bottom: 24px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #2c3e50;
}

/* 排名徽章 */
.rank-badge {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
  color: white;
}

.rank-gold {
  background: linear-gradient(135deg, #FFD700, #FFA500);
}

.rank-silver {
  background: linear-gradient(135deg, #C0C0C0, #A0A0A0);
}

.rank-bronze {
  background: linear-gradient(135deg, #CD7F32, #B8860B);
}

.rank-normal {
  background: #909399;
}

/* 商品信息 */
.product-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
}

.product-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.product-category {
  font-size: 12px;
  color: #909399;
}

.price {
  font-weight: 600;
  color: #E6A23C;
}

.score-text {
  margin-left: 8px;
  font-size: 12px;
  color: #606266;
}

/* 数据源标签 */
.data-sources {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.source-tag {
  margin: 0;
}

/* 市场洞察 */
.market-insights {
  margin-bottom: 24px;
}

.price-trends {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.trend-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.trend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.category-name {
  font-weight: 600;
  color: #2c3e50;
}

.trend-details {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #606266;
}

/* 风险预警 */
.risk-alerts {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.alert-item {
  padding: 16px;
  background: #fef0f0;
  border-radius: 8px;
  border-left: 4px solid #F56C6C;
}

.alert-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.alert-title {
  font-weight: 600;
  color: #2c3e50;
  flex: 1;
}

.alert-content {
  margin-bottom: 8px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.alert-suggestion {
  font-size: 14px;
  color: #E6A23C;
}

/* 智能推荐 */
.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.recommendation-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.recommendation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.recommendation-header {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.recommendation-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
}

.recommendation-name {
  margin: 0 0 4px 0;
  color: #2c3e50;
  font-size: 16px;
}

.recommendation-category {
  margin: 0 0 8px 0;
  color: #909399;
  font-size: 12px;
}

.recommendation-price {
  font-weight: 700;
  color: #E6A23C;
  font-size: 18px;
}

.recommendation-metrics {
  margin-bottom: 16px;
}

.metric {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.metric-label {
  font-size: 14px;
  color: #606266;
}

.metric-value {
  font-weight: 600;
  color: #2c3e50;
}

.metric-value.profit {
  color: #67C23A;
}

.recommendation-reason {
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
}

.recommendation-reason strong {
  color: #2c3e50;
}

.recommendation-reason p {
  margin: 4px 0 0 0;
  color: #606266;
}

.recommendation-actions {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .analysis-overview .el-col,
  .market-insights .el-col {
    margin-bottom: 16px;
  }

  .recommendations-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .bigdata-results-view {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    justify-content: flex-start;
  }

  .overview-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .product-info {
    flex-direction: column;
    text-align: center;
  }

  .trend-details {
    flex-direction: column;
    gap: 8px;
  }

  .recommendation-header {
    flex-direction: column;
    text-align: center;
  }
}
</style>
