# 入库单表单商品信息宽度调整说明

## 调整目标

将入库单表单中的商品信息表格列宽调整为与上面右边栏宽度一致，提升界面的视觉协调性。

## 调整内容

### 1. 表格列宽优化

**调整前：**
- 商品名称：`min-width="200"`（自适应宽度）
- 应收数量：`width="120"`
- 实收数量：`width="120"`
- 操作：`width="80"`

**调整后：**
- 商品名称：`width="380"`（固定宽度，与表单左侧字段对齐）
- 应收数量：`width="130"`（增加宽度，改善输入体验）
- 实收数量：`width="130"`（增加宽度，改善输入体验）
- 操作：`width="90"`（略微增加宽度）

### 2. 组件样式优化

**输入组件：**
- 为所有输入组件添加 `style="width: 100%"`，确保充分利用列宽
- 为数字输入框添加 `size="small"`，使界面更紧凑

**表格样式：**
- 添加白色背景和圆角边框
- 增加轻微阴影效果
- 优化表头样式，使用浅灰色背景
- 添加行悬停效果

### 3. CSS样式增强

```css
/* 入库商品表格样式优化 */
.receipt-items .el-table {
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.receipt-items .el-table .el-table__header {
  background-color: #f8f9fa;
}

.receipt-items .el-table .el-table__header th {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

.receipt-items .el-table .el-table__row:hover {
  background-color: #f5f7fa;
}

.receipt-items .el-table .el-table__cell {
  padding: 8px 12px;
}

.receipt-items .el-table .el-input-number .el-input__inner {
  text-align: center;
}
```

## 布局对齐逻辑

### 表单布局分析
- 对话框总宽度：900px
- 表单标签宽度：100px
- 表单内容区域：约800px
- 两列布局时每列宽度：约390px（考虑20px间距）

### 表格列宽分配
- 商品名称列（380px）：与表单单列字段宽度基本一致
- 数量列（130px × 2）：合理的输入框宽度
- 操作列（90px）：足够容纳删除按钮

## 视觉效果改进

1. **对齐一致性**：表格列宽与上方表单字段宽度保持视觉一致
2. **空间利用**：充分利用对话框宽度，避免浪费空间
3. **用户体验**：输入框宽度适中，便于操作
4. **视觉层次**：通过背景色和阴影增强表格的视觉层次

## 响应式考虑

当前调整主要针对桌面端显示效果，在移动端可能需要进一步优化：
- 考虑使用响应式列宽
- 在小屏幕上可能需要调整表格布局
- 可以考虑在移动端使用卡片式布局替代表格

## 文件修改

**修改文件：** `frontend/src/views/PurchaseReceiptView.vue`

**主要修改区域：**
1. 第462-518行：表格列定义和宽度设置
2. 第1571-1611行：CSS样式优化

## 测试建议

1. 在不同浏览器中测试显示效果
2. 验证表格在有/无操作列时的显示效果
3. 测试长商品名称的显示效果
4. 验证输入框的交互体验
