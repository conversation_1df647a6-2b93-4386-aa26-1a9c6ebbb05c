"""
客户服务层
"""

from typing import List, Optional, Tu<PERSON>
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from app.models.customer import Customer
from app.schemas.customer import CustomerCreate, CustomerUpdate, CustomerQuery, CustomerStats
import math


class CustomerService:
    def __init__(self, db: Session):
        self.db = db

    def get_customers(
        self, 
        skip: int = 0, 
        limit: int = 20,
        query: Optional[CustomerQuery] = None
    ) -> Tuple[List[Customer], int]:
        """获取客户列表"""
        
        # 构建查询
        db_query = self.db.query(Customer)
        
        # 应用过滤条件
        if query:
            conditions = []
            
            if query.name:
                conditions.append(Customer.name.like(f"%{query.name}%"))
            
            if query.code:
                conditions.append(Customer.code.like(f"%{query.code}%"))
            
            if query.customer_type:
                conditions.append(Customer.customer_type == query.customer_type)
            
            if query.level:
                conditions.append(Customer.level == query.level)
            
            if query.status:
                conditions.append(Customer.status == query.status)
            
            if query.phone:
                conditions.append(Customer.phone.like(f"%{query.phone}%"))
            
            if query.email:
                conditions.append(Customer.email.like(f"%{query.email}%"))
            
            if query.address:
                conditions.append(Customer.address.like(f"%{query.address}%"))
            
            if conditions:
                db_query = db_query.filter(and_(*conditions))
        
        # 获取总数
        total = db_query.count()
        
        # 应用分页和排序
        customers = db_query.order_by(Customer.created_at.desc()).offset(skip).limit(limit).all()

        # 确保数据完整性，为NULL字段设置默认值
        for customer in customers:
            if customer.current_balance is None:
                customer.current_balance = 0.0
            if customer.total_orders is None:
                customer.total_orders = 0
            if customer.total_amount is None:
                customer.total_amount = 0.0
            if customer.is_active is None:
                customer.is_active = True
            if customer.status is None:
                customer.status = "active"

        return customers, total

    def get_customer(self, customer_id: int) -> Optional[Customer]:
        """获取单个客户"""
        return self.db.query(Customer).filter(Customer.id == customer_id).first()

    def get_customer_by_code(self, code: str) -> Optional[Customer]:
        """根据编码获取客户"""
        return self.db.query(Customer).filter(Customer.code == code).first()

    def create_customer(self, customer_data: CustomerCreate) -> Customer:
        """创建客户"""
        
        # 检查编码是否已存在
        existing_customer = self.get_customer_by_code(customer_data.code)
        if existing_customer:
            raise ValueError(f"客户编码 {customer_data.code} 已存在")
        
        # 创建客户
        db_customer = Customer(**customer_data.model_dump())
        self.db.add(db_customer)
        self.db.commit()
        self.db.refresh(db_customer)
        
        return db_customer

    def update_customer(self, customer_id: int, customer_data: CustomerUpdate) -> Optional[Customer]:
        """更新客户"""
        
        db_customer = self.get_customer(customer_id)
        if not db_customer:
            return None
        
        # 如果更新编码，检查是否重复
        if customer_data.code and customer_data.code != db_customer.code:
            existing_customer = self.get_customer_by_code(customer_data.code)
            if existing_customer:
                raise ValueError(f"客户编码 {customer_data.code} 已存在")
        
        # 更新字段
        update_data = customer_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_customer, field, value)
        
        self.db.commit()
        self.db.refresh(db_customer)
        
        return db_customer

    def delete_customer(self, customer_id: int) -> bool:
        """删除客户"""
        
        db_customer = self.get_customer(customer_id)
        if not db_customer:
            return False
        
        self.db.delete(db_customer)
        self.db.commit()
        
        return True

    def get_customer_stats(self) -> CustomerStats:
        """获取客户统计信息"""
        
        # 总客户数
        total_customers = self.db.query(Customer).count()
        
        # 活跃客户数
        active_customers = self.db.query(Customer).filter(Customer.status == "active").count()
        
        # VIP客户数
        vip_customers = self.db.query(Customer).filter(Customer.level == "vip").count()
        
        # 新客户数（最近30天）
        from datetime import datetime, timedelta
        thirty_days_ago = datetime.now() - timedelta(days=30)
        new_customers = self.db.query(Customer).filter(Customer.created_at >= thirty_days_ago).count()
        
        # 总订单数和总金额
        total_orders = self.db.query(func.sum(Customer.total_orders)).scalar() or 0
        total_amount = self.db.query(func.sum(Customer.total_amount)).scalar() or 0.0
        
        return CustomerStats(
            total_customers=total_customers,
            active_customers=active_customers,
            vip_customers=vip_customers,
            new_customers=new_customers,
            total_orders=total_orders,
            total_amount=total_amount
        )
