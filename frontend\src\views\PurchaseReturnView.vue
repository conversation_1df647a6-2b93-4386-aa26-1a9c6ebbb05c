<template>
  <div class="purchase-return-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>采购退货单</h2>
        <p class="page-description">管理采购退货单，跟踪退货流程和质量问题</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建退货单
        </el-button>
        <el-button @click="exportReturns">
          <el-icon><Download /></el-icon>
          导出退货单
        </el-button>
      </div>
    </div>

    <!-- 退货单概览 -->
    <el-row :gutter="20" class="return-overview">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon total">
              <el-icon size="32"><Tickets /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ returnStats.total_returns }}</div>
              <div class="overview-label">退货单总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon pending">
              <el-icon size="32"><Clock /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ returnStats.draft_returns }}</div>
              <div class="overview-label">草稿</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon processing">
              <el-icon size="32"><Loading /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ returnStats.submitted_returns }}</div>
              <div class="overview-label">已提交</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon completed">
              <el-icon size="32"><CircleCheck /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ returnStats.approved_returns }}</div>
              <div class="overview-label">已审核</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true">
        <el-form-item label="退货单号">
          <el-input
            v-model="searchForm.return_no"
            placeholder="请输入退货单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="供应商">
          <el-select v-model="searchForm.supplier_id" placeholder="选择供应商" clearable style="width: 200px">
            <el-option
              v-for="supplier in suppliers"
              :key="supplier.id"
              :label="supplier.name"
              :value="supplier.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="单据状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 150px">
            <el-option label="草稿" value="draft" />
            <el-option label="已提交" value="submitted" />
            <el-option label="已审核" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="已退货" value="returned" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-form-item>

        <el-form-item label="退货日期">
          <el-date-picker
            v-model="searchForm.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button @click="fetchPurchaseReturns" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 退货单列表 -->
    <el-card class="return-list-card">
      <template #header>
        <div class="card-header">
          <span>退货单列表</span>
          <div class="header-actions">
            <el-button-group>
              <el-button 
                :type="viewMode === 'table' ? 'primary' : ''" 
                @click="viewMode = 'table'"
                :icon="List"
              >
                表格视图
              </el-button>
              <el-button 
                :type="viewMode === 'card' ? 'primary' : ''" 
                @click="viewMode = 'card'"
                :icon="Grid"
              >
                卡片视图
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'">
        <el-table :data="purchaseReturns" style="width: 100%" v-loading="loading">
          <el-table-column prop="return_no" label="退货单号" width="140" />
          <el-table-column prop="supplier_name" label="供应商" width="150" />
          <el-table-column prop="total_amount" label="退货金额" width="120">
            <template #default="{ row }">
              <span class="amount">¥{{ row.total_amount.toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="return_date" label="退货日期" width="120">
            <template #default="{ row }">
              {{ formatDate(row.return_date) }}
            </template>
          </el-table-column>
          <el-table-column prop="reason" label="退货原因" min-width="200" show-overflow-tooltip />
          <el-table-column prop="created_at" label="创建时间" width="150">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="240" fixed="right">
            <template #default="{ row }">
              <div class="table-actions">
                <el-button size="small" @click="viewReturnDetail(row)">查看</el-button>
                <el-dropdown @command="(command: string) => handleReturnAction(command, row)" v-if="row.status !== 'completed'">
                  <el-button size="small" type="primary">
                    操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit" v-if="row.status === 'draft'">编辑</el-dropdown-item>
                      <el-dropdown-item command="submit" v-if="row.status === 'draft'">提交</el-dropdown-item>
                      <el-dropdown-item command="approve" v-if="row.status === 'submitted'">审核通过</el-dropdown-item>
                      <el-dropdown-item command="reject" v-if="row.status === 'submitted'">审核拒绝</el-dropdown-item>
                      <el-dropdown-item command="return" v-if="row.status === 'approved'">确认退货</el-dropdown-item>
                      <el-dropdown-item command="complete" v-if="row.status === 'returned'">完成</el-dropdown-item>
                      <el-dropdown-item command="delete" v-if="row.status === 'draft'" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <el-row :gutter="20">
          <el-col :span="8" v-for="returnItem in purchaseReturns" :key="returnItem.id">
            <el-card class="return-card" @click="viewReturnDetail(returnItem)">
              <div class="card-header">
                <h3>{{ returnItem.return_no }}</h3>
                <el-tag :type="getStatusTagType(returnItem.status)">
                  {{ getStatusLabel(returnItem.status) }}
                </el-tag>
              </div>
              <div class="card-content">
                <p><strong>供应商:</strong> {{ returnItem.supplier_name }}</p>
                <p><strong>退货金额:</strong> <span class="amount">¥{{ returnItem.total_amount.toFixed(2) }}</span></p>
                <p><strong>退货日期:</strong> {{ formatDate(returnItem.return_date) }}</p>
                <p><strong>退货原因:</strong> {{ returnItem.reason }}</p>
              </div>
              <div class="card-actions" @click.stop>
                <el-button size="small" @click="viewReturnDetail(returnItem)">查看详情</el-button>
                <el-dropdown @command="(command: string) => handleReturnAction(command, returnItem)" v-if="returnItem.status !== 'completed'">
                  <el-button size="small" type="primary">
                    操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit" v-if="returnItem.status === 'draft'">编辑</el-dropdown-item>
                      <el-dropdown-item command="submit" v-if="returnItem.status === 'draft'">提交</el-dropdown-item>
                      <el-dropdown-item command="approve" v-if="returnItem.status === 'submitted'">审核通过</el-dropdown-item>
                      <el-dropdown-item command="reject" v-if="returnItem.status === 'submitted'">审核拒绝</el-dropdown-item>
                      <el-dropdown-item command="return" v-if="returnItem.status === 'approved'">确认退货</el-dropdown-item>
                      <el-dropdown-item command="complete" v-if="returnItem.status === 'returned'">完成</el-dropdown-item>
                      <el-dropdown-item command="delete" v-if="returnItem.status === 'draft'" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchPurchaseReturns"
          @current-change="fetchPurchaseReturns"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  purchaseReturnApi,
  type PurchaseReturn,
  type PurchaseReturnStats,
  PurchaseReturnStatus
} from '@/api/purchase'
import { supplierApi, type Supplier } from '@/api/suppliers'
import {
  Plus,
  Download,
  Tickets,
  Clock,
  Loading,
  CircleCheck,
  Search,
  List,
  Grid,
  ArrowDown,
  Refresh
} from '@element-plus/icons-vue'

// 响应式数据
const purchaseReturns = ref<PurchaseReturn[]>([])
const suppliers = ref<Supplier[]>([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const viewMode = ref<'table' | 'card'>('table')

const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const selectedReturn = ref<PurchaseReturn | null>(null)

// 搜索表单
const searchForm = reactive({
  return_no: '',
  supplier_id: null as number | null,
  status: '',
  date_range: null as [Date, Date] | null
})

// 统计数据
const returnStats = ref<PurchaseReturnStats>({
  total_returns: 0,
  draft_returns: 0,
  submitted_returns: 0,
  approved_returns: 0,
  returned_returns: 0,
  completed_returns: 0,
  total_amount: 0
})

// 方法
const fetchPurchaseReturns = async () => {
  loading.value = true
  try {
    const params: any = {
      page: currentPage.value,
      page_size: pageSize.value
    }

    if (searchForm.return_no) {
      params.return_no = searchForm.return_no
    }
    if (searchForm.supplier_id) {
      params.supplier_id = searchForm.supplier_id
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }
    if (searchForm.date_range) {
      params.start_date = searchForm.date_range[0].toISOString()
      params.end_date = searchForm.date_range[1].toISOString()
    }

    const response = await purchaseReturnApi.getPurchaseReturns(params)

    if (response) {
      purchaseReturns.value = response || []
      total.value = response.length || 0
    } else {
      purchaseReturns.value = []
      total.value = 0
    }

    // 获取统计数据
    const statsResponse = await purchaseReturnApi.getStats()
    if (statsResponse) {
      returnStats.value = statsResponse
    }
  } catch (error: any) {
    console.error('获取采购退货单失败:', error)
    ElMessage.error('获取采购退货单失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    loading.value = false
  }
}

const fetchSuppliers = async () => {
  try {
    const response = await supplierApi.getSuppliers()
    if (response && (response as any).items) {
      suppliers.value = (response as any).items
    }
  } catch (error: any) {
    console.error('获取供应商列表失败:', error)
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchPurchaseReturns()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    return_no: '',
    supplier_id: null,
    status: '',
    date_range: null
  })
  handleSearch()
}

const viewReturnDetail = (returnItem: PurchaseReturn) => {
  selectedReturn.value = returnItem
  showDetailDialog.value = true
}

const handleReturnAction = async (command: string, returnItem: PurchaseReturn) => {
  switch (command) {
    case 'edit':
      ElMessage.info('编辑功能开发中...')
      break
    case 'submit':
      await submitReturn(returnItem)
      break
    case 'approve':
      await approveReturn(returnItem)
      break
    case 'reject':
      await rejectReturn(returnItem)
      break
    case 'return':
      await confirmReturn(returnItem)
      break
    case 'complete':
      await completeReturn(returnItem)
      break
    case 'delete':
      await deleteReturn(returnItem)
      break
    default:
      ElMessage.warning('未知操作')
  }
}

const submitReturn = async (returnItem: PurchaseReturn) => {
  try {
    await ElMessageBox.confirm('确定要提交这个退货单吗？', '确认提交', {
      type: 'warning'
    })

    await purchaseReturnApi.submitPurchaseReturn(returnItem.id!)
    ElMessage.success('退货单提交成功')
    fetchPurchaseReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('提交退货单失败:', error)
      ElMessage.error('提交失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const approveReturn = async (returnItem: PurchaseReturn) => {
  try {
    await ElMessageBox.confirm('确定要审核通过这个退货单吗？', '确认审核', {
      type: 'warning'
    })

    await purchaseReturnApi.approvePurchaseReturn(returnItem.id!, {
      status: PurchaseReturnStatus.APPROVED
    })
    ElMessage.success('退货单审核通过')
    fetchPurchaseReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('审核退货单失败:', error)
      ElMessage.error('审核失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const rejectReturn = async (returnItem: PurchaseReturn) => {
  try {
    const { value: rejectReason } = await ElMessageBox.prompt('请输入拒绝原因', '审核拒绝', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '拒绝原因不能为空'
    })

    await purchaseReturnApi.approvePurchaseReturn(returnItem.id!, {
      status: PurchaseReturnStatus.REJECTED,
      note: rejectReason
    })
    ElMessage.success('退货单审核拒绝')
    fetchPurchaseReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('拒绝退货单失败:', error)
      ElMessage.error('拒绝失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const confirmReturn = async (returnItem: PurchaseReturn) => {
  try {
    await ElMessageBox.confirm('确定要确认退货吗？', '确认退货', {
      type: 'warning'
    })

    await purchaseReturnApi.returnPurchaseReturn(returnItem.id!)
    ElMessage.success('退货确认成功')
    fetchPurchaseReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('确认退货失败:', error)
      ElMessage.error('确认退货失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const completeReturn = async (returnItem: PurchaseReturn) => {
  try {
    await ElMessageBox.confirm('确定要完成这个退货单吗？', '确认完成', {
      type: 'warning'
    })

    await purchaseReturnApi.completePurchaseReturn(returnItem.id!)
    ElMessage.success('退货单完成')
    fetchPurchaseReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('完成退货单失败:', error)
      ElMessage.error('完成失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const deleteReturn = async (returnItem: PurchaseReturn) => {
  try {
    await ElMessageBox.confirm('确定要删除这个退货单吗？删除后无法恢复！', '确认删除', {
      type: 'warning'
    })

    await purchaseReturnApi.deletePurchaseReturn(returnItem.id!)
    ElMessage.success('退货单删除成功')
    fetchPurchaseReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除退货单失败:', error)
      ElMessage.error('删除失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const exportReturns = () => {
  ElMessage.success('退货单导出成功')
}

// 辅助方法
const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    submitted: '已提交',
    approved: '已审核',
    rejected: '已拒绝',
    returned: '已退货',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    draft: '',
    submitted: 'warning',
    approved: 'success',
    rejected: 'danger',
    returned: 'success',
    completed: 'success',
    cancelled: 'info'
  }
  return typeMap[status] || ''
}

const formatDate = (dateStr: string | null | undefined) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateStr: string | null | undefined) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  fetchPurchaseReturns()
  fetchSuppliers()
})
</script>

<style scoped>
.purchase-return-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
}

.page-description {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 退货单概览 */
.return-overview {
  margin-bottom: 12px;
}

.overview-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.overview-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.overview-icon {
  padding: 12px;
  border-radius: 12px;
  flex-shrink: 0;
}

.overview-icon.total {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-icon.pending {
  background: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.overview-icon.processing {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-icon.completed {
  background: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.overview-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 14px;
  color: #7f8c8d;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 退货单列表卡片 */
.return-list-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.return-list-card .el-card__header {
  padding: 14px 20px !important;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 金额样式 */
.amount {
  font-weight: 600;
  color: #E6A23C;
}

/* 卡片视图 */
.card-view {
  margin-top: 20px;
}

.return-card {
  margin-bottom: 20px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.return-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.return-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.return-card .card-header h3 {
  margin: 0;
  color: #303133;
}

.return-card .card-content {
  margin-bottom: 16px;
}

.return-card .card-content p {
  margin: 8px 0;
  color: #606266;
}

.return-card .card-actions {
  display: flex;
  gap: 8px;
}

/* 表格操作 */
.table-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.table-actions .el-button {
  margin: 0;
}

/* 分页 */
.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .purchase-return-view {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .return-overview .el-col {
    margin-bottom: 12px;
  }

  .overview-content {
    padding: 16px;
  }

  .search-card .el-form {
    flex-direction: column;
  }

  .search-card .el-form-item {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .table-actions {
    flex-direction: column;
    gap: 4px;
  }

  .card-view .el-col {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .header-left h2 {
    font-size: 24px;
  }

  .overview-value {
    font-size: 20px;
  }

  .overview-content {
    gap: 12px;
  }

  .overview-icon {
    padding: 8px;
  }
}
</style>
