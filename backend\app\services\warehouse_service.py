"""
仓库管理服务
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from app.models.warehouse import Warehouse
from app.models.inventory import Inventory
from app.schemas.warehouse import (
    WarehouseCreate, WarehouseUpdate, WarehouseQuery, WarehouseStats
)


class WarehouseService:
    """仓库管理服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_warehouses(self, query_params: WarehouseQuery) -> List[Warehouse]:
        """获取仓库列表"""
        query = self.db.query(Warehouse)
        
        # 应用筛选条件
        if query_params.name:
            query = query.filter(Warehouse.name.contains(query_params.name))
        if query_params.address:
            query = query.filter(Warehouse.address.contains(query_params.address))
        if query_params.status:
            query = query.filter(Warehouse.status == query_params.status)
        if query_params.is_active is not None:
            query = query.filter(Warehouse.is_active == query_params.is_active)
        if query_params.min_capacity:
            query = query.filter(Warehouse.total_capacity_m3 >= query_params.min_capacity)
        if query_params.max_capacity:
            query = query.filter(Warehouse.total_capacity_m3 <= query_params.max_capacity)
        
        # 分页
        skip = (query_params.page - 1) * query_params.page_size
        return query.offset(skip).limit(query_params.page_size).all()
    
    def get_warehouse(self, warehouse_id: int) -> Optional[Warehouse]:
        """获取单个仓库"""
        return self.db.query(Warehouse).filter(Warehouse.id == warehouse_id).first()
    
    def create_warehouse(self, warehouse_data: WarehouseCreate) -> Warehouse:
        """创建仓库"""
        # 检查仓库名称是否已存在
        existing = self.db.query(Warehouse).filter(Warehouse.name == warehouse_data.name).first()
        if existing:
            raise ValueError("仓库名称已存在")
        
        # 创建仓库
        db_warehouse = Warehouse(
            name=warehouse_data.name,
            address=warehouse_data.address,
            total_capacity_m3=warehouse_data.total_capacity_m3,
            used_capacity_m3=warehouse_data.used_capacity_m3,
            latitude=warehouse_data.latitude,
            longitude=warehouse_data.longitude,
            operating_hours=warehouse_data.operating_hours,
            contact_info=warehouse_data.contact_info,
            is_active=warehouse_data.is_active,
            status=warehouse_data.status
        )
        
        self.db.add(db_warehouse)
        self.db.commit()
        self.db.refresh(db_warehouse)
        return db_warehouse
    
    def update_warehouse(self, warehouse_id: int, warehouse_data: WarehouseUpdate) -> Optional[Warehouse]:
        """更新仓库"""
        db_warehouse = self.get_warehouse(warehouse_id)
        if not db_warehouse:
            return None
        
        # 如果更新名称，检查是否与其他仓库重复
        if warehouse_data.name and warehouse_data.name != db_warehouse.name:
            existing = self.db.query(Warehouse).filter(
                and_(Warehouse.name == warehouse_data.name, Warehouse.id != warehouse_id)
            ).first()
            if existing:
                raise ValueError("仓库名称已存在")
        
        # 更新字段
        update_data = warehouse_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_warehouse, field, value)
        
        db_warehouse.updated_at = datetime.now()
        self.db.commit()
        self.db.refresh(db_warehouse)
        return db_warehouse
    
    def delete_warehouse(self, warehouse_id: int) -> bool:
        """删除仓库"""
        db_warehouse = self.get_warehouse(warehouse_id)
        if not db_warehouse:
            return False
        
        # 检查是否有关联的库存记录
        inventory_count = self.db.query(Inventory).filter(
            Inventory.warehouse_id == warehouse_id,
            Inventory.is_active == True
        ).count()
        
        if inventory_count > 0:
            raise ValueError("仓库中还有库存记录，无法删除")
        
        self.db.delete(db_warehouse)
        self.db.commit()
        return True
    
    def calculate_warehouse_metrics(self, warehouse: Warehouse) -> Dict[str, Any]:
        """计算仓库指标"""
        # 计算可用容量
        available_capacity = warehouse.total_capacity_m3 - warehouse.used_capacity_m3
        
        # 计算利用率
        utilization_rate = (warehouse.used_capacity_m3 / warehouse.total_capacity_m3 * 100) if warehouse.total_capacity_m3 > 0 else 0
        
        # 计算库存商品数量
        inventory_count = self.db.query(Inventory).filter(
            Inventory.warehouse_id == warehouse.id,
            Inventory.is_active == True
        ).count()
        
        return {
            "available_capacity_m3": available_capacity,
            "utilization_rate": round(utilization_rate, 2),
            "inventory_count": inventory_count
        }
    
    def get_warehouse_stats(self) -> WarehouseStats:
        """获取仓库统计信息"""
        # 仓库总数
        total_warehouses = self.db.query(Warehouse).count()
        
        # 活跃仓库数
        active_warehouses = self.db.query(Warehouse).filter(Warehouse.is_active == True).count()
        
        # 总容量和已用容量
        capacity_stats = self.db.query(
            func.sum(Warehouse.total_capacity_m3).label('total_capacity'),
            func.sum(Warehouse.used_capacity_m3).label('used_capacity')
        ).first()
        
        total_capacity = float(capacity_stats.total_capacity or 0)
        used_capacity = float(capacity_stats.used_capacity or 0)
        available_capacity = total_capacity - used_capacity
        
        # 平均利用率
        average_utilization_rate = (used_capacity / total_capacity * 100) if total_capacity > 0 else 0
        
        return WarehouseStats(
            total_warehouses=total_warehouses,
            active_warehouses=active_warehouses,
            total_capacity=total_capacity,
            used_capacity=used_capacity,
            available_capacity=available_capacity,
            average_utilization_rate=round(average_utilization_rate, 2)
        )
    
    def get_warehouse_inventory(self, warehouse_id: int, skip: int = 0, limit: int = 50) -> Dict[str, Any]:
        """获取仓库库存"""
        warehouse = self.get_warehouse(warehouse_id)
        if not warehouse:
            return None
        
        # 获取库存记录
        inventory_query = self.db.query(Inventory).filter(
            Inventory.warehouse_id == warehouse_id,
            Inventory.is_active == True
        )
        
        total = inventory_query.count()
        inventories = inventory_query.offset(skip).limit(limit).all()
        
        return {
            "warehouse_id": warehouse_id,
            "warehouse_name": warehouse.name,
            "total": total,
            "inventories": inventories
        }
    
    def update_warehouse_capacity(self, warehouse_id: int, used_capacity_m3: float) -> bool:
        """更新仓库已用容量"""
        db_warehouse = self.get_warehouse(warehouse_id)
        if not db_warehouse:
            return False
        
        if used_capacity_m3 > db_warehouse.total_capacity_m3:
            raise ValueError("已用容量不能超过总容量")
        
        db_warehouse.used_capacity_m3 = used_capacity_m3
        db_warehouse.updated_at = datetime.now()
        self.db.commit()
        return True
    
    def get_warehouses_by_location(self, latitude: float, longitude: float, radius_km: float = 50) -> List[Warehouse]:
        """根据位置获取附近的仓库"""
        # 简化的距离计算，实际应用中应使用更精确的地理计算
        warehouses = self.db.query(Warehouse).filter(
            Warehouse.is_active == True,
            Warehouse.latitude.isnot(None),
            Warehouse.longitude.isnot(None)
        ).all()
        
        nearby_warehouses = []
        for warehouse in warehouses:
            # 简化的距离计算（实际应使用Haversine公式）
            lat_diff = abs(warehouse.latitude - latitude)
            lng_diff = abs(warehouse.longitude - longitude)
            distance = (lat_diff + lng_diff) * 111  # 粗略转换为公里
            
            if distance <= radius_km:
                nearby_warehouses.append(warehouse)
        
        return nearby_warehouses
