from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import random
import math
from app.models.logistics import LogisticsRoute, DeliveryOrder
from app.schemas.logistics import (
    LogisticsRouteCreate, DeliveryOrderCreate,
    CostCalculationRequest, CostCalculationResponse,
    RouteOptimizationRequest, RouteOptimizationResponse
)

class LogisticsService:
    def __init__(self, db: Session):
        self.db = db
    
    def get_routes(self, skip: int = 0, limit: int = 100, origin: str = None, destination: str = None) -> List[LogisticsRoute]:
        """获取物流路线列表"""
        query = self.db.query(LogisticsRoute).filter(LogisticsRoute.is_active == "active")
        
        if origin:
            query = query.filter(LogisticsRoute.origin.contains(origin))
        if destination:
            query = query.filter(LogisticsRoute.destination.contains(destination))
        
        return query.offset(skip).limit(limit).all()
    
    def get_route(self, route_id: int) -> Optional[LogisticsRoute]:
        """获取单个路线"""
        return self.db.query(LogisticsRoute).filter(LogisticsRoute.id == route_id).first()
    
    def create_route(self, route_data: LogisticsRouteCreate) -> LogisticsRoute:
        """创建新路线"""
        db_route = LogisticsRoute(**route_data.dict())
        self.db.add(db_route)
        self.db.commit()
        self.db.refresh(db_route)
        return db_route
    
    def calculate_cost(self, request: CostCalculationRequest) -> CostCalculationResponse:
        """计算配送成本"""
        # 查找最优路线
        best_route = self._find_best_route(request.pickup_address, request.delivery_address)
        
        if not best_route:
            # 如果没有现有路线，创建临时路线进行计算
            distance = self._calculate_distance(request.pickup_address, request.delivery_address)
            base_cost = 50.0
            cost_per_km = 2.5
            estimated_time = distance / 60  # 假设平均60km/h
        else:
            distance = best_route.distance_km
            base_cost = best_route.base_cost
            cost_per_km = best_route.cost_per_km
            estimated_time = best_route.estimated_time_hours
        
        # 计算各项费用
        distance_cost = distance * cost_per_km
        weight_cost = request.weight_kg * 0.5  # 每公斤0.5元
        
        additional_fees = {}
        if request.delivery_type == "express":
            additional_fees["加急费"] = base_cost * 0.5
            estimated_time *= 0.7  # 加急减少30%时间
        elif request.delivery_type == "same_day":
            additional_fees["当日达费"] = base_cost * 1.0
            estimated_time *= 0.5  # 当日达减少50%时间
        
        if request.volume_m3 and request.volume_m3 > 1.0:
            additional_fees["大件费"] = (request.volume_m3 - 1.0) * 20
        
        total_additional = sum(additional_fees.values())
        total_cost = base_cost + distance_cost + weight_cost + total_additional
        
        # 计算预计送达时间
        estimated_delivery_time = datetime.now() + timedelta(hours=estimated_time)
        
        return CostCalculationResponse(
            total_cost=round(total_cost, 2),
            base_cost=base_cost,
            distance_cost=round(distance_cost, 2),
            weight_cost=round(weight_cost, 2),
            additional_fees=additional_fees,
            estimated_delivery_time=estimated_delivery_time,
            recommended_route=best_route
        )
    
    def optimize_route(self, request: RouteOptimizationRequest) -> RouteOptimizationResponse:
        """路线优化"""
        # 简化的路线优化算法
        pickup_points = request.pickup_points
        delivery_points = request.delivery_points
        
        # 生成优化路线
        optimized_routes = []
        total_distance = 0
        total_cost = 0
        total_time = 0
        
        # 模拟路线优化结果
        for i, (pickup, delivery) in enumerate(zip(pickup_points, delivery_points)):
            distance = self._calculate_distance(pickup, delivery)
            cost = distance * 2.5 + 50  # 基础费用计算
            time = distance / 60  # 假设平均速度60km/h
            
            route_info = {
                "route_id": i + 1,
                "pickup": pickup,
                "delivery": delivery,
                "distance_km": distance,
                "cost": round(cost, 2),
                "estimated_time_hours": round(time, 2),
                "sequence": i + 1
            }
            optimized_routes.append(route_info)
            
            total_distance += distance
            total_cost += cost
            total_time += time
        
        # 计算效率评分
        efficiency_score = min(0.95, max(0.6, 1.0 - (total_cost / (len(pickup_points) * 200))))
        
        return RouteOptimizationResponse(
            optimized_routes=optimized_routes,
            total_distance=round(total_distance, 2),
            total_cost=round(total_cost, 2),
            total_time=round(total_time, 2),
            efficiency_score=round(efficiency_score, 2)
        )
    
    def get_delivery_orders(self, skip: int = 0, limit: int = 100, status: str = None) -> List[DeliveryOrder]:
        """获取配送订单列表"""
        query = self.db.query(DeliveryOrder)
        if status:
            query = query.filter(DeliveryOrder.status == status)
        return query.offset(skip).limit(limit).all()
    
    def get_delivery_order(self, order_id: int) -> Optional[DeliveryOrder]:
        """获取配送订单详情"""
        return self.db.query(DeliveryOrder).filter(DeliveryOrder.id == order_id).first()
    
    def create_delivery_order(self, order_data: DeliveryOrderCreate) -> DeliveryOrder:
        """创建配送订单"""
        # 生成订单号
        order_number = f"DO{datetime.now().strftime('%Y%m%d%H%M%S')}{random.randint(100, 999)}"
        
        # 查找最佳路线
        best_route = self._find_best_route(order_data.pickup_address, order_data.delivery_address)
        route_id = best_route.id if best_route else 1
        
        # 计算成本
        cost_request = CostCalculationRequest(
            pickup_address=order_data.pickup_address,
            delivery_address=order_data.delivery_address,
            weight_kg=order_data.weight_kg,
            volume_m3=order_data.volume_m3,
            package_count=order_data.package_count
        )
        cost_result = self.calculate_cost(cost_request)
        
        db_order = DeliveryOrder(
            order_number=order_number,
            route_id=route_id,
            calculated_cost=cost_result.total_cost,
            estimated_delivery_time=cost_result.estimated_delivery_time,
            **order_data.dict()
        )
        
        self.db.add(db_order)
        self.db.commit()
        self.db.refresh(db_order)
        return db_order
    
    def update_order_status(self, order_id: int, status: str, notes: str = None) -> bool:
        """更新订单状态"""
        db_order = self.get_delivery_order(order_id)
        if not db_order:
            return False
        
        db_order.status = status
        if notes:
            db_order.delivery_notes = notes
        
        if status == "delivered":
            db_order.actual_delivery_time = datetime.now()
        
        self.db.commit()
        return True
    
    def get_performance_analytics(self, time_period: str = "30d") -> Dict[str, Any]:
        """获取物流性能分析"""
        # 计算时间范围
        days = int(time_period.replace('d', ''))
        start_date = datetime.now() - timedelta(days=days)
        
        # 查询订单数据
        orders = self.db.query(DeliveryOrder).filter(
            DeliveryOrder.created_at >= start_date
        ).all()
        
        if not orders:
            return {"message": "暂无数据"}
        
        # 计算性能指标
        total_orders = len(orders)
        delivered_orders = [o for o in orders if o.status == "delivered"]
        delivery_rate = len(delivered_orders) / total_orders * 100
        
        # 计算平均成本和时间
        avg_cost = sum(o.calculated_cost or 0 for o in orders) / total_orders
        
        # 计算准时率
        on_time_deliveries = 0
        for order in delivered_orders:
            if (order.actual_delivery_time and order.estimated_delivery_time and 
                order.actual_delivery_time <= order.estimated_delivery_time):
                on_time_deliveries += 1
        
        on_time_rate = (on_time_deliveries / len(delivered_orders) * 100) if delivered_orders else 0
        
        return {
            "period": time_period,
            "total_orders": total_orders,
            "delivery_rate": round(delivery_rate, 2),
            "on_time_rate": round(on_time_rate, 2),
            "average_cost": round(avg_cost, 2),
            "status_distribution": self._get_status_distribution(orders),
            "daily_orders": self._get_daily_order_stats(orders, days)
        }
    
    def estimate_delivery_time(self, pickup_address: str, delivery_address: str, delivery_type: str = "standard") -> Dict[str, Any]:
        """预估配送时间"""
        distance = self._calculate_distance(pickup_address, delivery_address)
        base_time = distance / 60  # 基础时间，假设60km/h
        
        # 根据配送类型调整时间
        if delivery_type == "express":
            estimated_hours = base_time * 0.7
        elif delivery_type == "same_day":
            estimated_hours = base_time * 0.5
        else:
            estimated_hours = base_time
        
        estimated_delivery_time = datetime.now() + timedelta(hours=estimated_hours)
        
        return {
            "pickup_address": pickup_address,
            "delivery_address": delivery_address,
            "delivery_type": delivery_type,
            "distance_km": round(distance, 2),
            "estimated_hours": round(estimated_hours, 2),
            "estimated_delivery_time": estimated_delivery_time.isoformat(),
            "confidence": "high" if distance < 100 else "medium"
        }
    
    # 私有辅助方法
    def _find_best_route(self, pickup: str, delivery: str) -> Optional[LogisticsRoute]:
        """查找最佳路线"""
        # 简化的路线匹配逻辑
        routes = self.db.query(LogisticsRoute).filter(LogisticsRoute.is_active == "active").all()
        
        for route in routes:
            if (pickup in route.origin or route.origin in pickup) and \
               (delivery in route.destination or route.destination in delivery):
                return route
        
        return routes[0] if routes else None
    
    def _calculate_distance(self, address1: str, address2: str) -> float:
        """计算两地距离（模拟）"""
        # 简化的距离计算，实际应用中应使用地图API
        return random.uniform(10, 200)
    
    def _get_status_distribution(self, orders: List[DeliveryOrder]) -> Dict[str, int]:
        """获取订单状态分布"""
        distribution = {}
        for order in orders:
            status = order.status
            distribution[status] = distribution.get(status, 0) + 1
        return distribution
    
    def _get_daily_order_stats(self, orders: List[DeliveryOrder], days: int) -> List[Dict[str, Any]]:
        """获取每日订单统计"""
        daily_stats = []
        for i in range(days):
            date = datetime.now() - timedelta(days=i)
            day_orders = [o for o in orders if o.created_at.date() == date.date()]
            daily_stats.append({
                "date": date.strftime("%Y-%m-%d"),
                "order_count": len(day_orders),
                "total_cost": sum(o.calculated_cost or 0 for o in day_orders)
            })
        return daily_stats[:7]  # 返回最近7天
