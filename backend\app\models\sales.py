"""
销售相关数据模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, Boolean, Float, ForeignKey, Numeric, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.core.database import Base


class SalesOrder(Base):
    """销售订单模型"""
    __tablename__ = "sales_orders"

    id = Column(Integer, primary_key=True, index=True)
    order_no = Column(String(50), unique=True, nullable=False, comment="销售订单号")
    original_order_no = Column(String(100), comment="原始订单号(平台订单号)")
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False, comment="客户ID")
    
    # 订单信息
    total_amount = Column(Float, nullable=False, comment="订单总金额")
    status = Column(String(20), default="pending", comment="状态: pending, confirmed, producing, ready_to_ship, shipped, completed, cancelled")
    delivery_date = Column(DateTime(timezone=True), comment="交货日期")
    delivery_address = Column(Text, comment="配送地址")
    
    # 销售信息
    sales_person = Column(String(100), comment="销售员")
    platform = Column(String(20), default="manual", comment="平台: manual, taobao, tmall, jd, pdd, douyin, xiaohongshu, wechat")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    
    # 备注
    remark = Column(Text, comment="备注")
    
    # 关联关系
    customer = relationship("Customer", back_populates="sales_orders")
    items = relationship("SalesOrderItem", back_populates="order", cascade="all, delete-orphan")
    returns = relationship("SalesReturn", back_populates="sales_order")

    def __repr__(self):
        return f"<SalesOrder(id={self.id}, order_no='{self.order_no}', status='{self.status}')>"


class SalesOrderItem(Base):
    """销售订单明细"""
    __tablename__ = "sales_order_items"

    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(Integer, ForeignKey("sales_orders.id"), nullable=False, comment="销售订单ID")
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, comment="商品ID")
    
    # 商品信息
    product_name = Column(String(200), nullable=False, comment="商品名称")
    product_sku = Column(String(100), comment="商品SKU")
    
    # 数量和价格
    quantity = Column(Integer, nullable=False, comment="销售数量")
    unit_price = Column(Float, nullable=False, comment="单价")
    total_price = Column(Float, nullable=False, comment="小计")
    
    # 出库信息
    shipped_quantity = Column(Integer, default=0, comment="已出库数量")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    
    # 关联关系
    order = relationship("SalesOrder", back_populates="items")
    product = relationship("Product")

    def __repr__(self):
        return f"<SalesOrderItem(id={self.id}, product_name='{self.product_name}', quantity={self.quantity})>"


class SalesOutbound(Base):
    """销售出库单模型"""
    __tablename__ = "sales_outbounds"

    id = Column(Integer, primary_key=True, index=True)
    outbound_no = Column(String(50), unique=True, nullable=False, comment="出库单号")
    sales_order_id = Column(Integer, ForeignKey("sales_orders.id"), comment="销售订单ID")
    sales_order_no = Column(String(50), comment="销售订单号")
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False, comment="客户ID")
    warehouse_id = Column(Integer, ForeignKey("warehouses.id"), nullable=False, comment="仓库ID")
    
    # 出库信息
    status = Column(String(20), default="pending", comment="状态: pending, partial, completed, cancelled")
    outbound_date = Column(DateTime(timezone=True), comment="出库日期")
    
    # 创建信息
    created_by = Column(String(100), nullable=False, comment="创建人")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    
    # 备注
    remark = Column(Text, comment="备注")
    
    # 关联关系
    sales_order = relationship("SalesOrder")
    customer = relationship("Customer")
    warehouse = relationship("Warehouse")
    items = relationship("SalesOutboundItem", back_populates="outbound", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<SalesOutbound(id={self.id}, outbound_no='{self.outbound_no}', status='{self.status}')>"


class SalesOutboundItem(Base):
    """销售出库单明细"""
    __tablename__ = "sales_outbound_items"

    id = Column(Integer, primary_key=True, index=True)
    outbound_id = Column(Integer, ForeignKey("sales_outbounds.id"), nullable=False, comment="出库单ID")
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, comment="商品ID")
    
    # 商品信息
    product_name = Column(String(200), nullable=False, comment="商品名称")
    product_sku = Column(String(100), comment="商品SKU")
    
    # 数量
    quantity = Column(Integer, nullable=False, comment="应出数量")
    outbound_quantity = Column(Integer, nullable=False, comment="实出数量")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    
    # 关联关系
    outbound = relationship("SalesOutbound", back_populates="items")
    product = relationship("Product")

    def __repr__(self):
        return f"<SalesOutboundItem(id={self.id}, product_name='{self.product_name}', outbound_quantity={self.outbound_quantity})>"


class SalesReturnStatus(enum.Enum):
    """销售退货单状态"""
    DRAFT = "draft"           # 草稿
    SUBMITTED = "submitted"   # 已提交
    APPROVED = "approved"     # 已审核
    REJECTED = "rejected"     # 已拒绝
    RETURNED = "returned"     # 已退货
    COMPLETED = "completed"   # 已完成
    CANCELLED = "cancelled"   # 已取消


class SalesReturn(Base):
    """销售退货单模型"""
    __tablename__ = "sales_returns"

    id = Column(Integer, primary_key=True, index=True)
    return_no = Column(String(50), unique=True, index=True, nullable=False, comment="退货单号")

    # 关联信息
    sales_order_id = Column(Integer, ForeignKey("sales_orders.id"), nullable=True, comment="销售订单ID")
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False, comment="客户ID")

    # 基本信息
    return_date = Column(DateTime, nullable=False, comment="退货日期")
    reason = Column(Text, nullable=False, comment="退货原因")
    total_amount = Column(Numeric(10, 2), nullable=False, default=0, comment="退货总金额")

    # 状态信息
    status = Column(Enum(SalesReturnStatus), default=SalesReturnStatus.DRAFT, comment="退货单状态")

    # 审核信息
    submitted_at = Column(DateTime, nullable=True, comment="提交时间")
    submitted_by = Column(String(50), nullable=True, comment="提交人")
    approved_at = Column(DateTime, nullable=True, comment="审核时间")
    approved_by = Column(String(50), nullable=True, comment="审核人")
    approval_note = Column(Text, nullable=True, comment="审核备注")

    # 退货信息
    returned_at = Column(DateTime, nullable=True, comment="退货时间")
    returned_by = Column(String(50), nullable=True, comment="退货人")

    # 备注信息
    remark = Column(Text, nullable=True, comment="备注")

    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    created_by = Column(String(50), nullable=True, comment="创建人")
    updated_by = Column(String(50), nullable=True, comment="更新人")

    # 关系
    sales_order = relationship("SalesOrder", back_populates="returns")
    customer = relationship("Customer", back_populates="sales_returns")
    items = relationship("SalesReturnItem", back_populates="sales_return", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<SalesReturn(id={self.id}, return_no='{self.return_no}', status='{self.status}')>"


class SalesReturnItem(Base):
    """销售退货单明细模型"""
    __tablename__ = "sales_return_items"

    id = Column(Integer, primary_key=True, index=True)
    sales_return_id = Column(Integer, ForeignKey("sales_returns.id"), nullable=False, comment="销售退货单ID")

    # 商品信息
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, comment="商品ID")
    product_name = Column(String(200), nullable=False, comment="商品名称")
    product_sku = Column(String(100), nullable=True, comment="商品SKU")

    # 退货信息
    return_quantity = Column(Integer, nullable=False, comment="退货数量")
    unit_price = Column(Numeric(10, 2), nullable=False, comment="单价")
    total_price = Column(Numeric(10, 2), nullable=False, comment="小计金额")

    # 质量信息
    quality_issue = Column(String(200), nullable=True, comment="质量问题描述")

    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关系
    sales_return = relationship("SalesReturn", back_populates="items")
    product = relationship("Product")

    def __repr__(self):
        return f"<SalesReturnItem(id={self.id}, product_name='{self.product_name}', return_quantity={self.return_quantity})>"
