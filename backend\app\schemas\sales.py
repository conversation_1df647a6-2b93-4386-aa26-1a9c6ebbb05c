"""
销售订单数据模式
"""

from typing import List, Optional
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field
from enum import Enum


class SalesOrderStatus(str, Enum):
    """销售订单状态"""
    PENDING = "pending"
    CONFIRMED = "confirmed"
    PRODUCING = "producing"
    READY_TO_SHIP = "ready_to_ship"
    SHIPPED = "shipped"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class Platform(str, Enum):
    """销售平台"""
    MANUAL = "manual"
    TAOBAO = "taobao"
    TMALL = "tmall"
    JD = "jd"
    PDD = "pdd"
    DOUYIN = "douyin"
    XIAOHONGSHU = "xiaohongshu"
    WECHAT = "wechat"


class SalesOrderItemBase(BaseModel):
    """销售订单明细基础模型"""
    product_id: int = Field(..., description="商品ID")
    quantity: int = Field(..., ge=1, description="销售数量")
    unit_price: float = Field(..., ge=0, description="单价")
    total_price: float = Field(..., ge=0, description="小计")


class SalesOrderItemCreate(SalesOrderItemBase):
    """创建销售订单明细"""
    pass


class SalesOrderItemUpdate(BaseModel):
    """更新销售订单明细"""
    quantity: Optional[int] = Field(None, ge=1, description="销售数量")
    unit_price: Optional[float] = Field(None, ge=0, description="单价")
    total_price: Optional[float] = Field(None, ge=0, description="小计")


class SalesOrderItemResponse(SalesOrderItemBase):
    """销售订单明细响应"""
    id: int
    order_id: int
    product_name: str = Field(..., description="商品名称")
    product_sku: str = Field(..., description="商品SKU")
    shipped_quantity: int = Field(default=0, description="已出库数量")
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class SalesOrderBase(BaseModel):
    """销售订单基础模型"""
    customer_id: int = Field(..., description="客户ID")
    total_amount: float = Field(..., ge=0, description="订单总金额")
    status: SalesOrderStatus = Field(default=SalesOrderStatus.PENDING, description="订单状态")
    delivery_date: Optional[datetime] = Field(None, description="交货日期")
    delivery_address: Optional[str] = Field(None, description="配送地址")
    sales_person: Optional[str] = Field(None, description="销售员")
    platform: Platform = Field(default=Platform.MANUAL, description="销售平台")
    original_order_no: Optional[str] = Field(None, description="原始订单号(平台订单号)")
    remark: Optional[str] = Field(None, description="备注")


class SalesOrderCreate(SalesOrderBase):
    """创建销售订单"""
    items: List[SalesOrderItemCreate] = Field(..., description="订单明细")


class SalesOrderUpdate(BaseModel):
    """更新销售订单"""
    customer_id: Optional[int] = None
    total_amount: Optional[float] = Field(None, ge=0)
    status: Optional[SalesOrderStatus] = None
    delivery_date: Optional[datetime] = None
    delivery_address: Optional[str] = None
    sales_person: Optional[str] = None
    platform: Optional[Platform] = None
    original_order_no: Optional[str] = None
    remark: Optional[str] = None


class SalesOrderResponse(SalesOrderBase):
    """销售订单响应"""
    id: int
    order_no: str = Field(..., description="销售订单号")
    customer_name: str = Field(..., description="客户名称")
    created_at: datetime
    updated_at: Optional[datetime] = None
    items: List[SalesOrderItemResponse] = Field(default=[], description="订单明细")

    class Config:
        from_attributes = True


class SalesOrderQuery(BaseModel):
    """销售订单查询参数"""
    order_no: Optional[str] = Field(None, description="订单号")
    customer_id: Optional[int] = Field(None, description="客户ID")
    status: Optional[SalesOrderStatus] = Field(None, description="订单状态")
    sales_person: Optional[str] = Field(None, description="销售员")
    platform: Optional[Platform] = Field(None, description="销售平台")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")


class SalesOrderStats(BaseModel):
    """销售订单统计"""
    total_orders: int = Field(..., description="订单总数")
    pending_orders: int = Field(..., description="待处理订单数")
    processing_orders: int = Field(..., description="处理中订单数")
    completed_orders: int = Field(..., description="已完成订单数")
    cancelled_orders: int = Field(..., description="已取消订单数")
    total_amount: float = Field(..., description="订单总金额")
    average_order_amount: float = Field(..., description="平均订单金额")


class SalesOrderListResponse(BaseModel):
    """销售订单列表响应"""
    items: List[SalesOrderResponse]
    total: int
    page: int
    page_size: int
    pages: int


class SalesOutboundStatus(str, Enum):
    """销售出库单状态"""
    PENDING = "pending"
    PARTIAL = "partial"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class SalesOutboundItemBase(BaseModel):
    """销售出库单明细基础模型"""
    sales_order_item_id: Optional[int] = Field(None, description="销售订单明细ID")
    product_id: int = Field(..., description="商品ID")
    quantity: int = Field(..., ge=1, description="出库数量")


class SalesOutboundItemCreate(SalesOutboundItemBase):
    """创建销售出库单明细"""
    pass


class SalesOutboundItemResponse(SalesOutboundItemBase):
    """销售出库单明细响应"""
    id: int
    outbound_id: int
    product_name: str = Field(..., description="商品名称")
    product_sku: str = Field(..., description="商品SKU")
    created_at: datetime

    class Config:
        from_attributes = True


class SalesOutboundBase(BaseModel):
    """销售出库单基础模型"""
    sales_order_id: Optional[int] = Field(None, description="销售订单ID")
    sales_order_no: Optional[str] = Field(None, description="销售订单号")
    customer_id: int = Field(..., description="客户ID")
    warehouse_id: int = Field(..., description="仓库ID")
    status: SalesOutboundStatus = Field(default=SalesOutboundStatus.PENDING, description="出库状态")
    outbound_date: Optional[datetime] = Field(None, description="出库日期")
    operator: Optional[str] = Field(None, description="操作员")
    remark: Optional[str] = Field(None, description="备注")


class SalesOutboundCreate(SalesOutboundBase):
    """创建销售出库单"""
    items: List[SalesOutboundItemCreate] = Field(..., description="出库明细")


class SalesOutboundResponse(SalesOutboundBase):
    """销售出库单响应"""
    id: int
    outbound_no: str = Field(..., description="出库单号")
    customer_name: str = Field(..., description="客户名称")
    warehouse_name: str = Field(..., description="仓库名称")
    created_at: datetime
    updated_at: Optional[datetime] = None
    items: List[SalesOutboundItemResponse] = Field(default=[], description="出库明细")

    class Config:
        from_attributes = True


# ==================== 销售退货单相关Schema ====================

class SalesReturnStatus(str, Enum):
    """销售退货单状态"""
    DRAFT = "draft"
    SUBMITTED = "submitted"
    APPROVED = "approved"
    REJECTED = "rejected"
    RETURNED = "returned"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class SalesReturnItemBase(BaseModel):
    """销售退货单明细基础模型"""
    product_id: int = Field(..., description="商品ID")
    product_name: str = Field(..., description="商品名称")
    product_sku: Optional[str] = Field(None, description="商品SKU")
    return_quantity: int = Field(..., gt=0, description="退货数量")
    unit_price: Decimal = Field(..., ge=0, description="单价")
    total_price: Decimal = Field(..., ge=0, description="小计金额")
    quality_issue: Optional[str] = Field(None, description="质量问题描述")


class SalesReturnItemCreate(SalesReturnItemBase):
    """创建销售退货单明细"""
    pass


class SalesReturnItemUpdate(BaseModel):
    """更新销售退货单明细"""
    product_id: Optional[int] = None
    product_name: Optional[str] = None
    product_sku: Optional[str] = None
    return_quantity: Optional[int] = Field(None, gt=0)
    unit_price: Optional[Decimal] = Field(None, ge=0)
    total_price: Optional[Decimal] = Field(None, ge=0)
    quality_issue: Optional[str] = None


class SalesReturnItem(SalesReturnItemBase):
    """销售退货单明细"""
    id: int
    sales_return_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class SalesReturnBase(BaseModel):
    """销售退货单基础模型"""
    return_no: str = Field(..., description="退货单号")
    sales_order_id: Optional[int] = Field(None, description="销售订单ID")
    customer_id: int = Field(..., description="客户ID")
    return_date: datetime = Field(..., description="退货日期")
    reason: str = Field(..., description="退货原因")
    total_amount: Decimal = Field(..., ge=0, description="退货总金额")
    remark: Optional[str] = Field(None, description="备注")


class SalesReturnCreate(SalesReturnBase):
    """创建销售退货单"""
    items: List[SalesReturnItemCreate] = Field(..., description="退货明细")


class SalesReturnUpdate(BaseModel):
    """更新销售退货单"""
    return_no: Optional[str] = None
    sales_order_id: Optional[int] = None
    customer_id: Optional[int] = None
    return_date: Optional[datetime] = None
    reason: Optional[str] = None
    total_amount: Optional[Decimal] = Field(None, ge=0)
    remark: Optional[str] = None
    items: Optional[List[SalesReturnItemCreate]] = None


class SalesReturnStatusUpdate(BaseModel):
    """更新销售退货单状态"""
    status: SalesReturnStatus = Field(..., description="新状态")
    note: Optional[str] = Field(None, description="操作备注")


class SalesReturn(SalesReturnBase):
    """销售退货单"""
    id: int
    status: SalesReturnStatus

    # 审核信息
    submitted_at: Optional[datetime] = None
    submitted_by: Optional[str] = None
    approved_at: Optional[datetime] = None
    approved_by: Optional[str] = None
    approval_note: Optional[str] = None

    # 退货信息
    returned_at: Optional[datetime] = None
    returned_by: Optional[str] = None

    # 时间戳
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None

    # 关联信息
    customer_name: Optional[str] = None
    sales_order_no: Optional[str] = None

    # 明细
    items: List[SalesReturnItem] = []

    class Config:
        from_attributes = True


class SalesReturnStats(BaseModel):
    """销售退货单统计"""
    total_returns: int = Field(0, description="退货单总数")
    draft_returns: int = Field(0, description="草稿数量")
    submitted_returns: int = Field(0, description="已提交数量")
    approved_returns: int = Field(0, description="已审核数量")
    returned_returns: int = Field(0, description="已退货数量")
    completed_returns: int = Field(0, description="已完成数量")
    total_amount: Decimal = Field(0, description="退货总金额")


class SalesReturnQuery(BaseModel):
    """销售退货单查询参数"""
    return_no: Optional[str] = Field(None, description="退货单号")
    customer_id: Optional[int] = Field(None, description="客户ID")
    status: Optional[SalesReturnStatus] = Field(None, description="状态")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
