<template>
  <div class="logistics-routes-view">
    <div class="page-header">
      <h2>配送路线管理</h2>
      <el-button type="primary" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
        添加路线
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchQuery.origin"
            placeholder="搜索起点"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="searchQuery.destination"
            placeholder="搜索终点"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-select v-model="statusFilter" placeholder="路线状态" @change="handleStatusChange">
            <el-option label="全部状态" value="" />
            <el-option label="活跃" value="active" />
            <el-option label="停用" value="inactive" />
            <el-option label="维护中" value="maintenance" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button @click="fetchRoutes">刷新</el-button>
          <el-button @click="optimizeAllRoutes" :loading="optimizing">路线优化</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 路线列表 -->
    <el-card class="routes-table">
      <el-table
        :data="filteredRoutes"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="route_name" label="路线名称" min-width="150" />
        <el-table-column prop="origin" label="起点" width="120" />
        <el-table-column prop="destination" label="终点" width="120" />
        <el-table-column prop="distance_km" label="距离(km)" width="100">
          <template #default="{ row }">
            {{ row.distance_km?.toFixed(1) }}
          </template>
        </el-table-column>
        <el-table-column prop="estimated_time_hours" label="预计时间(h)" width="120">
          <template #default="{ row }">
            {{ row.estimated_time_hours?.toFixed(1) }}
          </template>
        </el-table-column>
        <el-table-column prop="base_cost" label="基础费用" width="100">
          <template #default="{ row }">
            ¥{{ row.base_cost }}
          </template>
        </el-table-column>
        <el-table-column prop="cost_per_km" label="每公里费用" width="120">
          <template #default="{ row }">
            ¥{{ row.cost_per_km }}
          </template>
        </el-table-column>
        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.is_active)">
              {{ getStatusText(row.is_active) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="editRoute(row)">编辑</el-button>
            <el-button size="small" @click="viewRouteDetails(row)">详情</el-button>
            <el-button 
              size="small" 
              :type="row.is_active === 'active' ? 'warning' : 'success'"
              @click="toggleRouteStatus(row)"
            >
              {{ row.is_active === 'active' ? '停用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑路线对话框 -->
    <el-dialog
      :title="editingRoute ? '编辑路线' : '添加路线'"
      v-model="showAddDialog"
      width="600px"
    >
      <el-form :model="routeForm" label-width="120px">
        <el-form-item label="路线名称" required>
          <el-input v-model="routeForm.route_name" />
        </el-form-item>
        <el-form-item label="起点" required>
          <el-input v-model="routeForm.origin" />
        </el-form-item>
        <el-form-item label="终点" required>
          <el-input v-model="routeForm.destination" />
        </el-form-item>
        <el-form-item label="距离(公里)" required>
          <el-input-number v-model="routeForm.distance_km" :min="0" :precision="1" />
        </el-form-item>
        <el-form-item label="预计时间(小时)" required>
          <el-input-number v-model="routeForm.estimated_time_hours" :min="0" :precision="1" />
        </el-form-item>
        <el-form-item label="基础费用" required>
          <el-input-number v-model="routeForm.base_cost" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="每公里费用" required>
          <el-input-number v-model="routeForm.cost_per_km" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="燃油费用">
          <el-input-number v-model="routeForm.fuel_cost" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="过路费">
          <el-input-number v-model="routeForm.toll_cost" :min="0" :precision="2" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveRoute">保存</el-button>
      </template>
    </el-dialog>

    <!-- 路线详情对话框 -->
    <el-dialog
      title="路线详情"
      v-model="showDetailsDialog"
      width="700px"
    >
      <div v-if="selectedRoute">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="路线名称">{{ selectedRoute.route_name }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(selectedRoute.is_active)">
              {{ getStatusText(selectedRoute.is_active) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="起点">{{ selectedRoute.origin }}</el-descriptions-item>
          <el-descriptions-item label="终点">{{ selectedRoute.destination }}</el-descriptions-item>
          <el-descriptions-item label="距离">{{ selectedRoute.distance_km }} 公里</el-descriptions-item>
          <el-descriptions-item label="预计时间">{{ selectedRoute.estimated_time_hours }} 小时</el-descriptions-item>
          <el-descriptions-item label="基础费用">¥{{ selectedRoute.base_cost }}</el-descriptions-item>
          <el-descriptions-item label="每公里费用">¥{{ selectedRoute.cost_per_km }}</el-descriptions-item>
          <el-descriptions-item label="燃油费用">¥{{ selectedRoute.fuel_cost || 0 }}</el-descriptions-item>
          <el-descriptions-item label="过路费">¥{{ selectedRoute.toll_cost || 0 }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(selectedRoute.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDate(selectedRoute.updated_at) }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="route-details" v-if="selectedRoute.route_details">
          <h4>路线详细信息:</h4>
          <pre>{{ JSON.stringify(selectedRoute.route_details, null, 2) }}</pre>
        </div>
      </div>
    </el-dialog>

    <!-- 路线优化结果对话框 -->
    <el-dialog
      title="路线优化结果"
      v-model="showOptimizationDialog"
      width="800px"
    >
      <div v-if="optimizationResult">
        <div class="optimization-summary">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-value">{{ optimizationResult.total_distance?.toFixed(1) }}</div>
                <div class="summary-label">总距离(km)</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-value">¥{{ optimizationResult.total_cost?.toFixed(2) }}</div>
                <div class="summary-label">总成本</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-value">{{ optimizationResult.total_time?.toFixed(1) }}</div>
                <div class="summary-label">总时间(h)</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-value">{{ (optimizationResult.efficiency_score * 100)?.toFixed(1) }}%</div>
                <div class="summary-label">效率评分</div>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <el-table :data="optimizationResult.optimized_routes" style="margin-top: 20px;">
          <el-table-column prop="sequence" label="顺序" width="80" />
          <el-table-column prop="pickup" label="取货点" />
          <el-table-column prop="delivery" label="配送点" />
          <el-table-column prop="distance_km" label="距离(km)" width="100">
            <template #default="{ row }">
              {{ row.distance_km?.toFixed(1) }}
            </template>
          </el-table-column>
          <el-table-column prop="cost" label="费用" width="100">
            <template #default="{ row }">
              ¥{{ row.cost?.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="estimated_time_hours" label="时间(h)" width="100">
            <template #default="{ row }">
              {{ row.estimated_time_hours?.toFixed(1) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useLogisticsStore } from '../stores/logistics'
import { ElMessage, ElMessageBox } from 'element-plus'

const logisticsStore = useLogisticsStore()

const searchQuery = ref({
  origin: '',
  destination: ''
})
const statusFilter = ref('')
const showAddDialog = ref(false)
const showDetailsDialog = ref(false)
const showOptimizationDialog = ref(false)
const editingRoute = ref(null)
const selectedRoute = ref(null)
const optimizationResult = ref(null)

const routeForm = ref({
  route_name: '',
  origin: '',
  destination: '',
  distance_km: 0,
  estimated_time_hours: 0,
  base_cost: 0,
  cost_per_km: 0,
  fuel_cost: 0,
  toll_cost: 0
})

const routes = ref([])
const loading = ref(false)
const optimizing = ref(false)

const filteredRoutes = computed(() => {
  let filtered = routes.value

  if (searchQuery.value.origin) {
    filtered = filtered.filter(route =>
      route.origin.toLowerCase().includes(searchQuery.value.origin.toLowerCase())
    )
  }

  if (searchQuery.value.destination) {
    filtered = filtered.filter(route =>
      route.destination.toLowerCase().includes(searchQuery.value.destination.toLowerCase())
    )
  }

  if (statusFilter.value) {
    filtered = filtered.filter(route => route.is_active === statusFilter.value)
  }

  return filtered
})

const fetchRoutes = async () => {
  loading.value = true
  try {
    await logisticsStore.fetchRoutes()
    routes.value = logisticsStore.routes
  } catch (error) {
    ElMessage.error('获取路线列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 搜索逻辑已在computed中实现
}

const handleStatusChange = () => {
  // 状态筛选逻辑已在computed中实现
}

const getStatusTagType = (status: string) => {
  const statusMap = {
    'active': 'success',
    'inactive': 'warning',
    'maintenance': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    'active': '活跃',
    'inactive': '停用',
    'maintenance': '维护中'
  }
  return statusMap[status] || status
}

const editRoute = (route) => {
  editingRoute.value = route
  routeForm.value = { ...route }
  showAddDialog.value = true
}

const viewRouteDetails = (route) => {
  selectedRoute.value = route
  showDetailsDialog.value = true
}

const toggleRouteStatus = async (route) => {
  const newStatus = route.is_active === 'active' ? 'inactive' : 'active'
  const action = newStatus === 'active' ? '启用' : '停用'
  
  try {
    await ElMessageBox.confirm(`确定要${action}这条路线吗？`, `确认${action}`, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 这里应该调用API更新路线状态
    route.is_active = newStatus
    ElMessage.success(`路线${action}成功`)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}失败`)
    }
  }
}

const saveRoute = async () => {
  try {
    if (editingRoute.value) {
      // 更新路线
      ElMessage.success('路线更新成功')
    } else {
      // 创建新路线
      await logisticsStore.createRoute(routeForm.value)
      ElMessage.success('路线创建成功')
    }
    
    showAddDialog.value = false
    editingRoute.value = null
    routeForm.value = {
      route_name: '',
      origin: '',
      destination: '',
      distance_km: 0,
      estimated_time_hours: 0,
      base_cost: 0,
      cost_per_km: 0,
      fuel_cost: 0,
      toll_cost: 0
    }
    
    await fetchRoutes()
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const optimizeAllRoutes = async () => {
  optimizing.value = true
  try {
    // 模拟路线优化
    const optimizationData = {
      pickup_points: ['北京', '上海', '广州'],
      delivery_points: ['深圳', '杭州', '成都'],
      vehicle_capacity: 1000,
      optimization_type: 'cost'
    }
    
    const result = await logisticsStore.optimizeRoute(optimizationData)
    
    if (result) {
      optimizationResult.value = result
      showOptimizationDialog.value = true
      ElMessage.success('路线优化完成')
    }
  } catch (error) {
    ElMessage.error('路线优化失败')
  } finally {
    optimizing.value = false
  }
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchRoutes()
})
</script>

<style scoped>
.logistics-routes-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.routes-table {
  margin-bottom: 20px;
}

.route-details {
  margin-top: 20px;
}

.route-details h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
}

.route-details pre {
  background: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
}

.optimization-summary {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.summary-item {
  text-align: center;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.summary-label {
  color: #7f8c8d;
  font-size: 14px;
}
</style>
