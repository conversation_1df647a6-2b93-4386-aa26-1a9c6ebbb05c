<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
      </div>
      <div class="gradient-overlay"></div>
    </div>

    <!-- 主要内容区域 -->
    <div class="login-content">
      <!-- 左侧信息面板 -->
      <div class="info-panel">
        <div class="brand-section">
          <div class="brand-logo">
            <el-icon size="48" color="#ffffff">
              <ShoppingBag />
            </el-icon>
          </div>
          <h1 class="brand-title">电子商务决策系统（演示版）</h1>
          <p class="brand-subtitle">智能选品决策与物流配送管理平台</p>
        </div>
        
        <div class="features-section">
          <div class="feature-item">
            <el-icon size="24" color="#ffffff">
              <TrendCharts />
            </el-icon>
            <div class="feature-text">
              <h3>智能分析</h3>
              <p>基于大数据的商品市场趋势分析</p>
            </div>
          </div>
          
          <div class="feature-item">
            <el-icon size="24" color="#ffffff">
              <Van />
            </el-icon>
            <div class="feature-text">
              <h3>物流优化</h3>
              <p>智能配送路线规划与成本控制</p>
            </div>
          </div>
          
          <div class="feature-item">
            <el-icon size="24" color="#ffffff">
              <DataAnalysis />
            </el-icon>
            <div class="feature-text">
              <h3>决策支持</h3>
              <p>多维度数据分析助力商业决策</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="login-panel">
        <div class="login-form-container">
          <div class="form-header">
            <h2>欢迎回来</h2>
            <p>请登录您的账户以继续使用</p>

            <!-- 测试账号提示 -->
            <div class="test-accounts">
              <el-alert
                title="测试账号"
                type="info"
                :closable="false"
                show-icon
              >
                <template #default>
                  <div class="account-info">
                    <div class="account-item">
                      <span><strong>管理员账户:</strong> admin / 123456</span>
                      <el-button
                        size="small"
                        type="primary"
                        link
                        @click="fillTestAccount('admin')"
                      >
                        一键填充
                      </el-button>
                    </div>
                  </div>
                </template>
              </el-alert>
            </div>
          </div>

          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            class="login-form"
            size="large"
          >
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名或邮箱"
                :prefix-icon="User"
                class="form-input"
              />
            </el-form-item>

            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                :prefix-icon="Lock"
                show-password
                class="form-input"
                @keyup.enter="handleLogin"
              />
            </el-form-item>

            <el-form-item class="form-options-container">
              <div class="form-options">
                <el-checkbox v-model="loginForm.remember">记住我</el-checkbox>
                <el-link type="primary" underline="never" @click="showForgotPassword">
                  忘记密码？
                </el-link>
              </div>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                class="login-button"
                :loading="loading"
                @click="handleLogin"
              >
                <span v-if="!loading">登录</span>
                <span v-else>登录中...</span>
              </el-button>
            </el-form-item>
          </el-form>

          <div class="form-footer">
            <el-divider>
              <span class="divider-text">或者</span>
            </el-divider>
            
            <div class="social-login">
              <el-button class="social-button wechat" circle @click="socialLogin('wechat')">
                <el-icon size="20">
                  <ChatDotRound />
                </el-icon>
              </el-button>
              
              <el-button class="social-button qq" circle @click="socialLogin('qq')">
                <el-icon size="20">
                  <User />
                </el-icon>
              </el-button>
              
              <el-button class="social-button github" circle @click="socialLogin('github')">
                <el-icon size="20">
                  <Link />
                </el-icon>
              </el-button>
            </div>

            <div class="register-link">
              <span>还没有账户？</span>
              <el-link type="primary" underline="never" @click="showRegister">
                立即注册
              </el-link>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 忘记密码对话框 -->
    <el-dialog
      title="重置密码"
      v-model="showForgotDialog"
      width="400px"
      :show-close="false"
    >
      <el-form :model="forgotForm" label-width="80px">
        <el-form-item label="邮箱">
          <el-input v-model="forgotForm.email" placeholder="请输入注册邮箱" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showForgotDialog = false">取消</el-button>
        <el-button type="primary" @click="handleForgotPassword">发送重置链接</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElNotification } from 'element-plus'
import { User, Lock, ChatDotRound, Link } from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const loginFormRef = ref()
const loading = ref(false)
const showForgotDialog = ref(false)

const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

const forgotForm = reactive({
  email: ''
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' },
    { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    loading.value = true

    // 使用认证store进行登录
    const result = await authStore.login({
      username: loginForm.username,
      password: loginForm.password,
      remember: loginForm.remember
    })

    if (result.success) {
      ElNotification({
        title: '登录成功',
        message: '欢迎回来！',
        type: 'success',
        duration: 3000
      })

      // 跳转到首页或原来要访问的页面
      const redirect = router.currentRoute.value.query.redirect as string
      router.push(redirect || '/')
    } else {
      ElMessage.error(result.message || '登录失败')
    }
  } catch (error) {
    ElMessage.error('登录失败，请重试')
  } finally {
    loading.value = false
  }
}

const socialLogin = (platform: string) => {
  ElMessage.info(`${platform} 登录功能开发中...`)
}

const showForgotPassword = () => {
  showForgotDialog.value = true
}

const handleForgotPassword = () => {
  if (!forgotForm.email) {
    ElMessage.warning('请输入邮箱地址')
    return
  }
  
  ElMessage.success('重置链接已发送到您的邮箱')
  showForgotDialog.value = false
  forgotForm.email = ''
}

const showRegister = () => {
  ElMessage.info('注册功能开发中...')
}

const fillTestAccount = (accountType: string) => {
  if (accountType === 'admin') {
    loginForm.username = 'admin'
    loginForm.password = '123456'
    ElMessage.success('已填充管理员测试账号')
  }
}
</script>

<style scoped>
.login-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: stretch;
  justify-content: stretch;
  padding: 0;
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 背景装饰 */
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 1;
}

.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 20%;
  right: 10%;
  animation-delay: 1s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation-delay: 2s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  right: 20%;
  animation-delay: 3s;
}

.shape-5 {
  width: 140px;
  height: 140px;
  top: 50%;
  left: 5%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
}

/* 主要内容区域 */
.login-content {
  position: relative;
  z-index: 10;
  display: flex;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

/* 左侧信息面板 */
.info-panel {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 30px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: white;
  position: relative;
  height: 100vh;
}

.info-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.brand-section {
  position: relative;
  z-index: 2;
  text-align: center;
  margin-bottom: 60px;
}

.brand-logo {
  margin-bottom: 20px;
}

.brand-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  background: linear-gradient(45deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  line-height: 1.6;
}

.features-section {
  position: relative;
  z-index: 2;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  transition: transform 0.3s ease;
}

.feature-item:hover {
  transform: translateX(10px);
}

.feature-text {
  margin-left: 16px;
}

.feature-text h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 4px;
}

.feature-text p {
  font-size: 0.9rem;
  opacity: 0.8;
  line-height: 1.4;
}

/* 右侧登录表单 */
.login-panel {
  flex: 1;
  padding: 40px 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  height: 100vh;
}

.login-form-container {
  width: 100%;
  max-width: 400px;
}

.form-header {
  text-align: center;
  margin-bottom: 40px;
}

.form-header h2 {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 8px;
}

.form-header p {
  color: #7f8c8d;
  font-size: 1rem;
}

.test-accounts {
  margin-top: 20px;
  margin-bottom: 10px;
}

.test-accounts :deep(.el-alert) {
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
}

.test-accounts :deep(.el-alert__title) {
  color: #1e40af;
  font-weight: 600;
  font-size: 14px;
}

.account-info {
  margin-top: 8px;
}

.account-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 6px 0;
  padding: 4px 0;
}

.account-item span {
  font-size: 13px;
  color: #374151;
  line-height: 1.4;
}

.account-item strong {
  color: #1e40af;
  font-weight: 600;
}

.account-item .el-button {
  font-size: 12px;
  padding: 2px 8px;
}

.login-form {
  margin-bottom: 30px;
}

.form-input {
  margin-bottom: 20px;
}

.form-input :deep(.el-input) {
  --el-input-border-color: #e1e8ed;
  --el-input-hover-border-color: #c0c4cc;
  --el-input-focus-border-color: #667eea;
}

.form-input :deep(.el-input__wrapper) {
  height: 50px;
  border-radius: 12px;
  border: 1px solid #e1e8ed;
  transition: all 0.3s ease;
  box-shadow: none;
}

.form-input :deep(.el-input__wrapper:hover) {
  border-color: #c0c4cc;
}

.form-input :deep(.el-input.is-focus .el-input__wrapper) {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.form-input :deep(.el-input__inner) {
  border: none;
  box-shadow: none;
  outline: none;
}

.form-options {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  width: 100% !important;
  margin-bottom: 20px;
}

.form-options .el-checkbox {
  margin-right: 0 !important;
  flex-shrink: 0;
}

.form-options .el-link {
  margin-left: 0 !important;
  flex-shrink: 0;
}

/* 确保表单项内容完全填充 */
.login-form :deep(.el-form-item__content) {
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* 特别针对包含form-options的表单项 */
.form-options-container :deep(.el-form-item__content) {
  width: 100% !important;
  display: block !important;
}

.form-options-container {
  width: 100%;
}

.form-options-container :deep(.el-form-item__content) > .form-options {
  width: 100% !important;
}



.login-button {
  width: 100%;
  height: 50px;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.form-footer {
  text-align: center;
}

.divider-text {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.social-login {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin: 20px 0;
}

.social-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 2px solid #e1e8ed;
  transition: all 0.3s ease;
}

.social-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.social-button.wechat:hover {
  background: #07c160;
  border-color: #07c160;
  color: white;
}

.social-button.qq:hover {
  background: #12b7f5;
  border-color: #12b7f5;
  color: white;
}

.social-button.github:hover {
  background: #333;
  border-color: #333;
  color: white;
}

.register-link {
  margin-top: 20px;
  color: #7f8c8d;
}

.register-link span {
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .login-content {
    width: 100%;
    height: 100%;
  }

  .info-panel {
    flex: 1;
    padding: 30px 25px;
    height: 100vh;
  }

  .login-panel {
    padding: 30px 25px;
    height: 100vh;
  }
}

@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
    width: 100%;
    height: 100vh;
  }

  .info-panel {
    flex: 1;
    padding: 25px 20px;
    height: 50vh;
    min-height: 50vh;
  }

  .brand-title {
    font-size: 2rem;
  }

  .login-panel {
    flex: 1;
    padding: 25px 20px;
    height: 50vh;
    min-height: 50vh;
  }

  .feature-item {
    padding: 15px;
    margin-bottom: 15px;
  }

  .brand-section {
    margin-bottom: 30px;
  }

  .test-accounts {
    margin: 15px 0;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 0;
  }

  .login-content {
    width: 100%;
    height: 100vh;
    margin: 0;
  }

  .info-panel {
    padding: 20px 15px;
    height: 45vh;
    min-height: 45vh;
  }

  .login-panel {
    padding: 20px 15px;
    height: 55vh;
    min-height: 55vh;
  }

  .brand-title {
    font-size: 1.8rem;
  }

  .feature-item {
    padding: 12px;
    margin-bottom: 12px;
  }
}
</style>
