"""
供应商相关的Pydantic模型
"""

from pydantic import BaseModel, Field, EmailStr
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum


class SupplierType(str, Enum):
    """供应商类型枚举"""
    MANUFACTURER = "manufacturer"
    WHOLESALER = "wholesaler"
    AGENT = "agent"
    TRADER = "trader"
    SERVICE = "service"


class SupplierStatus(str, Enum):
    """供应商状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    TERMINATED = "terminated"
    PENDING = "pending"


class CreditRating(str, Enum):
    """信用等级枚举"""
    AAA = "AAA"
    AA = "AA"
    A = "A"
    BBB = "BBB"
    BB = "BB"
    B = "B"
    CCC = "CCC"
    CC = "CC"
    C = "C"
    D = "D"


class SupplierBase(BaseModel):
    name: str = Field(..., description="供应商名称")
    code: str = Field(..., description="供应商编码")
    contact_person: Optional[str] = Field(None, description="联系人")
    phone: Optional[str] = Field(None, description="联系电话")
    email: Optional[EmailStr] = Field(None, description="邮箱")
    address: Optional[str] = Field(None, description="地址")
    category: Optional[str] = Field(None, description="供应商类别")
    region: Optional[str] = Field(None, description="所在地区")
    credit_rating: CreditRating = Field(default=CreditRating.A, description="信用等级")
    payment_terms: Optional[str] = Field(None, description="付款条件")
    delivery_terms: Optional[str] = Field(None, description="交货条件")
    credit_limit: float = Field(default=0.0, ge=0, description="信用额度")
    cooperation_years: int = Field(default=0, ge=0, description="合作年限")
    main_products: Optional[str] = Field(None, description="主营产品")
    status: SupplierStatus = Field(default=SupplierStatus.ACTIVE, description="供应商状态")
    business_license: Optional[str] = Field(None, description="营业执照号")
    tax_number: Optional[str] = Field(None, description="税号")
    remark: Optional[str] = Field(None, description="备注")


class SupplierCreate(SupplierBase):
    bank_account: Optional[Dict[str, Any]] = Field(None, description="银行账户信息")
    certificates: Optional[Dict[str, Any]] = Field(None, description="资质证书")


class SupplierUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    contact_person: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    address: Optional[str] = None
    category: Optional[str] = None
    region: Optional[str] = None
    credit_rating: Optional[CreditRating] = None
    payment_terms: Optional[str] = None
    delivery_terms: Optional[str] = None
    credit_limit: Optional[float] = Field(None, ge=0)
    cooperation_years: Optional[int] = Field(None, ge=0)
    main_products: Optional[str] = None
    status: Optional[SupplierStatus] = None
    business_license: Optional[str] = None
    tax_number: Optional[str] = None
    remark: Optional[str] = None
    bank_account: Optional[Dict[str, Any]] = None
    certificates: Optional[Dict[str, Any]] = None


class Supplier(SupplierBase):
    id: int
    current_balance: float = 0.0
    is_active: bool = True
    bank_account: Optional[Dict[str, Any]] = None
    certificates: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class SupplierListResponse(BaseModel):
    """供应商列表响应"""
    items: List[Supplier]
    total: int
    page: int
    page_size: int
    total_pages: int


class SupplierQuery(BaseModel):
    """供应商查询参数"""
    name: Optional[str] = Field(None, description="供应商名称")
    code: Optional[str] = Field(None, description="供应商编码")
    category: Optional[str] = Field(None, description="供应商类别")
    status: Optional[SupplierStatus] = Field(None, description="供应商状态")
    credit_rating: Optional[CreditRating] = Field(None, description="信用等级")
    contact_person: Optional[str] = Field(None, description="联系人")
    address: Optional[str] = Field(None, description="地址/地区")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")


# 扩展字段用于前端显示
class SupplierArchive(Supplier):
    """供应商档案（前端显示用）"""
    type: Optional[str] = None  # 兼容前端的type字段
    region: Optional[str] = None  # 兼容前端的region字段
    cooperation_years: Optional[int] = None  # 兼容前端的cooperation_years字段
    main_products: Optional[str] = None  # 兼容前端的main_products字段
    remarks: Optional[str] = None  # 兼容前端的remarks字段
