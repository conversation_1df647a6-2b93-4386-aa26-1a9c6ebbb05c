<template>
  <div class="customer-archive-view">
    <div class="page-header">
      <div class="header-left">
        <h2>客户档案</h2>
        <p class="page-description">管理客户基础信息，建立完整的客户数据库</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          新增客户
        </el-button>
        <el-button @click="exportCustomers">
          <el-icon><Download /></el-icon>
          导出档案
        </el-button>
      </div>
    </div>

    <!-- 客户概览 -->
    <el-row :gutter="20" class="customer-overview">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon total">
              <el-icon size="32"><User /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ customerStats.total_customers }}</div>
              <div class="overview-label">客户总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon active">
              <el-icon size="32"><CircleCheck /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ customerStats.active_customers }}</div>
              <div class="overview-label">活跃客户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon vip">
              <el-icon size="32"><Star /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ customerStats.vip_customers }}</div>
              <div class="overview-label">VIP客户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon new">
              <el-icon size="32"><UserFilled /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ customerStats.new_customers }}</div>
              <div class="overview-label">新客户</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true">
        <el-form-item label="客户姓名">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入客户姓名"
            clearable
            style="width: 200px"
            @keyup.enter="searchCustomers"
            @clear="searchCustomers"
          />
        </el-form-item>
        
        <el-form-item label="客户类型">
          <el-select v-model="searchForm.type" placeholder="选择类型" clearable style="width: 150px" @change="searchCustomers" @clear="searchCustomers">
            <el-option label="个人客户" value="individual" />
            <el-option label="企业客户" value="enterprise" />
            <el-option label="分销商" value="distributor" />
          </el-select>
        </el-form-item>

        <el-form-item label="客户等级">
          <el-select v-model="searchForm.level" placeholder="选择等级" clearable style="width: 120px" @change="searchCustomers" @clear="searchCustomers">
            <el-option label="普通客户" value="normal" />
            <el-option label="VIP客户" value="vip" />
            <el-option label="潜在客户" value="potential" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="所在地区">
          <el-input
            v-model="searchForm.region"
            placeholder="请输入地区"
            clearable
            style="width: 150px"
            @keyup.enter="searchCustomers"
            @clear="searchCustomers"
          />
        </el-form-item>
        
        <el-form-item label="客户状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 120px" @change="searchCustomers" @clear="searchCustomers">
            <el-option label="活跃" value="active" />
            <el-option label="不活跃" value="inactive" />
            <el-option label="黑名单" value="blacklist" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="searchCustomers">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
          <span v-if="hasSearchConditions" class="search-tip">
            <el-icon><CircleCheck /></el-icon>
            已应用搜索条件
          </span>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 客户档案列表 -->
    <el-card class="archive-list-card">
      <template #header>
        <div class="card-header">
          <span>客户档案列表 (共 {{ total }} 条)</span>
          <div class="header-actions">
            <el-button-group>
              <el-button :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
                <el-icon><List /></el-icon>
                列表视图
              </el-button>
              <el-button :type="viewMode === 'card' ? 'primary' : ''" @click="viewMode = 'card'">
                <el-icon><Grid /></el-icon>
                卡片视图
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'">
        <el-table :data="customerArchives" style="width: 100%" v-loading="loading">
          <el-table-column type="selection" width="55" />
          
          <el-table-column label="客户信息" min-width="250">
            <template #default="{ row }">
              <div class="customer-info">
                <div class="customer-avatar">
                  <el-avatar :size="50" :src="row.avatar">
                    <el-icon><User /></el-icon>
                  </el-avatar>
                </div>
                <div class="customer-details">
                  <div class="customer-name">{{ row.name }}</div>
                  <div class="customer-code">编码: {{ row.code }}</div>
                  <div class="customer-phone">{{ row.phone }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="type" label="客户类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getTypeTagType(row.type)">{{ getTypeLabel(row.type) }}</el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="level" label="客户等级" width="100">
            <template #default="{ row }">
              <el-tag :type="getLevelTagType(row.level)">{{ getLevelLabel(row.level) }}</el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="region" label="所在地区" width="120" />
          
          <el-table-column prop="total_orders" label="订单数量" width="100">
            <template #default="{ row }">
              <span class="order-count">{{ row.total_orders }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="total_amount" label="消费金额" width="120">
            <template #default="{ row }">
              <span class="amount">¥{{ row.total_amount.toFixed(2) }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="last_order_date" label="最后下单" width="120">
            <template #default="{ row }">
              {{ row.last_order_date ? formatDate(row.last_order_date) : '无' }}
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="客户状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="created_at" label="创建时间" width="120">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="viewCustomer(row)">查看</el-button>
              <el-button size="small" type="primary" @click="editCustomer(row)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteCustomer(row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <div class="customers-grid">
          <div 
            v-for="customer in customerArchives" 
            :key="customer.id"
            class="customer-card"
          >
            <div class="card-header">
              <div class="customer-avatar-section">
                <el-avatar :size="60" :src="customer.avatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <div class="customer-level">
                  <el-tag :type="getLevelTagType(customer.level)" size="small">
                    {{ getLevelLabel(customer.level) }}
                  </el-tag>
                </div>
              </div>
              <div class="customer-status">
                <el-tag :type="getStatusTagType(customer.status)" size="small">
                  {{ getStatusLabel(customer.status) }}
                </el-tag>
              </div>
            </div>
            
            <div class="card-content">
              <h4 class="card-title">{{ customer.name }}</h4>
              <p class="card-code">编码: {{ customer.code }}</p>
              <div class="card-type">
                <el-tag :type="getTypeTagType(customer.type)" size="small">
                  {{ getTypeLabel(customer.type) }}
                </el-tag>
              </div>
              
              <div class="customer-stats">
                <div class="stat-row">
                  <span class="label">联系电话:</span>
                  <span class="value">{{ customer.phone }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">所在地区:</span>
                  <span class="value">{{ customer.region }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">订单数量:</span>
                  <span class="order-count">{{ customer.total_orders }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">消费金额:</span>
                  <span class="amount">¥{{ customer.total_amount.toFixed(2) }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">最后下单:</span>
                  <span class="value">{{ customer.last_order_date ? formatDate(customer.last_order_date) : '无' }}</span>
                </div>
              </div>
            </div>
            
            <div class="card-actions">
              <el-button size="small" @click="viewCustomer(customer)">查看</el-button>
              <el-button size="small" type="primary" @click="editCustomer(customer)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteCustomer(customer.id)">删除</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑客户对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingCustomer ? '编辑客户档案' : '新增客户档案'"
      width="800px"
      @close="resetForm"
    >
      <el-form :model="customerForm" :rules="formRules" ref="formRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户姓名" prop="name">
              <el-input v-model="customerForm.name" placeholder="请输入客户姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户编码" prop="code">
              <el-input v-model="customerForm.code" placeholder="请输入客户编码" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户类型" prop="type">
              <el-select v-model="customerForm.type" placeholder="选择类型" style="width: 100%">
                <el-option label="个人客户" value="individual" />
                <el-option label="企业客户" value="enterprise" />
                <el-option label="分销商" value="distributor" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户等级" prop="level">
              <el-select v-model="customerForm.level" placeholder="选择等级" style="width: 100%">
                <el-option label="普通客户" value="normal" />
                <el-option label="VIP客户" value="vip" />
                <el-option label="潜在客户" value="potential" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="customerForm.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱地址" prop="email">
              <el-input v-model="customerForm.email" placeholder="请输入邮箱地址" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所在地区" prop="region">
              <el-input v-model="customerForm.region" placeholder="请输入所在地区" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户状态" prop="status">
              <el-select v-model="customerForm.status" placeholder="选择状态" style="width: 100%">
                <el-option label="活跃" value="active" />
                <el-option label="不活跃" value="inactive" />
                <el-option label="黑名单" value="blacklist" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="详细地址">
          <el-input
            v-model="customerForm.address"
            placeholder="请输入详细地址"
          />
        </el-form-item>
        
        <el-form-item label="客户头像">
          <el-input v-model="customerForm.avatar" placeholder="请输入头像URL" />
        </el-form-item>
        
        <el-form-item label="备注信息">
          <el-input
            v-model="customerForm.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCustomer" :loading="saving">
          {{ editingCustomer ? '更新' : '保存' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 客户详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="客户档案详情"
      width="700px"
    >
      <div v-if="selectedCustomer" class="customer-detail">
        <div class="detail-header">
          <div class="detail-avatar">
            <el-avatar :size="80" :src="selectedCustomer.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
          </div>
          <div class="detail-info">
            <h3>{{ selectedCustomer.name }}</h3>
            <p><strong>编码:</strong> {{ selectedCustomer.code }}</p>
            <p><strong>类型:</strong> {{ getTypeLabel(selectedCustomer.type) }}</p>
            <p><strong>等级:</strong> 
              <el-tag :type="getLevelTagType(selectedCustomer.level)">
                {{ getLevelLabel(selectedCustomer.level) }}
              </el-tag>
            </p>
            <p><strong>状态:</strong> 
              <el-tag :type="getStatusTagType(selectedCustomer.status)">
                {{ getStatusLabel(selectedCustomer.status) }}
              </el-tag>
            </p>
          </div>
        </div>
        
        <div class="detail-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="联系电话">{{ selectedCustomer.phone }}</el-descriptions-item>
            <el-descriptions-item label="邮箱地址">{{ selectedCustomer.email }}</el-descriptions-item>
            <el-descriptions-item label="所在地区">{{ selectedCustomer.region }}</el-descriptions-item>
            <el-descriptions-item label="订单数量">{{ selectedCustomer.total_orders }}</el-descriptions-item>
            <el-descriptions-item label="消费金额">¥{{ selectedCustomer.total_amount.toFixed(2) }}</el-descriptions-item>
            <el-descriptions-item label="最后下单">{{ selectedCustomer.last_order_date ? formatDate(selectedCustomer.last_order_date) : '无' }}</el-descriptions-item>
            <el-descriptions-item label="详细地址" :span="2">{{ selectedCustomer.address }}</el-descriptions-item>
            <el-descriptions-item label="创建时间" :span="2">{{ formatDateTime(selectedCustomer.created_at) }}</el-descriptions-item>
            <el-descriptions-item label="更新时间" :span="2">{{ formatDateTime(selectedCustomer.updated_at) }}</el-descriptions-item>
          </el-descriptions>
          
          <div class="detail-section" v-if="selectedCustomer.remarks">
            <h4>备注信息</h4>
            <p>{{ selectedCustomer.remarks }}</p>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Download,
  User,
  CircleCheck,
  Star,
  UserFilled,
  Search,
  List,
  Grid
} from '@element-plus/icons-vue'
import { customerApi, type Customer, type CustomerStats } from '@/api/customers'

// 类型定义
interface CustomerArchive {
  id: number
  name: string
  code: string
  type: 'individual' | 'enterprise' | 'distributor'
  level: 'normal' | 'vip' | 'potential'
  phone: string
  email: string
  region: string
  address: string
  avatar?: string
  status: 'active' | 'inactive' | 'blacklist'
  total_orders: number
  total_amount: number
  last_order_date?: string
  remarks?: string
  created_at: string
  updated_at: string
}



interface SearchForm {
  name: string
  type: string
  level: string
  region: string
  status: string
}

interface CustomerForm {
  name: string
  code: string
  type: string
  level: string
  phone: string
  email: string
  region: string
  address: string
  avatar: string
  status: string
  remarks: string
}

// 响应式数据
const customerArchives = ref<CustomerArchive[]>([])
const loading = ref(false)
const saving = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const viewMode = ref<'table' | 'card'>('table')

const showAddDialog = ref(false)
const showDetailDialog = ref(false)
const editingCustomer = ref<CustomerArchive | null>(null)
const selectedCustomer = ref<CustomerArchive | null>(null)

const customerStats = ref<CustomerStats>({
  total_customers: 0,
  active_customers: 0,
  vip_customers: 0,
  new_customers: 0,
  total_orders: 0,
  total_amount: 0
})

const searchForm = reactive<SearchForm>({
  name: '',
  type: '',
  level: '',
  region: '',
  status: ''
})

// 计算属性：检查是否有搜索条件
const hasSearchConditions = computed(() => {
  return searchForm.name.trim() !== '' ||
         searchForm.type !== '' ||
         searchForm.level !== '' ||
         searchForm.region.trim() !== '' ||
         searchForm.status !== ''
})

const customerForm = reactive<CustomerForm>({
  name: '',
  code: '',
  type: '',
  level: '',
  phone: '',
  email: '',
  region: '',
  address: '',
  avatar: '',
  status: 'active',
  remarks: ''
})

const formRef = ref()

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入客户姓名', trigger: 'blur' }],
  code: [{ required: true, message: '请输入客户编码', trigger: 'blur' }],
  type: [{ required: true, message: '请选择客户类型', trigger: 'change' }],
  level: [{ required: true, message: '请选择客户等级', trigger: 'change' }],
  phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  region: [{ required: true, message: '请输入所在地区', trigger: 'blur' }],
  status: [{ required: true, message: '请选择客户状态', trigger: 'change' }]
}

// 方法
const fetchCustomerArchives = async () => {
  loading.value = true
  try {


    // 构建查询参数
    const params: any = {
      page: currentPage.value,
      page_size: pageSize.value
    }

    // 添加搜索条件
    if (searchForm.name.trim()) {
      params.name = searchForm.name.trim()
    }
    if (searchForm.type) {
      params.customer_type = searchForm.type
    }
    if (searchForm.level) {
      params.level = searchForm.level
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }
    if (searchForm.region.trim()) {
      params.address = searchForm.region.trim()
    }

    // 调用客户API
    const response = await customerApi.getCustomers(params)



    // 由于响应拦截器已经返回了response.data，所以response就是实际数据
    const actualData = response as any






    if (!actualData.items || actualData.items.length === 0) {
      customerArchives.value = []
      total.value = 0
      return
    }

    customerArchives.value = actualData.items.map((customer: Customer) => ({
      id: customer.id,
      name: customer.name,
      code: customer.code,
      type: customer.customer_type,
      level: customer.level,
      phone: customer.phone || '',
      email: customer.email || '',
      region: customer.address || '', // 使用address作为region
      address: customer.default_delivery_address || customer.address || '',
      avatar: `https://picsum.photos/100/100?random=${customer.id}`,
      status: customer.status,
      total_orders: customer.total_orders,
      total_amount: customer.total_amount,
      last_order_date: customer.last_order_date || '',
      remarks: customer.remark || '',
      created_at: customer.created_at,
      updated_at: customer.updated_at || ''
    }))

    total.value = actualData.total



    // 获取统计数据
    const statsResponse = await customerApi.getCustomerStats()
    customerStats.value = statsResponse as any
  } catch (error) {
    ElMessage.error('获取客户档案失败')

    // 确保数据有默认值
    customerArchives.value = []
    total.value = 0
    customerStats.value = {
      total_customers: 0,
      active_customers: 0,
      vip_customers: 0,
      new_customers: 0,
      total_orders: 0,
      total_amount: 0
    }
  } finally {
    loading.value = false
  }
}

const searchCustomers = () => {
  // 重置到第一页
  currentPage.value = 1
  // 执行搜索
  fetchCustomerArchives()
}

const resetSearch = () => {
  // 清空搜索表单
  Object.assign(searchForm, {
    name: '',
    type: '',
    level: '',
    region: '',
    status: ''
  })
  // 重置到第一页
  currentPage.value = 1
  // 重新获取数据
  fetchCustomerArchives()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchCustomerArchives()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchCustomerArchives()
}

const viewCustomer = (customer: CustomerArchive) => {
  selectedCustomer.value = customer
  showDetailDialog.value = true
}

const editCustomer = (customer: CustomerArchive) => {
  editingCustomer.value = customer
  Object.assign(customerForm, {
    name: customer.name,
    code: customer.code,
    type: customer.type,
    level: customer.level,
    phone: customer.phone,
    email: customer.email,
    region: customer.region,
    address: customer.address,
    avatar: customer.avatar || '',
    status: customer.status,
    remarks: customer.remarks || ''
  })
  showAddDialog.value = true
}

const deleteCustomer = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个客户档案吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await customerApi.deleteCustomer(id)
    ElMessage.success('删除成功')

    // 重新加载数据
    await fetchCustomerArchives()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const saveCustomer = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    if (editingCustomer.value) {
      // 更新现有客户
      const updateData = {
        name: customerForm.name,
        code: customerForm.code,
        contact_person: customerForm.name, // 使用name作为联系人
        phone: customerForm.phone,
        email: customerForm.email,
        address: customerForm.address,
        customer_type: customerForm.type as any,
        level: customerForm.level as any,
        status: customerForm.status as any,
        default_delivery_address: customerForm.address,
        remark: customerForm.remarks
      }

      await customerApi.updateCustomer(editingCustomer.value.id, updateData)
      ElMessage.success('更新成功')
    } else {
      // 新增客户
      const createData = {
        name: customerForm.name,
        code: customerForm.code,
        contact_person: customerForm.name, // 使用name作为联系人
        phone: customerForm.phone,
        email: customerForm.email,
        address: customerForm.address,
        customer_type: customerForm.type as any,
        level: customerForm.level as any,
        status: customerForm.status as any,
        default_delivery_address: customerForm.address,
        remark: customerForm.remarks
      }

      await customerApi.createCustomer(createData)
      ElMessage.success('新增成功')
    }

    showAddDialog.value = false
    resetForm()
    // 重新加载数据
    await fetchCustomerArchives()
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  editingCustomer.value = null
  Object.assign(customerForm, {
    name: '',
    code: '',
    type: '',
    level: '',
    phone: '',
    email: '',
    region: '',
    address: '',
    avatar: '',
    status: 'active',
    remarks: ''
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const exportCustomers = () => {
  ElMessage.success('客户档案导出成功')
}

// 辅助方法
const getTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    'individual': '个人客户',
    'enterprise': '企业客户',
    'distributor': '分销商'
  }
  return labelMap[type] || type
}

const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    'individual': 'primary',
    'enterprise': 'success',
    'distributor': 'warning'
  }
  return typeMap[type] || 'info'
}

const getLevelLabel = (level: string) => {
  const labelMap: Record<string, string> = {
    'normal': '普通客户',
    'vip': 'VIP客户',
    'potential': '潜在客户'
  }
  return labelMap[level] || level
}

const getLevelTagType = (level: string) => {
  const levelMap: Record<string, string> = {
    'normal': 'info',
    'vip': 'success',
    'potential': 'warning'
  }
  return levelMap[level] || 'info'
}

const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    'active': '活跃',
    'inactive': '不活跃',
    'blacklist': '黑名单'
  }
  return labelMap[status] || status
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    'active': 'success',
    'inactive': 'warning',
    'blacklist': 'danger'
  }
  return statusMap[status] || 'info'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchCustomerArchives()
})
</script>

<style scoped>
.customer-archive-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
}

.page-description {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 客户概览 */
.customer-overview {
  margin-bottom: 20px;
}

.overview-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.overview-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.overview-icon {
  padding: 12px;
  border-radius: 12px;
  flex-shrink: 0;
}

.overview-icon.total {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-icon.active {
  background: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.overview-icon.vip {
  background: rgba(245, 108, 108, 0.1);
  color: #F56C6C;
}

.overview-icon.new {
  background: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.overview-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 14px;
  color: #7f8c8d;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-tip {
  margin-left: 10px;
  color: #67c23a;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 档案列表卡片 */
.archive-list-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 客户信息 */
.customer-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.customer-avatar {
  flex-shrink: 0;
}

.customer-details {
  flex: 1;
}

.customer-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.customer-code {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.customer-phone {
  font-size: 12px;
  color: #606266;
}

/* 数值样式 */
.order-count {
  font-weight: 600;
  color: #409EFF;
}

.amount {
  font-weight: 600;
  color: #E6A23C;
}

/* 卡片视图 */
.card-view {
  margin-top: 20px;
}

.customers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.customer-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.customer-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.customer-card .card-header {
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.customer-avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.customer-level {
  margin-top: 4px;
}

.card-content {
  padding: 16px;
}

.card-title {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.card-code {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #909399;
}

.card-type {
  margin-bottom: 16px;
}

.customer-stats {
  margin-top: 12px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.stat-row .label {
  color: #606266;
}

.stat-row .value {
  color: #2c3e50;
}

.card-actions {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 8px;
}

/* 分页 */
.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 客户详情 */
.customer-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.detail-avatar {
  flex-shrink: 0;
}

.detail-info {
  flex: 1;
}

.detail-info h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 20px;
}

.detail-info p {
  margin: 4px 0;
  color: #606266;
}

.detail-content {
  margin-top: 20px;
}

.detail-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 16px;
}

.detail-section p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 20px;
}

/* 按钮样式 */
.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .customer-overview .el-col {
    margin-bottom: 16px;
  }

  .customers-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (max-width: 768px) {
  .customer-archive-view {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    justify-content: flex-start;
  }

  .customers-grid {
    grid-template-columns: 1fr;
  }

  .detail-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .customer-info {
    flex-direction: column;
    text-align: center;
  }
}
</style>
