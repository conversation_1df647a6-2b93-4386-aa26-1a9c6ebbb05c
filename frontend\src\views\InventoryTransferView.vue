<template>
  <div class="inventory-transfer-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>库存调拨单</h2>
        <p class="page-description">管理库存调拨单，跟踪仓库间商品调拨流程</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="openCreateDialog">
          <el-icon><Plus /></el-icon>
          新建调拨单
        </el-button>
        <el-button @click="exportTransfers">
          <el-icon><Download /></el-icon>
          导出调拨单
        </el-button>
      </div>
    </div>

    <!-- 调拨单概览 -->
    <el-row :gutter="20" class="transfer-overview">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon total">
              <el-icon size="32"><Tickets /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ transferStats.total_transfers }}</div>
              <div class="overview-label">调拨单总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon pending">
              <el-icon size="32"><Clock /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ transferStats.draft_transfers }}</div>
              <div class="overview-label">草稿</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon processing">
              <el-icon size="32"><Loading /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ transferStats.submitted_transfers }}</div>
              <div class="overview-label">已提交</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon completed">
              <el-icon size="32"><CircleCheck /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ transferStats.approved_transfers }}</div>
              <div class="overview-label">已审核</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true">
        <el-form-item label="调拨单号">
          <el-input
            v-model="searchForm.transfer_no"
            placeholder="请输入调拨单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="调出仓库">
          <el-select v-model="searchForm.from_warehouse_id" placeholder="选择调出仓库" clearable style="width: 200px">
            <el-option
              v-for="warehouse in warehouses"
              :key="warehouse.id"
              :label="warehouse.name"
              :value="warehouse.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="调入仓库">
          <el-select v-model="searchForm.to_warehouse_id" placeholder="选择调入仓库" clearable style="width: 200px">
            <el-option
              v-for="warehouse in warehouses"
              :key="warehouse.id"
              :label="warehouse.name"
              :value="warehouse.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="单据状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 150px">
            <el-option label="草稿" value="draft" />
            <el-option label="已提交" value="submitted" />
            <el-option label="已审核" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="已调拨" value="transferred" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-form-item>

        <el-form-item label="调拨日期">
          <el-date-picker
            v-model="searchForm.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button @click="fetchInventoryTransfers" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 调拨单列表 -->
    <el-card class="transfer-list-card">
      <template #header>
        <div class="card-header">
          <span>调拨单列表</span>
          <div class="header-actions">
            <el-button-group>
              <el-button 
                :type="viewMode === 'table' ? 'primary' : ''" 
                @click="viewMode = 'table'"
                :icon="List"
              >
                表格视图
              </el-button>
              <el-button 
                :type="viewMode === 'card' ? 'primary' : ''" 
                @click="viewMode = 'card'"
                :icon="Grid"
              >
                卡片视图
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'">
        <el-table :data="inventoryTransfers" style="width: 100%" v-loading="loading">
          <el-table-column prop="transfer_no" label="调拨单号" width="140" />
          <el-table-column prop="from_warehouse_name" label="调出仓库" width="120" />
          <el-table-column prop="to_warehouse_name" label="调入仓库" width="120" />
          <el-table-column prop="total_quantity" label="调拨数量" width="100">
            <template #default="{ row }">
              <span class="quantity">{{ row.total_quantity }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="transfer_date" label="调拨日期" width="120">
            <template #default="{ row }">
              {{ formatDate(row.transfer_date) }}
            </template>
          </el-table-column>
          <el-table-column prop="reason" label="调拨原因" min-width="200" show-overflow-tooltip />
          <el-table-column prop="created_at" label="创建时间" width="150">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="240" fixed="right">
            <template #default="{ row }">
              <div class="table-actions">
                <el-button size="small" @click="viewTransferDetail(row)">查看</el-button>
                <el-dropdown @command="(command: string) => handleTransferAction(command, row)" v-if="row.status !== 'completed'">
                  <el-button size="small" type="primary">
                    操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit" v-if="row.status === 'draft'">编辑</el-dropdown-item>
                      <el-dropdown-item command="submit" v-if="row.status === 'draft'">提交</el-dropdown-item>
                      <el-dropdown-item command="approve" v-if="row.status === 'submitted'">审核通过</el-dropdown-item>
                      <el-dropdown-item command="reject" v-if="row.status === 'submitted'">审核拒绝</el-dropdown-item>
                      <el-dropdown-item command="transfer" v-if="row.status === 'approved'">确认调拨</el-dropdown-item>
                      <el-dropdown-item command="complete" v-if="row.status === 'transferred'">完成</el-dropdown-item>
                      <el-dropdown-item command="delete" v-if="row.status === 'draft'" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <el-row :gutter="20">
          <el-col :span="8" v-for="transferItem in inventoryTransfers" :key="transferItem.id">
            <el-card class="transfer-card" @click="viewTransferDetail(transferItem)">
              <div class="card-header">
                <h3>{{ transferItem.transfer_no }}</h3>
                <el-tag :type="getStatusTagType(transferItem.status)">
                  {{ getStatusLabel(transferItem.status) }}
                </el-tag>
              </div>
              <div class="card-content">
                <p><strong>调出仓库:</strong> {{ transferItem.from_warehouse_name }}</p>
                <p><strong>调入仓库:</strong> {{ transferItem.to_warehouse_name }}</p>
                <p><strong>调拨数量:</strong> <span class="quantity">{{ transferItem.total_quantity }}</span></p>
                <p><strong>调拨日期:</strong> {{ formatDate(transferItem.transfer_date) }}</p>
                <p><strong>调拨原因:</strong> {{ transferItem.reason }}</p>
              </div>
              <div class="card-actions" @click.stop>
                <el-button size="small" @click="viewTransferDetail(transferItem)">查看详情</el-button>
                <el-dropdown @command="(command: string) => handleTransferAction(command, transferItem)" v-if="transferItem.status !== 'completed'">
                  <el-button size="small" type="primary">
                    操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit" v-if="transferItem.status === 'draft'">编辑</el-dropdown-item>
                      <el-dropdown-item command="submit" v-if="transferItem.status === 'draft'">提交</el-dropdown-item>
                      <el-dropdown-item command="approve" v-if="transferItem.status === 'submitted'">审核通过</el-dropdown-item>
                      <el-dropdown-item command="reject" v-if="transferItem.status === 'submitted'">审核拒绝</el-dropdown-item>
                      <el-dropdown-item command="transfer" v-if="transferItem.status === 'approved'">确认调拨</el-dropdown-item>
                      <el-dropdown-item command="complete" v-if="transferItem.status === 'transferred'">完成</el-dropdown-item>
                      <el-dropdown-item command="delete" v-if="transferItem.status === 'draft'" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchInventoryTransfers"
          @current-change="fetchInventoryTransfers"
        />
      </div>
    </el-card>

    <!-- 创建/编辑调拨单对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingTransfer ? '编辑调拨单' : '创建调拨单'"
      width="900px"
      :close-on-click-modal="false"
      @close="onDialogClose"
    >
      <el-form :model="transferForm" :rules="transferRules" ref="transferFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="调拨单号" prop="transfer_no">
              <el-input v-model="transferForm.transfer_no" placeholder="系统自动生成" :disabled="editingTransfer" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="调拨日期" prop="transfer_date">
              <el-date-picker
                v-model="transferForm.transfer_date"
                type="datetime"
                placeholder="选择调拨日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="调出仓库" prop="from_warehouse_id">
              <el-select v-model="transferForm.from_warehouse_id" placeholder="选择调出仓库" style="width: 100%">
                <el-option
                  v-for="warehouse in warehouses"
                  :key="warehouse.id"
                  :label="warehouse.name"
                  :value="warehouse.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="调入仓库" prop="to_warehouse_id">
              <el-select v-model="transferForm.to_warehouse_id" placeholder="选择调入仓库" style="width: 100%">
                <el-option
                  v-for="warehouse in warehouses"
                  :key="warehouse.id"
                  :label="warehouse.name"
                  :value="warehouse.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="调拨原因" prop="reason">
          <el-input v-model="transferForm.reason" placeholder="请输入调拨原因" />
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="transferForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <!-- 调拨明细 -->
        <el-form-item label="调拨明细" required>
          <div class="transfer-items" style="width: 100%">
            <div class="items-header">
              <el-button type="primary" size="small" @click="addTransferItem">
                <el-icon><Plus /></el-icon>
                添加商品
              </el-button>
            </div>

            <el-table :data="transferForm.items" style="width: 100%; margin-top: 10px">
              <el-table-column label="商品名称" width="300">
                <template #default="{ row }">
                  <el-select
                    v-model="row.product_id"
                    placeholder="选择商品"
                    filterable
                    @change="onProductChange(row)"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="product in availableProducts"
                      :key="product.id"
                      :label="`${product.name} (${product.sku})`"
                      :value="product.id"
                    />
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column label="可用库存" width="120">
                <template #default="{ row }">
                  <span>{{ row.available_quantity || 0 }}</span>
                </template>
              </el-table-column>

              <el-table-column label="调拨数量" width="120">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.transfer_quantity"
                    :min="1"
                    :max="row.available_quantity || 999"
                    size="small"
                    style="width: 100%"
                  />
                </template>
              </el-table-column>

              <el-table-column label="操作" width="80">
                <template #default="{ $index }">
                  <el-button
                    type="danger"
                    size="small"
                    @click="removeTransferItem($index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="cancelTransferForm">取消</el-button>
        <el-button type="primary" @click="saveTransfer" :loading="saving">
          {{ editingTransfer ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 调拨单详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="调拨单详情"
      width="800px"
    >
      <div v-if="selectedTransfer" class="transfer-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="调拨单号">{{ selectedTransfer.transfer_no }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedTransfer.status)">
              {{ getStatusText(selectedTransfer.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="调出仓库">{{ selectedTransfer.from_warehouse_name }}</el-descriptions-item>
          <el-descriptions-item label="调入仓库">{{ selectedTransfer.to_warehouse_name }}</el-descriptions-item>
          <el-descriptions-item label="调拨日期">{{ formatDateTime(selectedTransfer.transfer_date) }}</el-descriptions-item>
          <el-descriptions-item label="总数量">{{ selectedTransfer.total_quantity }}</el-descriptions-item>
          <el-descriptions-item label="调拨原因" :span="2">{{ selectedTransfer.reason }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ selectedTransfer.remark || '无' }}</el-descriptions-item>
        </el-descriptions>

        <div class="transfer-items-detail" style="margin-top: 20px">
          <h4>调拨明细</h4>
          <el-table :data="selectedTransfer.items" style="width: 100%">
            <el-table-column label="商品名称" prop="product_name" />
            <el-table-column label="商品SKU" prop="product_sku" />
            <el-table-column label="调拨数量" prop="transfer_quantity" />
            <el-table-column label="可用库存" prop="available_quantity" />
          </el-table>
        </div>
      </div>

      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  inventoryTransferApi,
  type InventoryTransfer,
  type InventoryTransferStats,
  InventoryTransferStatus,
  type SimpleWarehouse
} from '@/api/inventory'
import { warehouseApi } from '@/api/warehouses'
import {
  Plus,
  Download,
  Tickets,
  Clock,
  Loading,
  CircleCheck,
  Search,
  List,
  Grid,
  ArrowDown,
  Refresh
} from '@element-plus/icons-vue'

// 响应式数据
const inventoryTransfers = ref<InventoryTransfer[]>([])
const warehouses = ref<SimpleWarehouse[]>([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const viewMode = ref<'table' | 'card'>('table')

const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const selectedTransfer = ref<InventoryTransfer | null>(null)
const editingTransfer = ref<InventoryTransfer | null>(null)
const saving = ref(false)

// 表单引用
const transferFormRef = ref()

// 可用商品列表
const availableProducts = ref<any[]>([])

// 调拨单表单
const transferForm = reactive({
  transfer_no: '',
  from_warehouse_id: null as number | null,
  to_warehouse_id: null as number | null,
  transfer_date: new Date(),
  reason: '',
  remark: '',
  items: [] as any[]
})

// 表单验证规则
const transferRules = {
  from_warehouse_id: [{ required: true, message: '请选择调出仓库', trigger: 'change' }],
  to_warehouse_id: [{ required: true, message: '请选择调入仓库', trigger: 'change' }],
  transfer_date: [{ required: true, message: '请选择调拨日期', trigger: 'change' }],
  reason: [{ required: true, message: '请输入调拨原因', trigger: 'blur' }]
}

// 搜索表单
const searchForm = reactive({
  transfer_no: '',
  from_warehouse_id: null as number | null,
  to_warehouse_id: null as number | null,
  status: '',
  date_range: null as [Date, Date] | null
})

// 统计数据
const transferStats = ref<InventoryTransferStats>({
  total_transfers: 0,
  draft_transfers: 0,
  submitted_transfers: 0,
  approved_transfers: 0,
  transferred_transfers: 0,
  completed_transfers: 0,
  total_quantity: 0
})

// 方法
const fetchInventoryTransfers = async () => {
  loading.value = true
  try {
    const params: any = {
      page: currentPage.value,
      page_size: pageSize.value
    }

    if (searchForm.transfer_no) {
      params.transfer_no = searchForm.transfer_no
    }
    if (searchForm.from_warehouse_id) {
      params.from_warehouse_id = searchForm.from_warehouse_id
    }
    if (searchForm.to_warehouse_id) {
      params.to_warehouse_id = searchForm.to_warehouse_id
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }
    if (searchForm.date_range) {
      params.start_date = searchForm.date_range[0].toISOString()
      params.end_date = searchForm.date_range[1].toISOString()
    }

    const response = await inventoryTransferApi.getInventoryTransfers(params)

    if (response) {
      inventoryTransfers.value = response || []
      total.value = response.length || 0
    } else {
      inventoryTransfers.value = []
      total.value = 0
    }

    // 获取统计数据
    const statsResponse = await inventoryTransferApi.getStats()
    if (statsResponse) {
      transferStats.value = statsResponse
    }
  } catch (error: any) {
    console.error('获取库存调拨单失败:', error)
    ElMessage.error('获取库存调拨单失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    loading.value = false
  }
}

const fetchWarehouses = async () => {
  try {
    // 获取活跃状态的仓库列表（专门用于调拨单）
    const response = await inventoryTransferApi.getActiveWarehouses()
    warehouses.value = response || []
  } catch (error: any) {
    console.error('获取活跃仓库列表失败:', error)
    // 如果专用API失败，回退到通用仓库API并过滤活跃仓库
    try {
      const fallbackResponse = await warehouseApi.getWarehouses({ is_active: true, status: 'active' })
      const filteredWarehouses = (fallbackResponse || [])
        .filter((w: any) => w.is_active && w.status === 'active')
        .map((w: any) => ({
          id: w.id!,
          name: w.name,
          address: w.address,
          status: w.status,
          is_active: w.is_active
        } as SimpleWarehouse))
      warehouses.value = filteredWarehouses
    } catch (fallbackError) {
      console.error('获取仓库列表失败:', fallbackError)
      warehouses.value = []
    }
  }
}

const fetchTransferStats = async () => {
  try {
    const stats = await inventoryTransferApi.getStats()
    transferStats.value = stats
  } catch (error: any) {
    console.error('获取统计数据失败:', error)
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchInventoryTransfers()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    transfer_no: '',
    from_warehouse_id: null,
    to_warehouse_id: null,
    status: '',
    date_range: null
  })
  handleSearch()
}

const viewTransferDetail = (transferItem: InventoryTransfer) => {
  selectedTransfer.value = transferItem
  showDetailDialog.value = true
}

const handleTransferAction = async (command: string, transferItem: InventoryTransfer) => {
  switch (command) {
    case 'edit':
      await editTransfer(transferItem)
      break
    case 'submit':
      await submitTransfer(transferItem)
      break
    case 'approve':
      await approveTransfer(transferItem)
      break
    case 'reject':
      await rejectTransfer(transferItem)
      break
    case 'transfer':
      await confirmTransfer(transferItem)
      break
    case 'complete':
      await completeTransfer(transferItem)
      break
    case 'delete':
      await deleteTransfer(transferItem)
      break
    default:
      ElMessage.warning('未知操作')
  }
}

const submitTransfer = async (transferItem: InventoryTransfer) => {
  try {
    await ElMessageBox.confirm('确定要提交这个调拨单吗？', '确认提交', {
      type: 'warning'
    })

    await inventoryTransferApi.submitInventoryTransfer(transferItem.id!)
    ElMessage.success('调拨单提交成功')
    fetchInventoryTransfers()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('提交调拨单失败:', error)
      ElMessage.error('提交失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const approveTransfer = async (transferItem: InventoryTransfer) => {
  try {
    await ElMessageBox.confirm('确定要审核通过这个调拨单吗？', '确认审核', {
      type: 'warning'
    })

    await inventoryTransferApi.approveInventoryTransfer(transferItem.id!, {
      status: InventoryTransferStatus.APPROVED
    })
    ElMessage.success('调拨单审核通过')
    fetchInventoryTransfers()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('审核调拨单失败:', error)
      ElMessage.error('审核失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const rejectTransfer = async (transferItem: InventoryTransfer) => {
  try {
    const { value: rejectReason } = await ElMessageBox.prompt('请输入拒绝原因', '审核拒绝', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '拒绝原因不能为空'
    })

    await inventoryTransferApi.approveInventoryTransfer(transferItem.id!, {
      status: InventoryTransferStatus.REJECTED,
      note: rejectReason
    })
    ElMessage.success('调拨单审核拒绝')
    fetchInventoryTransfers()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('拒绝调拨单失败:', error)
      ElMessage.error('拒绝失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const confirmTransfer = async (transferItem: InventoryTransfer) => {
  try {
    await ElMessageBox.confirm('确定要确认调拨吗？', '确认调拨', {
      type: 'warning'
    })

    await inventoryTransferApi.transferInventoryTransfer(transferItem.id!)
    ElMessage.success('调拨确认成功')
    fetchInventoryTransfers()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('确认调拨失败:', error)
      ElMessage.error('确认调拨失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const completeTransfer = async (transferItem: InventoryTransfer) => {
  try {
    await ElMessageBox.confirm('确定要完成这个调拨单吗？', '确认完成', {
      type: 'warning'
    })

    await inventoryTransferApi.completeInventoryTransfer(transferItem.id!)
    ElMessage.success('调拨单完成')
    fetchInventoryTransfers()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('完成调拨单失败:', error)
      ElMessage.error('完成失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const deleteTransfer = async (transferItem: InventoryTransfer) => {
  try {
    await ElMessageBox.confirm('确定要删除这个调拨单吗？删除后无法恢复！', '确认删除', {
      type: 'warning'
    })

    await inventoryTransferApi.deleteInventoryTransfer(transferItem.id!)
    ElMessage.success('调拨单删除成功')
    fetchInventoryTransfers()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除调拨单失败:', error)
      ElMessage.error('删除失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const exportTransfers = () => {
  ElMessage.success('调拨单导出成功')
}

// 调拨单表单相关方法
const resetTransferForm = () => {
  Object.assign(transferForm, {
    transfer_no: '',
    from_warehouse_id: null,
    to_warehouse_id: null,
    transfer_date: new Date(),
    reason: '',
    remark: '',
    items: []
  })
  editingTransfer.value = null
}

const addTransferItem = () => {
  transferForm.items.push({
    product_id: null,
    product_name: '',
    product_sku: '',
    transfer_quantity: 1,
    available_quantity: 0
  })
}

const removeTransferItem = (index: number) => {
  transferForm.items.splice(index, 1)
}

const onProductChange = async (row: any) => {
  if (!row.product_id) return

  try {
    // 获取商品信息和库存
    const product = availableProducts.value.find(p => p.id === row.product_id)
    if (product) {
      row.product_name = product.name
      row.product_sku = product.sku

      // 获取该商品在调出仓库的库存
      if (transferForm.from_warehouse_id) {
        // 这里应该调用API获取具体库存，暂时使用模拟数据
        row.available_quantity = 100 // 模拟库存数量
      }
    }
  } catch (error) {
    console.error('获取商品信息失败:', error)
  }
}

const editTransfer = async (transferItem: InventoryTransfer) => {
  editingTransfer.value = transferItem

  // 填充表单数据
  Object.assign(transferForm, {
    transfer_no: transferItem.transfer_no,
    from_warehouse_id: transferItem.from_warehouse_id,
    to_warehouse_id: transferItem.to_warehouse_id,
    transfer_date: new Date(transferItem.transfer_date),
    reason: transferItem.reason,
    remark: transferItem.remark,
    items: transferItem.items.map(item => ({
      product_id: item.product_id,
      product_name: item.product_name,
      product_sku: item.product_sku,
      transfer_quantity: item.transfer_quantity,
      available_quantity: item.available_quantity
    }))
  })

  showCreateDialog.value = true
}

const saveTransfer = async () => {
  if (!transferFormRef.value) return

  try {
    await transferFormRef.value.validate()

    if (transferForm.items.length === 0) {
      ElMessage.error('请至少添加一个调拨商品')
      return
    }

    // 验证调出仓库和调入仓库不能相同
    if (transferForm.from_warehouse_id === transferForm.to_warehouse_id) {
      ElMessage.error('调出仓库和调入仓库不能相同')
      return
    }

    saving.value = true

    // 计算总数量
    const total_quantity = transferForm.items.reduce((sum, item) => sum + item.transfer_quantity, 0)

    const transferData = {
      ...transferForm,
      total_quantity,
      transfer_date: transferForm.transfer_date.toISOString()
    }

    if (editingTransfer.value) {
      // 更新调拨单
      const updateData = {
        ...transferData,
        from_warehouse_id: transferData.from_warehouse_id || undefined,
        to_warehouse_id: transferData.to_warehouse_id || undefined
      }
      await inventoryTransferApi.updateInventoryTransfer(editingTransfer.value.id!, updateData)
      ElMessage.success('调拨单更新成功')
    } else {
      // 创建调拨单
      const createData = {
        ...transferData,
        from_warehouse_id: transferData.from_warehouse_id!,
        to_warehouse_id: transferData.to_warehouse_id!
      }
      await inventoryTransferApi.createInventoryTransfer(createData)
      ElMessage.success('调拨单创建成功')
    }

    showCreateDialog.value = false
    resetTransferForm()
    await fetchInventoryTransfers()
    await fetchTransferStats()

  } catch (error: any) {
    console.error('保存调拨单失败:', error)
    const errorMessage = error.response?.data?.detail || error.message || '保存失败'
    ElMessage.error(errorMessage)
  } finally {
    saving.value = false
  }
}

const openCreateDialog = () => {
  resetTransferForm() // 先重置表单
  showCreateDialog.value = true
}

const onDialogClose = () => {
  // 对话框关闭时重置表单
  resetTransferForm()
}

const cancelTransferForm = () => {
  showCreateDialog.value = false
  resetTransferForm()
}

const fetchAvailableProducts = async () => {
  try {
    // 这里应该调用商品API获取可用商品列表
    // 暂时使用模拟数据
    availableProducts.value = [
      { id: 1, name: 'iPhone 15 Pro Max', sku: 'IP15PM001' },
      { id: 2, name: 'Samsung Galaxy S24', sku: 'SGS24001' },
      { id: 3, name: 'MacBook Pro 16"', sku: 'MBP16001' }
    ]
  } catch (error) {
    console.error('获取商品列表失败:', error)
  }
}

// 辅助方法
const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    submitted: '已提交',
    approved: '已审核',
    rejected: '已拒绝',
    transferred: '已调拨',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    draft: '',
    submitted: 'warning',
    approved: 'success',
    rejected: 'danger',
    transferred: 'success',
    completed: 'success',
    cancelled: 'info'
  }
  return typeMap[status] || ''
}

const formatDate = (dateStr: string | null | undefined) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateStr: string | null | undefined) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    draft: '',
    submitted: 'warning',
    approved: 'success',
    rejected: 'danger',
    transferred: 'success',
    completed: 'success',
    cancelled: 'info'
  }
  return typeMap[status] || ''
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    submitted: '已提交',
    approved: '已审核',
    rejected: '已拒绝',
    transferred: '已调拨',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 生命周期
onMounted(() => {
  fetchInventoryTransfers()
  fetchWarehouses()
  fetchTransferStats()
  fetchAvailableProducts()
})
</script>

<style scoped>
.inventory-transfer-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
}

.page-description {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 调拨单概览 */
.transfer-overview {
  margin-bottom: 12px;
}

.overview-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.overview-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.overview-icon {
  padding: 12px;
  border-radius: 12px;
  flex-shrink: 0;
}

.overview-icon.total {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-icon.pending {
  background: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.overview-icon.processing {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-icon.completed {
  background: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.overview-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 14px;
  color: #7f8c8d;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 调拨单列表卡片 */
.transfer-list-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.transfer-list-card .el-card__header {
  padding: 14px 20px !important;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 数量样式 */
.quantity {
  font-weight: 600;
  color: #409EFF;
}

/* 卡片视图 */
.card-view {
  margin-top: 20px;
}

.transfer-card {
  margin-bottom: 20px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.transfer-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.transfer-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.transfer-card .card-header h3 {
  margin: 0;
  color: #303133;
}

.transfer-card .card-content {
  margin-bottom: 16px;
}

.transfer-card .card-content p {
  margin: 8px 0;
  color: #606266;
}

.transfer-card .card-actions {
  display: flex;
  gap: 8px;
}

/* 表格操作 */
.table-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.table-actions .el-button {
  margin: 0;
}

/* 分页 */
.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .inventory-transfer-view {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .transfer-overview .el-col {
    margin-bottom: 12px;
  }

  .overview-content {
    padding: 16px;
  }

  .search-card .el-form {
    flex-direction: column;
  }

  .search-card .el-form-item {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .table-actions {
    flex-direction: column;
    gap: 4px;
  }

  .card-view .el-col {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .header-left h2 {
    font-size: 24px;
  }

  .overview-value {
    font-size: 20px;
  }

  .overview-content {
    gap: 12px;
  }

  .overview-icon {
    padding: 8px;
  }
}
</style>
