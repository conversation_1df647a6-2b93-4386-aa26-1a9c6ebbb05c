"""
库存服务
"""

from typing import List, Tu<PERSON>, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from app.models.inventory import (
    Inventory, InventoryTransaction
)
from app.models.warehouse import Warehouse
from app.models.product import Product
from app.schemas.inventory import (
    InventoryCreate, InventoryUpdate, InventoryQuery, InventoryAdjustment,
    InventoryTransactionCreate, InventoryStats, TransactionType
)


class InventoryService:
    """库存服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_inventory_list(
        self, 
        skip: int = 0, 
        limit: int = 20, 
        query: Optional[InventoryQuery] = None
    ) -> Tuple[List[Inventory], int]:
        """获取库存列表"""
        
        # 构建查询
        db_query = self.db.query(Inventory).options(
            joinedload(Inventory.product),
            joinedload(Inventory.warehouse)
        ).filter(Inventory.is_active == True)

        # 检查是否需要join Product表
        need_product_join = False
        if query:
            if query.product_name or query.product_sku or query.product_category:
                need_product_join = True

        # 如果需要，添加Product表的join
        if need_product_join:
            db_query = db_query.join(Product)

        # 应用筛选条件
        if query:
            if query.product_name:
                db_query = db_query.filter(
                    Product.name.ilike(f"%{query.product_name}%")
                )
            if query.product_sku:
                db_query = db_query.filter(
                    Product.sku.ilike(f"%{query.product_sku}%")
                )
            if query.product_category:
                db_query = db_query.filter(
                    Product.category == query.product_category
                )
            if query.warehouse_id:
                db_query = db_query.filter(Inventory.warehouse_id == query.warehouse_id)
            if query.batch_no:
                db_query = db_query.filter(Inventory.batch_no.ilike(f"%{query.batch_no}%"))
            if query.production_date_start:
                db_query = db_query.filter(Inventory.production_date >= query.production_date_start)
            if query.production_date_end:
                db_query = db_query.filter(Inventory.production_date <= query.production_date_end)
            if query.expiry_date_start:
                db_query = db_query.filter(Inventory.expiry_date >= query.expiry_date_start)
            if query.expiry_date_end:
                db_query = db_query.filter(Inventory.expiry_date <= query.expiry_date_end)
            if query.is_expired is not None:
                from datetime import date
                today = date.today()
                if query.is_expired:
                    db_query = db_query.filter(
                        and_(Inventory.expiry_date.isnot(None), Inventory.expiry_date < today)
                    )
                else:
                    db_query = db_query.filter(
                        or_(Inventory.expiry_date.is_(None), Inventory.expiry_date >= today)
                    )
            if query.is_near_expiry is not None and query.is_near_expiry:
                from datetime import date, timedelta
                today = date.today()
                near_expiry_date = today + timedelta(days=7)  # 7天内过期
                db_query = db_query.filter(
                    and_(
                        Inventory.expiry_date.isnot(None),
                        Inventory.expiry_date >= today,
                        Inventory.expiry_date <= near_expiry_date
                    )
                )
            if query.status:
                db_query = db_query.filter(Inventory.status == query.status)
            if query.stock_status:
                if query.stock_status == "sufficient":
                    db_query = db_query.filter(Inventory.current_stock >= Inventory.min_stock)
                elif query.stock_status == "low":
                    db_query = db_query.filter(
                        and_(
                            Inventory.current_stock > 0,
                            Inventory.current_stock < Inventory.min_stock
                        )
                    )
                elif query.stock_status == "out":
                    db_query = db_query.filter(Inventory.current_stock == 0)
            if query.min_stock_level is not None:
                db_query = db_query.filter(Inventory.current_stock >= query.min_stock_level)
            if query.max_stock_level is not None:
                db_query = db_query.filter(Inventory.current_stock <= query.max_stock_level)
        
        # 获取总数
        total = db_query.count()
        
        # 分页和排序
        inventories = db_query.order_by(desc(Inventory.updated_at)).offset(skip).limit(limit).all()
        
        return inventories, total
    
    def get_inventory(self, inventory_id: int) -> Optional[Inventory]:
        """获取单个库存记录"""
        return self.db.query(Inventory).options(
            joinedload(Inventory.product),
            joinedload(Inventory.warehouse)
        ).filter(Inventory.id == inventory_id).first()
    
    def get_inventory_by_product_warehouse(self, product_id: int, warehouse_id: int) -> Optional[Inventory]:
        """根据商品和仓库获取库存记录"""
        return self.db.query(Inventory).filter(
            and_(
                Inventory.product_id == product_id,
                Inventory.warehouse_id == warehouse_id,
                Inventory.is_active == True
            )
        ).first()
    
    def create_inventory(self, inventory_data: InventoryCreate) -> Inventory:
        """创建库存记录"""
        
        # 检查是否已存在相同商品和仓库的库存记录
        existing = self.get_inventory_by_product_warehouse(
            inventory_data.product_id, 
            inventory_data.warehouse_id
        )
        if existing:
            raise ValueError("该商品在此仓库的库存记录已存在")
        
        # 验证商品和仓库是否存在
        product = self.db.query(Product).filter(Product.id == inventory_data.product_id).first()
        if not product:
            raise ValueError(f"商品ID {inventory_data.product_id} 不存在")
        
        warehouse = self.db.query(Warehouse).filter(Warehouse.id == inventory_data.warehouse_id).first()
        if not warehouse:
            raise ValueError(f"仓库ID {inventory_data.warehouse_id} 不存在")
        
        # 创建库存记录
        db_inventory = Inventory(**inventory_data.dict())
        db_inventory.available_stock = db_inventory.current_stock - db_inventory.reserved_stock
        
        self.db.add(db_inventory)
        self.db.commit()
        self.db.refresh(db_inventory)
        
        return db_inventory
    
    def update_inventory(self, inventory_id: int, inventory_data: InventoryUpdate) -> Optional[Inventory]:
        """更新库存记录"""
        
        db_inventory = self.db.query(Inventory).filter(Inventory.id == inventory_id).first()
        if not db_inventory:
            return None
        
        # 更新字段
        update_data = inventory_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_inventory, field, value)
        
        # 重新计算可用库存
        db_inventory.available_stock = db_inventory.current_stock - db_inventory.reserved_stock
        db_inventory.updated_at = datetime.now()
        
        self.db.commit()
        self.db.refresh(db_inventory)
        
        return db_inventory
    
    def adjust_inventory(self, adjustment: InventoryAdjustment) -> Inventory:
        """调整库存"""
        
        db_inventory = self.db.query(Inventory).filter(Inventory.id == adjustment.inventory_id).first()
        if not db_inventory:
            raise ValueError("库存记录不存在")
        
        # 记录调整前的数量
        before_quantity = db_inventory.current_stock
        
        # 计算调整后的数量
        if adjustment.adjustment_type == TransactionType.IN:
            after_quantity = before_quantity + adjustment.quantity
        elif adjustment.adjustment_type == TransactionType.OUT:
            after_quantity = max(0, before_quantity - adjustment.quantity)
        elif adjustment.adjustment_type == TransactionType.ADJUST:
            after_quantity = adjustment.quantity
        else:
            raise ValueError("不支持的调整类型")
        
        # 更新库存
        db_inventory.current_stock = after_quantity
        db_inventory.available_stock = after_quantity - db_inventory.reserved_stock
        db_inventory.updated_at = datetime.now()
        
        # 更新最后入库/出库时间
        if adjustment.adjustment_type == TransactionType.IN:
            db_inventory.last_in_date = datetime.now()
        elif adjustment.adjustment_type == TransactionType.OUT:
            db_inventory.last_out_date = datetime.now()
        
        # 创建库存变动记录
        transaction = InventoryTransaction(
            inventory_id=adjustment.inventory_id,
            transaction_type=adjustment.adjustment_type,
            quantity=adjustment.quantity,
            before_quantity=before_quantity,
            after_quantity=after_quantity,
            operator=adjustment.operator,
            reason=adjustment.reason,
            remark=adjustment.remark
        )
        
        self.db.add(transaction)
        self.db.commit()
        self.db.refresh(db_inventory)
        
        return db_inventory
    
    def get_inventory_transactions(
        self, 
        inventory_id: Optional[int] = None,
        skip: int = 0, 
        limit: int = 20
    ) -> Tuple[List[InventoryTransaction], int]:
        """获取库存变动记录"""
        
        db_query = self.db.query(InventoryTransaction).options(
            joinedload(InventoryTransaction.inventory).joinedload(Inventory.product),
            joinedload(InventoryTransaction.inventory).joinedload(Inventory.warehouse)
        )
        
        if inventory_id:
            db_query = db_query.filter(InventoryTransaction.inventory_id == inventory_id)
        
        # 获取总数
        total = db_query.count()
        
        # 分页和排序
        transactions = db_query.order_by(desc(InventoryTransaction.created_at)).offset(skip).limit(limit).all()
        
        return transactions, total
    
    def get_inventory_stats(self) -> InventoryStats:
        """获取库存统计"""
        
        # 基础统计
        total_products = self.db.query(Inventory).filter(Inventory.is_active == True).count()
        total_warehouses = self.db.query(Warehouse).filter(Warehouse.is_active == True).count()
        
        # 库存状态统计
        normal_stock = self.db.query(Inventory).filter(
            and_(
                Inventory.is_active == True,
                Inventory.current_stock >= Inventory.min_stock
            )
        ).count()
        
        low_stock = self.db.query(Inventory).filter(
            and_(
                Inventory.is_active == True,
                Inventory.current_stock > 0,
                Inventory.current_stock < Inventory.min_stock
            )
        ).count()
        
        out_of_stock = self.db.query(Inventory).filter(
            and_(
                Inventory.is_active == True,
                Inventory.current_stock == 0
            )
        ).count()
        
        # 库存价值统计
        total_stock_value_result = self.db.query(
            func.sum(Inventory.current_stock * Inventory.average_cost)
        ).filter(Inventory.is_active == True).scalar()
        total_stock_value = total_stock_value_result or 0
        
        # 平均库存水平
        avg_stock_result = self.db.query(
            func.avg(Inventory.current_stock)
        ).filter(Inventory.is_active == True).scalar()
        average_stock_level = float(avg_stock_result or 0)
        
        return InventoryStats(
            total_products=total_products,
            total_warehouses=total_warehouses,
            normal_stock=normal_stock,
            low_stock=low_stock,
            out_of_stock=out_of_stock,
            total_stock_value=total_stock_value,
            average_stock_level=average_stock_level
        )
    
    def get_warehouses(self) -> List[Warehouse]:
        """获取所有仓库"""
        return self.db.query(Warehouse).filter(Warehouse.is_active == True).all()












