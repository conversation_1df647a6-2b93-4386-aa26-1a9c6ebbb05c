<template>
  <div class="supplier-archive-view">
    <div class="page-header">
      <div class="header-left">
        <h2>供应商档案</h2>
        <p class="page-description">管理供应商基础信息，建立完整的供应商数据库</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          新增供应商
        </el-button>
        <el-button @click="exportSuppliers">
          <el-icon><Download /></el-icon>
          导出档案
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true">
        <el-form-item label="供应商名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入供应商名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="供应商类型">
          <el-select v-model="searchForm.type" placeholder="选择类型" clearable style="width: 150px">
            <el-option label="制造商" value="manufacturer" />
            <el-option label="批发商" value="wholesaler" />
            <el-option label="代理商" value="agent" />
            <el-option label="贸易商" value="trader" />
            <el-option label="服务商" value="service" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="所在地区">
          <el-input
            v-model="searchForm.region"
            placeholder="请输入地区"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        
        <el-form-item label="合作状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 120px">
            <el-option label="合作中" value="active" />
            <el-option label="暂停合作" value="suspended" />
            <el-option label="已终止" value="terminated" />
            <el-option label="待审核" value="pending" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="searchSuppliers">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 供应商档案列表 -->
    <el-card class="archive-list-card">
      <template #header>
        <div class="card-header">
          <span>供应商档案列表 (共 {{ total }} 条)</span>
          <div class="header-actions">
            <el-button-group>
              <el-button :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
                <el-icon><List /></el-icon>
                列表视图
              </el-button>
              <el-button :type="viewMode === 'card' ? 'primary' : ''" @click="viewMode = 'card'">
                <el-icon><Grid /></el-icon>
                卡片视图
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'">
        <el-table :data="supplierArchives" style="width: 100%" v-loading="loading">
          <el-table-column type="selection" width="55" />
          
          <el-table-column label="供应商信息" min-width="250">
            <template #default="{ row }">
              <div class="supplier-info">
                <div class="supplier-avatar">
                  <el-icon size="32" color="#409EFF"><OfficeBuilding /></el-icon>
                </div>
                <div class="supplier-details">
                  <div class="supplier-name">{{ row.name }}</div>
                  <div class="supplier-code">编码: {{ row.code }}</div>
                  <div class="supplier-contact">{{ row.contact_person }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="category" label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getTypeTagType(row.category)">{{ getTypeLabel(row.category) }}</el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="region" label="地区" width="120" />
          
          <el-table-column prop="phone" label="联系电话" width="130" />
          
          <el-table-column prop="cooperation_years" label="合作年限" width="100">
            <template #default="{ row }">
              <span>{{ row.cooperation_years }}年</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="credit_rating" label="信用等级" width="100">
            <template #default="{ row }">
              <el-rate
                :model-value="getCreditRatingDisplay(row.credit_rating)"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value}"
              />
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="created_at" label="创建时间" width="120">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="viewSupplier(row)">查看</el-button>
              <el-button size="small" type="primary" @click="editSupplier(row)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteSupplier(row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <div class="suppliers-grid">
          <div 
            v-for="supplier in supplierArchives" 
            :key="supplier.id"
            class="supplier-card"
          >
            <div class="supplier-card-header">
              <div class="card-avatar">
                <el-icon size="40" color="#409EFF"><OfficeBuilding /></el-icon>
              </div>
              <div class="card-status">
                <el-tag :type="getStatusTagType(supplier.status)" size="small">
                  {{ getStatusLabel(supplier.status) }}
                </el-tag>
              </div>
            </div>
            
            <div class="card-content">
              <h4 class="card-title">{{ supplier.name }}</h4>
              <p class="card-code">编码: {{ supplier.code }}</p>
              <div class="card-type">
                <el-tag :type="getTypeTagType(supplier.category)" size="small">
                  {{ getTypeLabel(supplier.category) }}
                </el-tag>
              </div>
              
              <div class="card-info">
                <div class="info-row">
                  <span class="label">联系人:</span>
                  <span class="value">{{ supplier.contact_person }}</span>
                </div>
                <div class="info-row">
                  <span class="label">电话:</span>
                  <span class="value">{{ supplier.phone }}</span>
                </div>
                <div class="info-row">
                  <span class="label">地区:</span>
                  <span class="value">{{ supplier.region }}</span>
                </div>
                <div class="info-row">
                  <span class="label">合作年限:</span>
                  <span class="value">{{ supplier.cooperation_years }}年</span>
                </div>
                <div class="info-row">
                  <span class="label">信用等级:</span>
                  <el-rate
                    :model-value="getCreditRatingDisplay(supplier.credit_rating)"
                    disabled
                    size="small"
                  />
                </div>
              </div>
            </div>
            
            <div class="card-actions">
              <el-button size="small" @click="viewSupplier(supplier)">查看</el-button>
              <el-button size="small" type="primary" @click="editSupplier(supplier)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteSupplier(supplier.id)">删除</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑供应商对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingSupplier ? '编辑供应商档案' : '新增供应商档案'"
      width="800px"
      @close="resetForm"
    >
      <el-form :model="supplierForm" :rules="formRules" ref="formRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="供应商名称" prop="name">
              <el-input v-model="supplierForm.name" placeholder="请输入供应商名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商编码" prop="code">
              <el-input v-model="supplierForm.code" placeholder="请输入供应商编码" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="供应商类型" prop="type">
              <el-select v-model="supplierForm.type" placeholder="选择类型" style="width: 100%">
                <el-option label="制造商" value="manufacturer" />
                <el-option label="批发商" value="wholesaler" />
                <el-option label="代理商" value="agent" />
                <el-option label="贸易商" value="trader" />
                <el-option label="服务商" value="service" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所在地区" prop="region">
              <el-input v-model="supplierForm.region" placeholder="请输入所在地区" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人" prop="contact_person">
              <el-input v-model="supplierForm.contact_person" placeholder="请输入联系人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="supplierForm.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮箱地址" prop="email">
              <el-input v-model="supplierForm.email" placeholder="请输入邮箱地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合作年限" prop="cooperation_years">
              <el-input-number
                v-model="supplierForm.cooperation_years"
                :min="0"
                :max="50"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="信用等级" prop="credit_rating">
              <el-rate v-model="supplierForm.credit_rating" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合作状态" prop="status">
              <el-select v-model="supplierForm.status" placeholder="选择状态" style="width: 100%">
                <el-option label="合作中" value="active" />
                <el-option label="暂停合作" value="suspended" />
                <el-option label="已终止" value="terminated" />
                <el-option label="待审核" value="pending" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="详细地址">
          <el-input
            v-model="supplierForm.address"
            placeholder="请输入详细地址"
          />
        </el-form-item>
        
        <el-form-item label="主营产品">
          <el-input
            v-model="supplierForm.main_products"
            type="textarea"
            :rows="3"
            placeholder="请输入主营产品信息"
          />
        </el-form-item>
        
        <el-form-item label="备注信息">
          <el-input
            v-model="supplierForm.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveSupplier" :loading="saving">
          {{ editingSupplier ? '更新' : '保存' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 供应商详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="供应商档案详情"
      width="700px"
    >
      <div v-if="selectedSupplier" class="supplier-detail">
        <div class="detail-header">
          <div class="detail-avatar">
            <el-icon size="60" color="#409EFF"><OfficeBuilding /></el-icon>
          </div>
          <div class="detail-info">
            <h3>{{ selectedSupplier.name }}</h3>
            <p><strong>编码:</strong> {{ selectedSupplier.code }}</p>
            <p><strong>类型:</strong> {{ getTypeLabel(selectedSupplier.category) }}</p>
            <p><strong>状态:</strong> 
              <el-tag :type="getStatusTagType(selectedSupplier.status)">
                {{ getStatusLabel(selectedSupplier.status) }}
              </el-tag>
            </p>
          </div>
        </div>
        
        <div class="detail-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="供应商类型">
              <el-tag :type="getTypeTagType(selectedSupplier.category)">{{ getTypeLabel(selectedSupplier.category) }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="联系人">{{ selectedSupplier.contact_person }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ selectedSupplier.phone }}</el-descriptions-item>
            <el-descriptions-item label="邮箱地址">{{ selectedSupplier.email }}</el-descriptions-item>
            <el-descriptions-item label="所在地区">{{ selectedSupplier.region }}</el-descriptions-item>
            <el-descriptions-item label="合作年限">{{ selectedSupplier.cooperation_years }}年</el-descriptions-item>
            <el-descriptions-item label="信用等级">
              <el-rate :model-value="getCreditRatingDisplay(selectedSupplier.credit_rating)" disabled />
            </el-descriptions-item>
            <el-descriptions-item label="详细地址" :span="2">{{ selectedSupplier.address }}</el-descriptions-item>
            <el-descriptions-item label="创建时间" :span="2">{{ formatDateTime(selectedSupplier.created_at) }}</el-descriptions-item>
            <el-descriptions-item label="更新时间" :span="2">{{ formatDateTime(selectedSupplier.updated_at) }}</el-descriptions-item>
          </el-descriptions>
          
          <div class="detail-section" v-if="selectedSupplier.main_products">
            <h4>主营产品</h4>
            <p>{{ selectedSupplier.main_products }}</p>
          </div>
          
          <div class="detail-section" v-if="selectedSupplier.remarks">
            <h4>备注信息</h4>
            <p>{{ selectedSupplier.remarks }}</p>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Download,
  Search,
  List,
  Grid,
  OfficeBuilding
} from '@element-plus/icons-vue'
import { supplierApi, type Supplier, type SupplierQuery } from '@/api/suppliers'

// 类型定义
type SupplierArchive = Supplier & {
  type?: 'manufacturer' | 'wholesaler' | 'agent' | 'trader' | 'service'
  region?: string
  cooperation_years?: number
  main_products?: string
  remarks?: string
}

interface SearchForm {
  name: string
  type: string
  region: string
  status: string
}

interface SupplierForm {
  name: string
  code: string
  type?: string
  region?: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  cooperation_years?: number
  credit_rating?: number
  status?: string
  main_products?: string
  remarks?: string
}

// 响应式数据
const supplierArchives = ref<SupplierArchive[]>([])
const loading = ref(false)
const saving = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const viewMode = ref<'table' | 'card'>('table')

const showAddDialog = ref(false)
const showDetailDialog = ref(false)
const editingSupplier = ref<SupplierArchive | null>(null)
const selectedSupplier = ref<SupplierArchive | null>(null)

const searchForm = reactive<SearchForm>({
  name: '',
  type: '',
  region: '',
  status: ''
})

const supplierForm = reactive<SupplierForm>({
  name: '',
  code: '',
  type: '',
  region: '',
  contact_person: '',
  phone: '',
  email: '',
  address: '',
  cooperation_years: 0,
  credit_rating: 5,
  status: 'active',
  main_products: '',
  remarks: ''
})

const formRef = ref()

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入供应商编码', trigger: 'blur' }],
  type: [{ required: true, message: '请选择供应商类型', trigger: 'change' }],
  region: [{ required: true, message: '请输入所在地区', trigger: 'blur' }],
  contact_person: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  cooperation_years: [{ required: true, message: '请输入合作年限', trigger: 'blur' }],
  status: [{ required: true, message: '请选择合作状态', trigger: 'change' }]
}

// 方法
const fetchSupplierArchives = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params: SupplierQuery = {
      name: searchForm.name || undefined,
      category: searchForm.type || undefined, // type映射到category
      status: searchForm.status || undefined,
      address: searchForm.region || undefined, // region映射到address
      page: currentPage.value,
      page_size: pageSize.value
    }

    // 移除空值参数
    Object.keys(params).forEach(key => {
      if (params[key as keyof SupplierQuery] === undefined) {
        delete params[key as keyof SupplierQuery]
      }
    })

    // 调用API获取供应商列表
    const response = await supplierApi.getSuppliers(params) as any

    if (response) {
      supplierArchives.value = response.items || []
      total.value = response.total || 0
    } else {
      supplierArchives.value = []
      total.value = 0
    }

  } catch (error) {
    ElMessage.error('获取供应商档案失败')
  } finally {
    loading.value = false
  }
}

const searchSuppliers = () => {
  // 实现搜索逻辑
  fetchSupplierArchives()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    type: '',
    region: '',
    status: ''
  })
  fetchSupplierArchives()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchSupplierArchives()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchSupplierArchives()
}

const viewSupplier = (supplier: SupplierArchive) => {
  selectedSupplier.value = supplier
  showDetailDialog.value = true
}

const editSupplier = (supplier: SupplierArchive) => {
  editingSupplier.value = supplier
  Object.assign(supplierForm, {
    name: supplier.name || '',
    code: supplier.code || '',
    type: supplier.category || supplier.type || '', // category映射到type
    region: supplier.region || '',
    contact_person: supplier.contact_person || '',
    phone: supplier.phone || '',
    email: supplier.email || '',
    address: supplier.address || '',
    cooperation_years: supplier.cooperation_years || 0,
    credit_rating: typeof supplier.credit_rating === 'string' ? getCreditRatingNumber(supplier.credit_rating) : (supplier.credit_rating || 3),
    status: supplier.status || 'active',
    main_products: supplier.main_products || '',
    remarks: supplier.remarks || supplier.remark || ''
  })
  showAddDialog.value = true
}

const deleteSupplier = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个供应商档案吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 调用API删除供应商
    await supplierApi.deleteSupplier(id)
    ElMessage.success('删除成功')

    // 刷新供应商列表
    await fetchSupplierArchives()

  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const saveSupplier = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    if (editingSupplier.value) {
      // 更新现有供应商
      const updateData = {
        name: supplierForm.name || '',
        code: supplierForm.code || '',
        contact_person: supplierForm.contact_person || undefined,
        phone: supplierForm.phone || undefined,
        email: supplierForm.email || undefined,
        address: supplierForm.address || undefined,
        category: supplierForm.type || undefined, // type映射到category
        region: supplierForm.region || undefined, // 所在地区
        credit_rating: getCreditRatingString(supplierForm.credit_rating || 3), // 转换为字符串
        cooperation_years: supplierForm.cooperation_years || 0, // 合作年限
        main_products: supplierForm.main_products || undefined, // 主营产品
        status: supplierForm.status as 'active' | 'inactive' | 'suspended' | 'terminated' | 'pending',
        remark: supplierForm.remarks || undefined
      }



      await supplierApi.updateSupplier(editingSupplier.value.id, updateData)
      ElMessage.success('更新成功')
    } else {
      // 新增供应商
      const createData = {
        name: supplierForm.name || '',
        code: supplierForm.code || '',
        contact_person: supplierForm.contact_person || undefined,
        phone: supplierForm.phone || undefined,
        email: supplierForm.email || undefined,
        address: supplierForm.address || undefined,
        category: supplierForm.type || undefined, // type映射到category
        region: supplierForm.region || undefined, // 所在地区
        credit_rating: getCreditRatingString(supplierForm.credit_rating || 3), // 转换为字符串
        cooperation_years: supplierForm.cooperation_years || 0, // 合作年限
        main_products: supplierForm.main_products || undefined, // 主营产品
        status: supplierForm.status as 'active' | 'inactive' | 'suspended' | 'terminated' | 'pending',
        remark: supplierForm.remarks || undefined
      }

      await supplierApi.createSupplier(createData as any)
      ElMessage.success('新增成功')
    }

    showAddDialog.value = false
    resetForm()

    // 刷新供应商列表
    await fetchSupplierArchives()

  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  editingSupplier.value = null
  Object.assign(supplierForm, {
    name: '',
    code: '',
    type: '',
    region: '',
    contact_person: '',
    phone: '',
    email: '',
    address: '',
    cooperation_years: 0,
    credit_rating: 5,
    status: 'active',
    main_products: '',
    remarks: ''
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const exportSuppliers = () => {
  ElMessage.success('供应商档案导出成功')
}

// 辅助方法
const getTypeLabel = (type: string | undefined) => {
  if (!type) return '未设置'
  // 如果已经是中文，直接返回
  if (/[\u4e00-\u9fa5]/.test(type)) {
    return type
  }
  // 英文类型映射（向后兼容）
  const labelMap: Record<string, string> = {
    'manufacturer': '制造商',
    'wholesaler': '批发商',
    'agent': '代理商',
    'trader': '贸易商',
    'service': '服务商'
  }
  return labelMap[type] || type
}

const getTypeTagType = (type: string | undefined) => {
  if (!type) return 'info'

  // 中文类型映射
  const chineseTypeMap: Record<string, string> = {
    '电子产品': 'primary',
    '数码产品': 'primary',
    '服装': 'success',
    '美妆': 'warning',
    '家居用品': 'info',
    '食品': 'success',
    '制造商': 'success',
    '批发商': 'primary',
    '代理商': 'warning',
    '贸易商': 'info',
    '服务商': ''
  }

  // 英文类型映射（向后兼容）
  const englishTypeMap: Record<string, string> = {
    'manufacturer': 'success',
    'wholesaler': 'primary',
    'agent': 'warning',
    'trader': 'info',
    'service': ''
  }

  return chineseTypeMap[type] || englishTypeMap[type] || 'info'
}

const getStatusLabel = (status: string | undefined) => {
  if (!status) return '未设置'
  const labelMap: Record<string, string> = {
    'active': '合作中',
    'suspended': '暂停合作',
    'terminated': '已终止',
    'pending': '待审核'
  }
  return labelMap[status] || status
}

const getStatusTagType = (status: string | undefined) => {
  if (!status) return 'info'
  const typeMap: Record<string, string> = {
    'active': 'success',
    'suspended': 'warning',
    'terminated': 'danger',
    'pending': 'info'
  }
  return typeMap[status] || 'info'
}

const formatDate = (dateString: string | undefined) => {
  if (!dateString) return '未设置'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateString: string | undefined) => {
  if (!dateString) return '未设置'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 信用等级转换函数
const getCreditRatingString = (rating: number): string => {
  const ratingMap: { [key: number]: string } = {
    5: 'AAA',
    4: 'AA',
    3: 'A',
    2: 'BBB',
    1: 'BB'
  }
  return ratingMap[rating] || 'A'
}

const getCreditRatingNumber = (rating: string): number => {
  const ratingMap: { [key: string]: number } = {
    'AAA': 5,
    'AA': 4,
    'A': 3,
    'BBB': 2,
    'BB': 1
  }
  return ratingMap[rating] || 3
}

const getCreditRatingDisplay = (rating: string | number | undefined): number => {
  if (typeof rating === 'number') {
    return rating
  }
  if (typeof rating === 'string') {
    return getCreditRatingNumber(rating)
  }
  return 3 // 默认值
}

onMounted(() => {
  fetchSupplierArchives()
})
</script>

<style scoped>
.supplier-archive-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
  width: 100%;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
}

.page-description {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 按钮样式 */
.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 档案列表卡片 */
.archive-list-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 供应商信息 */
.supplier-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.supplier-avatar {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  background: rgba(64, 158, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.supplier-details {
  flex: 1;
}

.supplier-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.supplier-code {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.supplier-contact {
  font-size: 12px;
  color: #606266;
}

/* 卡片视图 */
.card-view {
  margin-top: 20px;
}

.suppliers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.supplier-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.supplier-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.supplier-card-header {
  position: relative;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-avatar {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-status {
  position: absolute;
  top: 15px;
  right: 15px;
}

.card-content {
  padding: 20px;
}

.card-title {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.card-code {
  margin: 0 0 12px 0;
  font-size: 12px;
  color: #909399;
}

.card-type {
  margin-bottom: 16px;
}

.card-info {
  margin-top: 16px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-row .label {
  color: #606266;
  font-weight: 500;
}

.info-row .value {
  color: #2c3e50;
}

.card-actions {
  padding: 12px 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 8px;
}

/* 分页 */
.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 供应商详情 */
.supplier-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.detail-avatar {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  background: rgba(64, 158, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.detail-info h3 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 20px;
}

.detail-info p {
  margin: 0 0 8px 0;
  color: #606266;
}

.detail-content {
  margin-top: 20px;
}

.detail-section {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 16px;
}

.detail-section p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 20px;
}

/* 按钮样式 */
.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .suppliers-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (max-width: 768px) {
  .supplier-archive-view {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    justify-content: flex-start;
  }

  .suppliers-grid {
    grid-template-columns: 1fr;
  }

  .supplier-info {
    flex-direction: column;
    text-align: center;
  }

  .detail-header {
    flex-direction: column;
    text-align: center;
  }
}
</style>
