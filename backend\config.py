"""
应用配置文件
包含数据库连接、应用设置等配置信息
"""

import os
from typing import Optional
from dotenv import load_dotenv
from pathlib import Path

# 加载环境变量 - 指定.env文件的完整路径
env_path = Path(__file__).parent / '.env'
load_dotenv(dotenv_path=env_path)


class DatabaseConfig:
    """数据库配置"""

    # MySQL数据库配置 - 请根据实际情况修改
    DB_TYPE: str = os.getenv("DB_TYPE", "mysql")
    DB_HOST: str = os.getenv("DB_HOST", "*********")
    DB_PORT: int = int(os.getenv("DB_PORT", "3306"))
    DB_NAME: str = os.getenv("DB_NAME", "ecommerce_decision")
    DB_USER: str = os.getenv("DB_USER", "root")
    DB_PASSWORD: str = os.getenv("DB_PASSWORD", "mysql_cQXnkc")
    DB_CHARSET: str = os.getenv("DB_CHARSET", "utf8mb4")
    
    # 连接池配置
    DB_POOL_SIZE: int = 10
    DB_MAX_OVERFLOW: int = 20
    DB_POOL_TIMEOUT: int = 30
    DB_POOL_RECYCLE: int = 3600
    
    # 是否显示SQL语句（开发环境使用）
    DB_ECHO: bool = False
    
    @classmethod
    def get_database_url(cls) -> str:
        """获取数据库连接URL"""
        return f"mysql+pymysql://{cls.DB_USER}:{cls.DB_PASSWORD}@{cls.DB_HOST}:{cls.DB_PORT}/{cls.DB_NAME}?charset={cls.DB_CHARSET}"

    @classmethod
    def get_async_database_url(cls) -> str:
        """获取异步数据库连接URL"""
        return f"mysql+aiomysql://{cls.DB_USER}:{cls.DB_PASSWORD}@{cls.DB_HOST}:{cls.DB_PORT}/{cls.DB_NAME}?charset={cls.DB_CHARSET}"


class AppConfig:
    """应用配置"""
    
    # 应用基本信息
    APP_NAME: str = "电子商务决策系统"
    APP_VERSION: str = "1.0.0"
    APP_DESCRIPTION: str = "基于数据分析的电子商务决策支持系统"
    
    # API配置
    API_PREFIX: str = "/api"
    API_VERSION: str = "v1"
    
    # 跨域配置
    CORS_ORIGINS: list = [
        "http://localhost:5173",
        "http://127.0.0.1:5173"
    ]
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 文件上传配置
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: set = {".jpg", ".jpeg", ".png", ".gif", ".pdf", ".xlsx", ".csv"}
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    LOG_MAX_SIZE: int = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT: int = 5
    
    # Redis配置（如果使用缓存）
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    
    # 分页配置
    DEFAULT_PAGE_SIZE: int = 20
    MAX_PAGE_SIZE: int = 100


class EnvironmentConfig:
    """环境配置"""
    
    # 环境类型：development, testing, production
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")
    
    # 调试模式
    DEBUG: bool = ENVIRONMENT == "development"
    
    # 测试模式
    TESTING: bool = ENVIRONMENT == "testing"
    
    @classmethod
    def is_development(cls) -> bool:
        return cls.ENVIRONMENT == "development"
    
    @classmethod
    def is_production(cls) -> bool:
        return cls.ENVIRONMENT == "production"
    
    @classmethod
    def is_testing(cls) -> bool:
        return cls.ENVIRONMENT == "testing"


# 根据环境加载不同配置
def get_config():
    """根据环境获取配置"""
    if EnvironmentConfig.is_production():
        return ProductionConfig()
    elif EnvironmentConfig.is_testing():
        return TestingConfig()
    else:
        return DevelopmentConfig()


class DevelopmentConfig(DatabaseConfig, AppConfig, EnvironmentConfig):
    """开发环境配置"""
    DB_ECHO = True
    LOG_LEVEL = "DEBUG"


class TestingConfig(DatabaseConfig, AppConfig, EnvironmentConfig):
    """测试环境配置"""
    DB_NAME = "ecommerce_decision_test"
    TESTING = True


class ProductionConfig(DatabaseConfig, AppConfig, EnvironmentConfig):
    """生产环境配置"""
    DB_ECHO = False
    DEBUG = False
    LOG_LEVEL = "WARNING"
    
    # 生产环境应该从环境变量读取敏感信息
    DB_PASSWORD = os.getenv("DB_PASSWORD", "your-production-password")
    SECRET_KEY = os.getenv("SECRET_KEY", "your-production-secret-key")


# 默认配置
config = get_config()
