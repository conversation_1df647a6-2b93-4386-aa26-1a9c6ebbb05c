/**
 * 客户管理API
 */

import { api } from './index'

// 客户类型枚举
export enum CustomerType {
  INDIVIDUAL = 'individual',
  ENTERPRISE = 'enterprise', 
  DISTRIBUTOR = 'distributor'
}

// 客户等级枚举
export enum CustomerLevel {
  VIP = 'vip',
  NORMAL = 'normal',
  POTENTIAL = 'potential'
}

// 客户状态枚举
export enum CustomerStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BLACKLIST = 'blacklist'
}

// 客户接口
export interface Customer {
  id: number
  name: string
  code: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  customer_type: CustomerType
  level: CustomerLevel
  credit_limit: number
  current_balance: number
  total_orders: number
  total_amount: number
  status: CustomerStatus
  is_active: boolean
  platform_accounts?: Record<string, any>
  default_delivery_address?: string
  delivery_preferences?: Record<string, any>
  created_at: string
  updated_at?: string
  last_order_date?: string
  remark?: string
}

// 客户创建接口
export interface CustomerCreate {
  name: string
  code: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  customer_type?: CustomerType
  level?: CustomerLevel
  credit_limit?: number
  status?: CustomerStatus
  default_delivery_address?: string
  platform_accounts?: Record<string, any>
  delivery_preferences?: Record<string, any>
  remark?: string
}

// 客户更新接口
export interface CustomerUpdate {
  name?: string
  code?: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  customer_type?: CustomerType
  level?: CustomerLevel
  credit_limit?: number
  status?: CustomerStatus
  default_delivery_address?: string
  platform_accounts?: Record<string, any>
  delivery_preferences?: Record<string, any>
  remark?: string
}

// 客户查询参数
export interface CustomerQuery {
  name?: string
  code?: string
  customer_type?: CustomerType
  level?: CustomerLevel
  status?: CustomerStatus
  phone?: string
  email?: string
  address?: string
}

// 客户列表响应
export interface CustomerListResponse {
  items: Customer[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 客户统计信息
export interface CustomerStats {
  total_customers: number
  active_customers: number
  vip_customers: number
  new_customers: number
  total_orders: number
  total_amount: number
}

// 客户API
export const customerApi = {
  // 获取客户列表
  getCustomers: (params?: {
    page?: number
    page_size?: number
    name?: string
    code?: string
    customer_type?: CustomerType
    level?: CustomerLevel
    status?: CustomerStatus
    phone?: string
    email?: string
    address?: string
  }) => {
    return api.get<CustomerListResponse>('/api/customers/', { params })
  },

  // 获取客户统计信息
  getCustomerStats: () => {
    return api.get<CustomerStats>('/api/customers/stats')
  },

  // 获取单个客户
  getCustomer: (id: number) => {
    return api.get<Customer>(`/api/customers/${id}`)
  },

  // 创建客户
  createCustomer: (data: CustomerCreate) => {
    return api.post<Customer>('/api/customers/', data)
  },

  // 更新客户
  updateCustomer: (id: number, data: CustomerUpdate) => {
    return api.put<Customer>(`/api/customers/${id}`, data)
  },

  // 删除客户
  deleteCustomer: (id: number) => {
    return api.delete(`/api/customers/${id}`)
  }
}
