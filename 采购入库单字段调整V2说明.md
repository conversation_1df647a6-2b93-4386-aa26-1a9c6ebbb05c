# 采购入库单字段调整V2说明

## 调整目标

1. 将采购入库单明细表中的 `received_quantity`（实收数量）字段重命名为 `quantity`（数量）
2. 在采购入库单主表中添加审批和提交相关字段：
   - `submitted_by`（提交人）
   - `submitted_at`（提交时间）
   - `approved_by`（审批人）
   - `approved_at`（审批时间）

## 主要更改

### 1. 数据库模型调整

**文件**: `backend/app/models/purchase.py`

#### 1.1 主表字段添加
```python
# 添加的字段
submitted_by = Column(String(100), comment="提交人")
submitted_at = Column(DateTime(timezone=True), comment="提交时间")
approved_by = Column(String(100), comment="审批人")
approved_at = Column(DateTime(timezone=True), comment="审批时间")
```

#### 1.2 明细表字段重命名
```python
# 修改前
received_quantity = Column(Integer, nullable=False, comment="实收数量")

# 修改后
quantity = Column(Integer, nullable=False, comment="数量")
```

### 2. Schema定义调整

**文件**: `backend/app/schemas/purchase.py`

#### 2.1 主表Schema更新
```python
class PurchaseReceiptBase(BaseModel):
    # ... 原有字段 ...
    submitted_by: Optional[str] = None
    submitted_at: Optional[datetime] = None
    approved_by: Optional[str] = None
    approved_at: Optional[datetime] = None
    # ... 其他字段 ...
```

#### 2.2 明细表Schema更新
```python
class PurchaseReceiptItemBase(BaseModel):
    product_id: int
    product_name: str
    product_sku: Optional[str] = None
    quantity: int = Field(..., ge=0, description="数量")  # 重命名
    batch_no: Optional[str] = Field(None, description="批次号")
```

### 3. 前端接口调整

**文件**: `frontend/src/api/purchase-receipt.ts`

#### 3.1 主表接口更新
```typescript
export interface PurchaseReceipt {
  // ... 原有字段 ...
  submitted_by?: string
  submitted_at?: string
  approved_by?: string
  approved_at?: string
  // ... 其他字段 ...
}
```

#### 3.2 明细表接口更新
```typescript
export interface PurchaseReceiptItem {
  // ... 原有字段 ...
  quantity: number  // 重命名自 received_quantity
  batch_no?: string
  // ... 其他字段 ...
}
```

### 4. 前端界面调整

**文件**: `frontend/src/views/PurchaseReceiptView.vue`

#### 4.1 表格列调整
- 将"实收数量"列标题改为"数量"
- 更新所有相关的数据绑定从 `received_quantity` 到 `quantity`
- 简化入库状态显示逻辑

#### 4.2 表单数据结构调整
```typescript
interface ReceiptForm {
  // ... 其他字段 ...
  items: {
    product_id: number
    product_name: string
    product_sku?: string
    quantity: number  // 重命名
    batch_no?: string
  }[]
}
```

### 5. 后端服务调整

**文件**: `backend/app/services/purchase_receipt_service.py`

#### 5.1 创建入库单明细
```python
db_item = PurchaseReceiptItem(
    receipt_id=db_receipt.id,
    product_id=item_data.product_id,
    product_name=product.name,
    product_sku=product.sku,
    quantity=item_data.quantity,  # 重命名
    batch_no=item_data.batch_no
)
```

#### 5.2 从采购订单创建入库单
```python
items=[
    {
        "product_id": item.product_id,
        "product_name": item.product_name,
        "product_sku": item.product_sku,
        "quantity": 0,  # 重命名，初始数量为0
        "batch_no": None
    }
    for item in order.items
]
```

## 数据库结构变更

### 主表 (purchase_receipts)

**新增字段**:
- `submitted_by` VARCHAR(100) - 提交人
- `submitted_at` DATETIME - 提交时间
- `approved_by` VARCHAR(100) - 审批人
- `approved_at` DATETIME - 审批时间

### 明细表 (purchase_receipt_items)

**字段重命名**:
- `received_quantity` → `quantity`

**最终表结构**:
```sql
purchase_receipt_items (
  id, receipt_id, product_id, product_name, product_sku, 
  quantity, created_at, batch_no
)
```

## 业务逻辑调整

### 1. 工作流程增强

新增的审批和提交字段支持以下工作流程：
1. **创建** - 用户创建入库单（created_by）
2. **提交** - 用户提交入库单审批（submitted_by, submitted_at）
3. **审批** - 管理员审批入库单（approved_by, approved_at）
4. **入库** - 执行实际入库操作

### 2. 字段语义调整

- **quantity**: 统一表示入库数量，简化了原来的"应收"和"实收"概念
- **batch_no**: 支持批次管理，便于库存追踪

### 3. 状态计算简化

```javascript
// 修改后的进度计算
const getReceiptProgress = (receipt: PurchaseReceipt) => {
  if (!receipt.items || receipt.items.length === 0) return 0
  
  const totalItems = receipt.items.length
  const receivedItems = receipt.items.filter(item => (item.quantity || 0) > 0).length
  
  return totalItems > 0 ? Math.round((receivedItems / totalItems) * 100) : 0
}
```

## 数据库迁移

**迁移脚本**: `backend/migrate_receipt_fields_v2.py`

**迁移内容**:
1. 在主表中添加4个审批和提交相关字段
2. 将明细表的 `received_quantity` 字段重命名为 `quantity`
3. 保留现有数据的完整性

**使用方法**:
```bash
cd backend
python migrate_receipt_fields_v2.py
```

**迁移结果验证**:
- ✅ 主表新增4个字段
- ✅ 明细表字段重命名成功
- ✅ 数据完整性保持

## 影响分析

### 1. 正面影响
- **简化概念**: 统一使用"数量"概念，避免"应收"和"实收"的混淆
- **增强流程**: 支持完整的提交-审批工作流程
- **提升追踪**: 记录关键操作的执行人和时间
- **批次管理**: 继续支持批次号功能

### 2. 兼容性
- **数据库**: 通过迁移脚本平滑升级
- **API**: 接口保持向后兼容
- **前端**: 界面优化，用户体验提升

### 3. 注意事项
- 现有的入库单数据通过迁移脚本处理
- 审批和提交字段初始为空，可根据业务需要填充
- 需要更新相关的用户培训材料

## 测试建议

1. **数据库迁移测试**
   - 验证字段重命名正确
   - 验证新字段添加成功
   - 测试数据完整性

2. **功能测试**
   - 测试创建入库单功能
   - 测试数量输入和显示
   - 测试批次号功能

3. **工作流测试**
   - 测试提交功能（如果实现）
   - 测试审批功能（如果实现）
   - 验证时间戳记录

## 部署步骤

1. 备份现有数据库
2. 部署后端代码更改
3. 执行数据库迁移脚本
4. 部署前端代码更改
5. 验证功能正常
6. 更新用户文档

## 文件清单

### 后端文件
- `backend/app/models/purchase.py` - 数据模型
- `backend/app/schemas/purchase.py` - API Schema
- `backend/app/services/purchase_receipt_service.py` - 业务逻辑
- `backend/migrate_receipt_fields_v2.py` - 迁移脚本

### 前端文件
- `frontend/src/api/purchase-receipt.ts` - API接口定义
- `frontend/src/views/PurchaseReceiptView.vue` - 主界面组件

### 文档文件
- `采购入库单字段调整V2说明.md` - 本文档

## 总结

本次调整成功实现了：
1. ✅ 字段重命名：`received_quantity` → `quantity`
2. ✅ 新增审批流程字段：提交人、提交时间、审批人、审批时间
3. ✅ 前后端代码同步更新
4. ✅ 数据库平滑迁移
5. ✅ 保持系统功能完整性

系统现在具备了更完善的工作流程支持和更简洁的数据结构，为后续的业务扩展奠定了良好基础。
