<template>
  <div class="product-recommendation-view">
    <div class="page-header">
      <h2>智能商品推荐</h2>
    </div>

    <!-- 推荐配置 -->
    <el-card class="recommendation-config">
      <template #header>
        <div class="card-header">
          <el-icon><Star /></el-icon>
          <span>推荐配置</span>
        </div>
      </template>
      
      <el-form :model="recommendationForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="商品分类">
              <el-select v-model="recommendationForm.category" placeholder="选择分类（可选）">
                <el-option label="全部分类" value="" />
                <el-option label="电子产品" value="电子产品" />
                <el-option label="服装鞋帽" value="服装鞋帽" />
                <el-option label="家用电器" value="家用电器" />
                <el-option label="美妆个护" value="美妆个护" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="价格范围">
              <el-slider
                v-model="priceRange"
                range
                :min="0"
                :max="10000"
                :step="100"
                show-stops
                show-input
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="推荐数量">
              <el-input-number 
                v-model="recommendationForm.limit" 
                :min="1" 
                :max="50" 
                :step="1"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item>
              <el-button type="primary" @click="getRecommendations" :loading="loading">
                获取推荐
              </el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 推荐结果 -->
    <el-card class="recommendation-results" v-if="recommendations.length">
      <template #header>
        <div class="card-header">
          <el-icon><Trophy /></el-icon>
          <span>推荐结果 ({{ recommendations.length }} 个商品)</span>
        </div>
      </template>
      
      <div class="products-grid">
        <div 
          v-for="(product, index) in recommendations" 
          :key="product.id"
          class="product-card"
        >
          <div class="product-rank">
            <el-tag :type="getRankTagType(index + 1)" size="small">
              #{{ index + 1 }}
            </el-tag>
          </div>
          
          <div class="product-info">
            <h4 class="product-name">{{ product.name }}</h4>
            <p class="product-category">{{ product.category }}</p>
            
            <div class="product-metrics">
              <div class="metric-item">
                <span class="metric-label">价格:</span>
                <span class="metric-value">¥{{ product.price }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">成本:</span>
                <span class="metric-value">¥{{ product.cost }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">利润率:</span>
                <span class="metric-value profit-margin">{{ product.profit_margin?.toFixed(2) }}%</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">库存:</span>
                <span class="metric-value">{{ product.stock }}</span>
              </div>
            </div>
            
            <div class="product-score">
              <el-rate
                v-model="product.recommendation_score"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value}"
              />
            </div>
            
            <div class="recommendation-reason">
              <el-tag type="info" size="small">
                {{ recommendationReasons[product.id] || '综合评分较高' }}
              </el-tag>
            </div>
          </div>
          
          <div class="product-actions">
            <el-button size="small" @click="viewProduct(product)">查看详情</el-button>
            <el-button size="small" type="primary" @click="addToSelection(product)">
              加入选品
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 推荐统计 -->
    <el-row :gutter="20" class="recommendation-stats" v-if="recommendations.length">
      <el-col :span="8">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ averageScore.toFixed(2) }}</div>
            <div class="stat-label">平均推荐评分</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ averageProfitMargin.toFixed(2) }}%</div>
            <div class="stat-label">平均利润率</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ highProfitCount }}</div>
            <div class="stat-label">高利润商品数</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 商品详情对话框 -->
    <el-dialog
      title="商品详情"
      v-model="showProductDialog"
      width="600px"
    >
      <div v-if="selectedProduct">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="商品名称">{{ selectedProduct.name }}</el-descriptions-item>
          <el-descriptions-item label="分类">{{ selectedProduct.category }}</el-descriptions-item>
          <el-descriptions-item label="价格">¥{{ selectedProduct.price }}</el-descriptions-item>
          <el-descriptions-item label="成本">¥{{ selectedProduct.cost }}</el-descriptions-item>
          <el-descriptions-item label="利润率">{{ selectedProduct.profit_margin?.toFixed(2) }}%</el-descriptions-item>
          <el-descriptions-item label="库存">{{ selectedProduct.stock }}</el-descriptions-item>
          <el-descriptions-item label="推荐评分">{{ selectedProduct.recommendation_score?.toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="市场趋势">{{ selectedProduct.market_trend?.toFixed(2) || 'N/A' }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="product-description" v-if="selectedProduct.description">
          <h4>商品描述:</h4>
          <p>{{ selectedProduct.description }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useProductStore } from '../stores/products'
import { ElMessage } from 'element-plus'

const productStore = useProductStore()

const recommendationForm = ref({
  category: '',
  limit: 10
})

const priceRange = ref([0, 5000])
const recommendations = ref([])
const recommendationReasons = ref({})
const loading = ref(false)
const showProductDialog = ref(false)
const selectedProduct = ref(null)

// 计算属性
const averageScore = computed(() => {
  if (!recommendations.value.length) return 0
  const total = recommendations.value.reduce((sum, product) => sum + (product.recommendation_score || 0), 0)
  return total / recommendations.value.length
})

const averageProfitMargin = computed(() => {
  if (!recommendations.value.length) return 0
  const total = recommendations.value.reduce((sum, product) => sum + (product.profit_margin || 0), 0)
  return total / recommendations.value.length
})

const highProfitCount = computed(() => {
  return recommendations.value.filter(product => (product.profit_margin || 0) > 30).length
})

const getRecommendations = async () => {
  loading.value = true
  try {
    const params = {
      category: recommendationForm.value.category || undefined,
      price_range: priceRange.value,
      limit: recommendationForm.value.limit
    }
    
    const result = await productStore.getRecommendations(params)
    
    if (result) {
      recommendations.value = result.products
      recommendationReasons.value = result.recommendation_reasons
      ElMessage.success(`获取到 ${result.products.length} 个推荐商品`)
    }
  } catch (error) {
    ElMessage.error('获取推荐失败')
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  recommendationForm.value = {
    category: '',
    limit: 10
  }
  priceRange.value = [0, 5000]
  recommendations.value = []
  recommendationReasons.value = {}
}

const getRankTagType = (rank: number) => {
  if (rank === 1) return 'danger'
  if (rank <= 3) return 'warning'
  if (rank <= 5) return 'success'
  return 'info'
}

const viewProduct = (product: any) => {
  selectedProduct.value = product
  showProductDialog.value = true
}

const addToSelection = (product: any) => {
  ElMessage.success(`已将 "${product.name}" 加入选品清单`)
  // 这里可以实现加入选品清单的逻辑
}

onMounted(() => {
  // 页面加载时可以获取默认推荐
  getRecommendations()
})
</script>

<style scoped>
.product-recommendation-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.recommendation-config {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.recommendation-results {
  margin-bottom: 20px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.product-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background: white;
  transition: box-shadow 0.3s;
}

.product-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.product-rank {
  text-align: right;
  margin-bottom: 12px;
}

.product-name {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 16px;
}

.product-category {
  margin: 0 0 12px 0;
  color: #7f8c8d;
  font-size: 14px;
}

.product-metrics {
  margin-bottom: 12px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 13px;
}

.metric-label {
  color: #909399;
}

.metric-value {
  color: #2c3e50;
  font-weight: 500;
}

.profit-margin {
  color: #67C23A;
  font-weight: bold;
}

.product-score {
  margin-bottom: 12px;
}

.recommendation-reason {
  margin-bottom: 16px;
}

.product-actions {
  display: flex;
  gap: 8px;
}

.product-actions .el-button {
  flex: 1;
}

.recommendation-stats {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 2em;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  color: #7f8c8d;
  font-size: 14px;
}

.product-description {
  margin-top: 20px;
}

.product-description h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
}

.product-description p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}
</style>
