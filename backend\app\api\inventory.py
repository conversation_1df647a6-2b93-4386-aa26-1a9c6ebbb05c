"""
库存管理API
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from datetime import datetime
from app.core.database import get_db
from app.services.inventory_service import InventoryService
from app.models.inventory import InventoryTransfer, InventoryTransferItem, InventoryTransferStatus
from app.models.warehouse import Warehouse
from app.schemas.inventory import (
    InventoryResponse, InventoryCreate, InventoryUpdate, InventoryQuery,
    InventoryAdjustment, InventoryTransactionResponse, InventoryStats,
    PaginatedInventoryResponse, PaginatedTransactionResponse,
    InventoryTransfer as InventoryTransferSchema,
    InventoryTransferCreate,
    InventoryTransferUpdate,
    InventoryTransferStatusUpdate,
    InventoryTransferStats,
    InventoryTransferQuery
)
from app.schemas.warehouse import WarehouseResponse
from app.core.utils import generate_order_number

router = APIRouter()


def build_inventory_response_data(inventory) -> dict:
    """构建库存响应数据"""
    return {
        "id": inventory.id,
        "product_id": inventory.product_id,
        "warehouse_id": inventory.warehouse_id,
        "current_stock": inventory.current_stock or 0,
        "reserved_stock": inventory.reserved_stock or 0,
        "available_stock": inventory.available_stock or 0,
        "min_stock": inventory.min_stock or 0,
        "max_stock": inventory.max_stock or 0,
        "safety_stock": inventory.safety_stock or 0,
        "average_cost": inventory.average_cost or 0.0,
        "last_cost": inventory.last_cost or 0.0,
        "location": inventory.location or "",
        "zone": inventory.zone or "",
        "batch_no": inventory.batch_no or None,
        "production_date": inventory.production_date.isoformat() if inventory.production_date else None,
        "expiry_date": inventory.expiry_date.isoformat() if inventory.expiry_date else None,
        "status": inventory.status or "normal",
        "is_active": inventory.is_active if inventory.is_active is not None else True,
        "product_name": inventory.product.name if inventory.product else "",
        "product_sku": inventory.product.sku if inventory.product else "",
        "product_category": inventory.product.category if inventory.product else None,
        "product_image": inventory.product.image if inventory.product else None,
        "warehouse_name": inventory.warehouse.name if inventory.warehouse else "",
        "created_at": inventory.created_at,
        "updated_at": inventory.updated_at,
        "last_in_date": inventory.last_in_date,
        "last_out_date": inventory.last_out_date
    }


@router.get("/", response_model=PaginatedInventoryResponse)
async def get_inventory_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    product_name: Optional[str] = Query(None, description="商品名称"),
    product_sku: Optional[str] = Query(None, description="商品SKU"),
    product_category: Optional[str] = Query(None, description="商品分类"),
    warehouse_id: Optional[int] = Query(None, description="仓库ID"),
    batch_no: Optional[str] = Query(None, description="批次号"),
    production_date_start: Optional[str] = Query(None, description="生产日期开始"),
    production_date_end: Optional[str] = Query(None, description="生产日期结束"),
    expiry_date_start: Optional[str] = Query(None, description="到期日期开始"),
    expiry_date_end: Optional[str] = Query(None, description="到期日期结束"),
    is_expired: Optional[bool] = Query(None, description="是否过期"),
    is_near_expiry: Optional[bool] = Query(None, description="是否临近过期"),
    status: Optional[str] = Query(None, description="库存状态"),
    stock_status: Optional[str] = Query(None, description="库存水平"),
    db: Session = Depends(get_db)
):
    """获取库存列表"""
    
    service = InventoryService(db)
    
    # 构建查询参数
    from datetime import datetime

    # 转换日期字符串为date对象
    prod_start_date = None
    prod_end_date = None
    exp_start_date = None
    exp_end_date = None

    if production_date_start:
        try:
            prod_start_date = datetime.strptime(production_date_start, "%Y-%m-%d").date()
        except ValueError:
            pass

    if production_date_end:
        try:
            prod_end_date = datetime.strptime(production_date_end, "%Y-%m-%d").date()
        except ValueError:
            pass

    if expiry_date_start:
        try:
            exp_start_date = datetime.strptime(expiry_date_start, "%Y-%m-%d").date()
        except ValueError:
            pass

    if expiry_date_end:
        try:
            exp_end_date = datetime.strptime(expiry_date_end, "%Y-%m-%d").date()
        except ValueError:
            pass

    query_params = InventoryQuery(
        product_name=product_name,
        product_sku=product_sku,
        product_category=product_category,
        warehouse_id=warehouse_id,
        batch_no=batch_no,
        production_date_start=prod_start_date,
        production_date_end=prod_end_date,
        expiry_date_start=exp_start_date,
        expiry_date_end=exp_end_date,
        is_expired=is_expired,
        is_near_expiry=is_near_expiry,
        status=status,
        stock_status=stock_status
    )
    
    # 获取数据
    skip = (page - 1) * page_size
    inventories, total = service.get_inventory_list(skip=skip, limit=page_size, query=query_params)
    
    # 转换响应数据
    items = []
    for inventory in inventories:
        try:
            item_data = {
                "id": inventory.id,
                "product_id": inventory.product_id,
                "warehouse_id": inventory.warehouse_id,
                "current_stock": inventory.current_stock,
                "reserved_stock": inventory.reserved_stock or 0,
                "available_stock": (inventory.current_stock or 0) - (inventory.reserved_stock or 0),
                "min_stock": inventory.min_stock or 0,
                "max_stock": inventory.max_stock or 0,
                "safety_stock": inventory.safety_stock or 0,
                "average_cost": inventory.average_cost or 0.0,
                "last_cost": inventory.last_cost or 0.0,
                "location": inventory.location or "",
                "zone": inventory.zone or "",
                "status": inventory.status or "normal",
                "is_active": inventory.is_active or False,
                "product_name": getattr(inventory.product, 'name', '') if inventory.product else "",
                "product_sku": getattr(inventory.product, 'sku', '') if inventory.product else "",
                "product_category": getattr(inventory.product, 'category', None) if inventory.product else None,
                "product_image": getattr(inventory.product, 'image', None) if inventory.product else None,
                "warehouse_name": getattr(inventory.warehouse, 'name', '') if inventory.warehouse else "",
                "created_at": inventory.created_at,
                "updated_at": inventory.updated_at,
                "last_in_date": inventory.last_in_date,
                "last_out_date": inventory.last_out_date
            }
            items.append(InventoryResponse(**item_data))
        except Exception as e:
            # 跳过有问题的记录
            continue
    
    # 计算分页信息
    pages = (total + page_size - 1) // page_size
    
    return PaginatedInventoryResponse(
        items=items,
        total=total,
        page=page,
        page_size=page_size,
        pages=pages
    )


@router.get("/stats", response_model=InventoryStats)
async def get_inventory_stats(db: Session = Depends(get_db)):
    """获取库存统计"""
    
    service = InventoryService(db)
    return service.get_inventory_stats()


@router.get("/warehouses", response_model=List[WarehouseResponse])
async def get_warehouses(db: Session = Depends(get_db)):
    """获取仓库列表"""
    
    service = InventoryService(db)
    warehouses = service.get_warehouses()
    
    items = []
    for warehouse in warehouses:
        try:
            # 计算仓库统计信息
            inventory_count = 0
            try:
                inventory_count = len([inv for inv in warehouse.inventory_items if getattr(inv, 'is_active', False)])
            except:
                inventory_count = 0

            available_capacity = (warehouse.total_capacity_m3 or 0) - (warehouse.used_capacity_m3 or 0)
            utilization_rate = ((warehouse.used_capacity_m3 or 0) / (warehouse.total_capacity_m3 or 1) * 100) if (warehouse.total_capacity_m3 or 0) > 0 else 0

            item_data = {
                "id": warehouse.id,
                "name": warehouse.name or "",
                "address": warehouse.address or "",
                "total_capacity_m3": warehouse.total_capacity_m3 or 0.0,
                "used_capacity_m3": warehouse.used_capacity_m3 or 0.0,
                "available_capacity_m3": available_capacity,
                "utilization_rate": utilization_rate,
                "inventory_count": inventory_count,
                "latitude": warehouse.latitude,
                "longitude": warehouse.longitude,
                "operating_hours": warehouse.operating_hours,
                "contact_info": warehouse.contact_info,
                "is_active": warehouse.is_active or False,
                "status": warehouse.status or "active",
                "created_at": warehouse.created_at,
                "updated_at": warehouse.updated_at
            }
            items.append(WarehouseResponse(**item_data))
        except Exception as e:
            # 跳过有问题的记录
            continue
    
    return items


@router.get("/{inventory_id}", response_model=InventoryResponse)
async def get_inventory(inventory_id: int, db: Session = Depends(get_db)):
    """获取单个库存记录"""
    
    service = InventoryService(db)
    inventory = service.get_inventory(inventory_id)
    
    if not inventory:
        raise HTTPException(status_code=404, detail="库存记录不存在")
    
    return InventoryResponse(**build_inventory_response_data(inventory))


@router.post("/", response_model=InventoryResponse)
async def create_inventory(inventory_data: InventoryCreate, db: Session = Depends(get_db)):
    """创建库存记录"""
    
    service = InventoryService(db)
    
    try:
        inventory = service.create_inventory(inventory_data)
        
        return InventoryResponse(**build_inventory_response_data(inventory))
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{inventory_id}", response_model=InventoryResponse)
async def update_inventory(
    inventory_id: int, 
    inventory_data: InventoryUpdate, 
    db: Session = Depends(get_db)
):
    """更新库存记录"""
    
    service = InventoryService(db)
    inventory = service.update_inventory(inventory_id, inventory_data)
    
    if not inventory:
        raise HTTPException(status_code=404, detail="库存记录不存在")
    
    return InventoryResponse(**build_inventory_response_data(inventory))


@router.post("/adjust", response_model=InventoryResponse)
async def adjust_inventory(adjustment: InventoryAdjustment, db: Session = Depends(get_db)):
    """调整库存"""
    
    service = InventoryService(db)
    
    try:
        inventory = service.adjust_inventory(adjustment)
        
        return InventoryResponse(**build_inventory_response_data(inventory))
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/transactions/", response_model=PaginatedTransactionResponse)
async def get_inventory_transactions(
    inventory_id: Optional[int] = Query(None, description="库存ID"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取库存变动记录"""
    
    service = InventoryService(db)
    
    skip = (page - 1) * page_size
    transactions, total = service.get_inventory_transactions(
        inventory_id=inventory_id,
        skip=skip,
        limit=page_size
    )
    
    # 转换响应数据
    items = []
    for transaction in transactions:
        item_data = {
            "id": transaction.id,
            "inventory_id": transaction.inventory_id,
            "transaction_type": transaction.transaction_type,
            "quantity": transaction.quantity,
            "before_quantity": transaction.before_quantity,
            "after_quantity": transaction.after_quantity,
            "reference_type": transaction.reference_type,
            "reference_id": transaction.reference_id,
            "reference_no": transaction.reference_no,
            "operator": transaction.operator,
            "reason": transaction.reason,
            "remark": transaction.remark,
            "product_name": transaction.inventory.product.name if transaction.inventory and transaction.inventory.product else "",
            "product_sku": transaction.inventory.product.sku if transaction.inventory and transaction.inventory.product else "",
            "warehouse_name": transaction.inventory.warehouse.name if transaction.inventory and transaction.inventory.warehouse else "",
            "created_at": transaction.created_at
        }
        items.append(InventoryTransactionResponse(**item_data))
    
    # 计算分页信息
    pages = (total + page_size - 1) // page_size
    
    return PaginatedTransactionResponse(
        items=items,
        total=total,
        page=page,
        page_size=page_size,
        pages=pages
    )


# ==================== 库存调拨单相关API ====================

@router.get("/transfers/active-warehouses")
def get_active_warehouses(db: Session = Depends(get_db)):
    """获取活跃状态的仓库列表（用于调拨单选择）"""
    warehouses = db.query(Warehouse).filter(
        Warehouse.is_active == True,
        Warehouse.status == "active"
    ).order_by(Warehouse.name).all()

    return [
        {
            "id": warehouse.id,
            "name": warehouse.name,
            "address": warehouse.address,
            "status": warehouse.status,
            "is_active": warehouse.is_active
        }
        for warehouse in warehouses
    ]


def get_inventory_transfer_stats(db: Session) -> InventoryTransferStats:
    """获取库存调拨单统计信息"""
    from sqlalchemy import func

    # 基础统计
    total_transfers = db.query(InventoryTransfer).count()
    draft_transfers = db.query(InventoryTransfer).filter(InventoryTransfer.status == InventoryTransferStatus.DRAFT).count()
    submitted_transfers = db.query(InventoryTransfer).filter(InventoryTransfer.status == InventoryTransferStatus.SUBMITTED).count()
    approved_transfers = db.query(InventoryTransfer).filter(InventoryTransfer.status == InventoryTransferStatus.APPROVED).count()
    transferred_transfers = db.query(InventoryTransfer).filter(InventoryTransfer.status == InventoryTransferStatus.TRANSFERRED).count()
    completed_transfers = db.query(InventoryTransfer).filter(InventoryTransfer.status == InventoryTransferStatus.COMPLETED).count()

    # 数量统计
    total_quantity = db.query(func.sum(InventoryTransfer.total_quantity)).scalar() or 0

    return InventoryTransferStats(
        total_transfers=total_transfers,
        draft_transfers=draft_transfers,
        submitted_transfers=submitted_transfers,
        approved_transfers=approved_transfers,
        transferred_transfers=transferred_transfers,
        completed_transfers=completed_transfers,
        total_quantity=total_quantity
    )


@router.get("/transfers/stats", response_model=InventoryTransferStats)
def get_transfer_stats(db: Session = Depends(get_db)):
    """获取库存调拨单统计信息"""
    return get_inventory_transfer_stats(db)


@router.get("/transfers/", response_model=List[InventoryTransferSchema])
def get_inventory_transfers(
    transfer_no: Optional[str] = Query(None, description="调拨单号"),
    from_warehouse_id: Optional[int] = Query(None, description="调出仓库ID"),
    to_warehouse_id: Optional[int] = Query(None, description="调入仓库ID"),
    status: Optional[InventoryTransferStatus] = Query(None, description="状态"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取库存调拨单列表"""
    from sqlalchemy.orm import joinedload

    query = db.query(InventoryTransfer).options(
        joinedload(InventoryTransfer.from_warehouse),
        joinedload(InventoryTransfer.to_warehouse),
        joinedload(InventoryTransfer.items)
    )

    # 应用筛选条件
    if transfer_no:
        query = query.filter(InventoryTransfer.transfer_no.contains(transfer_no))
    if from_warehouse_id:
        query = query.filter(InventoryTransfer.from_warehouse_id == from_warehouse_id)
    if to_warehouse_id:
        query = query.filter(InventoryTransfer.to_warehouse_id == to_warehouse_id)
    if status:
        query = query.filter(InventoryTransfer.status == status)
    if start_date:
        query = query.filter(InventoryTransfer.transfer_date >= start_date)
    if end_date:
        query = query.filter(InventoryTransfer.transfer_date <= end_date)

    # 分页
    offset = (page - 1) * page_size
    transfers = query.offset(offset).limit(page_size).all()

    # 添加关联信息
    result = []
    for transfer_obj in transfers:
        transfer_dict = transfer_obj.__dict__.copy()

        # 添加仓库名称
        if transfer_obj.from_warehouse:
            transfer_dict['from_warehouse_name'] = transfer_obj.from_warehouse.name
        if transfer_obj.to_warehouse:
            transfer_dict['to_warehouse_name'] = transfer_obj.to_warehouse.name

        # 添加调拨明细
        from app.schemas.inventory import InventoryTransferItem as InventoryTransferItemSchema
        transfer_dict['items'] = [
            InventoryTransferItemSchema.model_validate(item) for item in transfer_obj.items
        ]

        result.append(InventoryTransferSchema.model_validate(transfer_dict))

    return result


@router.get("/transfers/{transfer_id}", response_model=InventoryTransferSchema)
def get_inventory_transfer(transfer_id: int, db: Session = Depends(get_db)):
    """获取库存调拨单详情"""
    from sqlalchemy.orm import joinedload

    transfer_obj = db.query(InventoryTransfer).options(
        joinedload(InventoryTransfer.from_warehouse),
        joinedload(InventoryTransfer.to_warehouse),
        joinedload(InventoryTransfer.items)
    ).filter(InventoryTransfer.id == transfer_id).first()

    if not transfer_obj:
        raise HTTPException(status_code=404, detail="库存调拨单不存在")

    transfer_dict = transfer_obj.__dict__.copy()

    # 添加关联信息
    if transfer_obj.from_warehouse:
        transfer_dict['from_warehouse_name'] = transfer_obj.from_warehouse.name
    if transfer_obj.to_warehouse:
        transfer_dict['to_warehouse_name'] = transfer_obj.to_warehouse.name

    # 添加调拨明细
    from app.schemas.inventory import InventoryTransferItem as InventoryTransferItemSchema
    transfer_dict['items'] = [
        InventoryTransferItemSchema.model_validate(item) for item in transfer_obj.items
    ]

    return InventoryTransferSchema.model_validate(transfer_dict)


@router.post("/transfers/", response_model=InventoryTransferSchema)
def create_inventory_transfer(transfer_data: InventoryTransferCreate, db: Session = Depends(get_db)):
    """创建库存调拨单"""
    # 验证仓库是否存在且为活跃状态
    from_warehouse = db.query(Warehouse).filter(
        Warehouse.id == transfer_data.from_warehouse_id,
        Warehouse.is_active == True,
        Warehouse.status == "active"
    ).first()
    if not from_warehouse:
        raise HTTPException(status_code=404, detail="调出仓库不存在或未启用")

    to_warehouse = db.query(Warehouse).filter(
        Warehouse.id == transfer_data.to_warehouse_id,
        Warehouse.is_active == True,
        Warehouse.status == "active"
    ).first()
    if not to_warehouse:
        raise HTTPException(status_code=404, detail="调入仓库不存在或未启用")

    # 验证不能是同一个仓库
    if transfer_data.from_warehouse_id == transfer_data.to_warehouse_id:
        raise HTTPException(status_code=400, detail="调出仓库和调入仓库不能是同一个")

    # 生成调拨单号
    if not transfer_data.transfer_no:
        transfer_data.transfer_no = generate_order_number("IT")

    # 创建调拨单
    db_transfer = InventoryTransfer(
        transfer_no=transfer_data.transfer_no,
        from_warehouse_id=transfer_data.from_warehouse_id,
        to_warehouse_id=transfer_data.to_warehouse_id,
        transfer_date=transfer_data.transfer_date,
        reason=transfer_data.reason,
        total_quantity=transfer_data.total_quantity,
        remark=transfer_data.remark,
        status=InventoryTransferStatus.DRAFT
    )

    db.add(db_transfer)
    db.flush()  # 获取ID

    # 创建调拨明细
    for item_data in transfer_data.items:
        db_item = InventoryTransferItem(
            inventory_transfer_id=db_transfer.id,
            product_id=item_data.product_id,
            product_name=item_data.product_name,
            product_sku=item_data.product_sku,
            transfer_quantity=item_data.transfer_quantity,
            available_quantity=item_data.available_quantity
        )
        db.add(db_item)

    db.commit()
    db.refresh(db_transfer)

    # 重新查询以获取完整的关联数据
    from sqlalchemy.orm import joinedload
    transfer_obj = db.query(InventoryTransfer).options(
        joinedload(InventoryTransfer.from_warehouse),
        joinedload(InventoryTransfer.to_warehouse),
        joinedload(InventoryTransfer.items)
    ).filter(InventoryTransfer.id == db_transfer.id).first()

    # 构建响应数据
    from app.schemas.inventory import InventoryTransferItem as InventoryTransferItemSchema

    response_data = {
        "id": transfer_obj.id,
        "transfer_no": transfer_obj.transfer_no,
        "from_warehouse_id": transfer_obj.from_warehouse_id,
        "to_warehouse_id": transfer_obj.to_warehouse_id,
        "transfer_date": transfer_obj.transfer_date,
        "reason": transfer_obj.reason,
        "total_quantity": transfer_obj.total_quantity,
        "status": transfer_obj.status,
        "remark": transfer_obj.remark,
        "submitted_at": transfer_obj.submitted_at,
        "submitted_by": transfer_obj.submitted_by,
        "approved_at": transfer_obj.approved_at,
        "approved_by": transfer_obj.approved_by,
        "approval_note": transfer_obj.approval_note,
        "transferred_at": transfer_obj.transferred_at,
        "transferred_by": transfer_obj.transferred_by,
        "created_at": transfer_obj.created_at,
        "updated_at": transfer_obj.updated_at,
        "created_by": transfer_obj.created_by,
        "updated_by": transfer_obj.updated_by,
        "from_warehouse_name": transfer_obj.from_warehouse.name if transfer_obj.from_warehouse else None,
        "to_warehouse_name": transfer_obj.to_warehouse.name if transfer_obj.to_warehouse else None,
        "items": [
            InventoryTransferItemSchema.model_validate(item) for item in transfer_obj.items
        ]
    }

    return InventoryTransferSchema.model_validate(response_data)


@router.put("/transfers/{transfer_id}", response_model=InventoryTransferSchema)
def update_inventory_transfer(transfer_id: int, transfer_data: InventoryTransferUpdate, db: Session = Depends(get_db)):
    """更新库存调拨单"""
    db_transfer = db.query(InventoryTransfer).filter(InventoryTransfer.id == transfer_id).first()
    if not db_transfer:
        raise HTTPException(status_code=404, detail="库存调拨单不存在")

    # 只有草稿状态才能修改
    if db_transfer.status != InventoryTransferStatus.DRAFT:
        raise HTTPException(status_code=400, detail="只有草稿状态的调拨单才能修改")

    # 更新基本信息
    update_data = transfer_data.model_dump(exclude_unset=True)
    items_data = update_data.pop('items', None)

    # 验证仓库状态（如果更新了仓库信息）
    if 'from_warehouse_id' in update_data:
        from_warehouse = db.query(Warehouse).filter(
            Warehouse.id == update_data['from_warehouse_id'],
            Warehouse.is_active == True,
            Warehouse.status == "active"
        ).first()
        if not from_warehouse:
            raise HTTPException(status_code=404, detail="调出仓库不存在或未启用")

    if 'to_warehouse_id' in update_data:
        to_warehouse = db.query(Warehouse).filter(
            Warehouse.id == update_data['to_warehouse_id'],
            Warehouse.is_active == True,
            Warehouse.status == "active"
        ).first()
        if not to_warehouse:
            raise HTTPException(status_code=404, detail="调入仓库不存在或未启用")

    # 验证不能是同一个仓库（如果同时更新了两个仓库）
    final_from_warehouse_id = update_data.get('from_warehouse_id', db_transfer.from_warehouse_id)
    final_to_warehouse_id = update_data.get('to_warehouse_id', db_transfer.to_warehouse_id)
    if final_from_warehouse_id == final_to_warehouse_id:
        raise HTTPException(status_code=400, detail="调出仓库和调入仓库不能是同一个")

    for field, value in update_data.items():
        setattr(db_transfer, field, value)

    # 更新明细
    if items_data is not None:
        # 删除原有明细
        db.query(InventoryTransferItem).filter(InventoryTransferItem.inventory_transfer_id == transfer_id).delete()

        # 创建新明细
        for item_data in items_data:
            db_item = InventoryTransferItem(
                inventory_transfer_id=transfer_id,
                **item_data
            )
            db.add(db_item)

    db.commit()
    db.refresh(db_transfer)

    # 重新查询以获取完整的关联数据
    from sqlalchemy.orm import joinedload
    transfer_obj = db.query(InventoryTransfer).options(
        joinedload(InventoryTransfer.from_warehouse),
        joinedload(InventoryTransfer.to_warehouse),
        joinedload(InventoryTransfer.items)
    ).filter(InventoryTransfer.id == transfer_id).first()

    # 构建响应数据
    from app.schemas.inventory import InventoryTransferItem as InventoryTransferItemSchema

    response_data = {
        "id": transfer_obj.id,
        "transfer_no": transfer_obj.transfer_no,
        "from_warehouse_id": transfer_obj.from_warehouse_id,
        "to_warehouse_id": transfer_obj.to_warehouse_id,
        "transfer_date": transfer_obj.transfer_date,
        "reason": transfer_obj.reason,
        "total_quantity": transfer_obj.total_quantity,
        "status": transfer_obj.status,
        "remark": transfer_obj.remark,
        "submitted_at": transfer_obj.submitted_at,
        "submitted_by": transfer_obj.submitted_by,
        "approved_at": transfer_obj.approved_at,
        "approved_by": transfer_obj.approved_by,
        "approval_note": transfer_obj.approval_note,
        "transferred_at": transfer_obj.transferred_at,
        "transferred_by": transfer_obj.transferred_by,
        "created_at": transfer_obj.created_at,
        "updated_at": transfer_obj.updated_at,
        "created_by": transfer_obj.created_by,
        "updated_by": transfer_obj.updated_by,
        "from_warehouse_name": transfer_obj.from_warehouse.name if transfer_obj.from_warehouse else None,
        "to_warehouse_name": transfer_obj.to_warehouse.name if transfer_obj.to_warehouse else None,
        "items": [
            InventoryTransferItemSchema.model_validate(item) for item in transfer_obj.items
        ]
    }

    return InventoryTransferSchema.model_validate(response_data)


@router.delete("/transfers/{transfer_id}")
def delete_inventory_transfer(transfer_id: int, db: Session = Depends(get_db)):
    """删除库存调拨单"""
    db_transfer = db.query(InventoryTransfer).filter(InventoryTransfer.id == transfer_id).first()
    if not db_transfer:
        raise HTTPException(status_code=404, detail="库存调拨单不存在")

    # 只有草稿状态才能删除
    if db_transfer.status != InventoryTransferStatus.DRAFT:
        raise HTTPException(status_code=400, detail="只有草稿状态的调拨单才能删除")

    db.delete(db_transfer)
    db.commit()

    return {"message": "库存调拨单删除成功"}


@router.post("/transfers/{transfer_id}/submit")
def submit_inventory_transfer(transfer_id: int, db: Session = Depends(get_db)):
    """提交库存调拨单"""
    db_transfer = db.query(InventoryTransfer).filter(InventoryTransfer.id == transfer_id).first()
    if not db_transfer:
        raise HTTPException(status_code=404, detail="库存调拨单不存在")

    if db_transfer.status != InventoryTransferStatus.DRAFT:
        raise HTTPException(status_code=400, detail="只有草稿状态的调拨单才能提交")

    db_transfer.status = InventoryTransferStatus.SUBMITTED
    db_transfer.submitted_at = datetime.now()
    db_transfer.submitted_by = "system"  # 实际应用中应该是当前用户

    db.commit()

    return {"message": "库存调拨单提交成功"}


@router.post("/transfers/{transfer_id}/approve")
def approve_inventory_transfer(
    transfer_id: int,
    status_data: InventoryTransferStatusUpdate,
    db: Session = Depends(get_db)
):
    """审核库存调拨单"""
    db_transfer = db.query(InventoryTransfer).filter(InventoryTransfer.id == transfer_id).first()
    if not db_transfer:
        raise HTTPException(status_code=404, detail="库存调拨单不存在")

    if db_transfer.status != InventoryTransferStatus.SUBMITTED:
        raise HTTPException(status_code=400, detail="只有已提交的调拨单才能审核")

    if status_data.status not in [InventoryTransferStatus.APPROVED, InventoryTransferStatus.REJECTED]:
        raise HTTPException(status_code=400, detail="审核状态只能是已审核或已拒绝")

    db_transfer.status = status_data.status
    db_transfer.approved_at = datetime.now()
    db_transfer.approved_by = "system"  # 实际应用中应该是当前用户
    db_transfer.approval_note = status_data.note

    db.commit()

    action = "审核通过" if status_data.status == InventoryTransferStatus.APPROVED else "审核拒绝"
    return {"message": f"库存调拨单{action}"}


@router.post("/transfers/{transfer_id}/transfer")
def transfer_inventory_transfer(transfer_id: int, db: Session = Depends(get_db)):
    """确认调拨"""
    db_transfer = db.query(InventoryTransfer).filter(InventoryTransfer.id == transfer_id).first()
    if not db_transfer:
        raise HTTPException(status_code=404, detail="库存调拨单不存在")

    if db_transfer.status != InventoryTransferStatus.APPROVED:
        raise HTTPException(status_code=400, detail="只有已审核的调拨单才能确认调拨")

    db_transfer.status = InventoryTransferStatus.TRANSFERRED
    db_transfer.transferred_at = datetime.now()
    db_transfer.transferred_by = "system"  # 实际应用中应该是当前用户

    # TODO: 这里应该更新库存，从调出仓库减少库存，向调入仓库增加库存

    db.commit()

    return {"message": "库存调拨确认成功"}


@router.post("/transfers/{transfer_id}/complete")
def complete_inventory_transfer(transfer_id: int, db: Session = Depends(get_db)):
    """完成库存调拨单"""
    db_transfer = db.query(InventoryTransfer).filter(InventoryTransfer.id == transfer_id).first()
    if not db_transfer:
        raise HTTPException(status_code=404, detail="库存调拨单不存在")

    if db_transfer.status != InventoryTransferStatus.TRANSFERRED:
        raise HTTPException(status_code=400, detail="只有已调拨的调拨单才能完成")

    db_transfer.status = InventoryTransferStatus.COMPLETED

    db.commit()

    return {"message": "库存调拨单完成"}


# ==================== 批次管理相关API ====================










