<template>
  <div class="delivery-orders-view">
    <div class="page-header">
      <h2>配送订单管理</h2>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        创建订单
      </el-button>
    </div>

    <!-- 订单统计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ orderStats.total || 0 }}</div>
            <div class="stat-label">总订单数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card pending">
          <div class="stat-content">
            <div class="stat-value">{{ orderStats.pending || 0 }}</div>
            <div class="stat-label">待配送</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card in-transit">
          <div class="stat-content">
            <div class="stat-value">{{ orderStats.in_transit || 0 }}</div>
            <div class="stat-label">配送中</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card delivered">
          <div class="stat-content">
            <div class="stat-value">{{ orderStats.delivered || 0 }}</div>
            <div class="stat-label">已送达</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选和搜索 -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchQuery"
            placeholder="搜索订单号"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-select v-model="statusFilter" placeholder="订单状态" @change="handleStatusFilter">
            <el-option label="全部状态" value="" />
            <el-option label="待配送" value="pending" />
            <el-option label="配送中" value="in_transit" />
            <el-option label="已送达" value="delivered" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY年MM月DD日"
            value-format="YYYY-MM-DD"
            @change="handleDateFilter"
          />
        </el-col>
        <el-col :span="6">
          <el-button @click="fetchOrders">刷新</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 订单列表 -->
    <el-card class="orders-table">
      <el-table
        :data="filteredOrders"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="order_number" label="订单号" width="150" />
        <el-table-column prop="pickup_address" label="取货地址" min-width="200" />
        <el-table-column prop="delivery_address" label="配送地址" min-width="200" />
        <el-table-column prop="weight_kg" label="重量(kg)" width="100" />
        <el-table-column prop="calculated_cost" label="配送费用" width="100">
          <template #default="{ row }">
            ¥{{ row.calculated_cost?.toFixed(2) || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="estimated_delivery_time" label="预计送达" width="180">
          <template #default="{ row }">
            {{ formatDate(row.estimated_delivery_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewOrderDetails(row)">详情</el-button>
            <el-button 
              size="small" 
              type="primary" 
              @click="updateOrderStatus(row)"
              v-if="row.status !== 'delivered' && row.status !== 'cancelled'"
            >
              更新状态
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建订单对话框 -->
    <el-dialog
      title="创建配送订单"
      v-model="showCreateDialog"
      width="600px"
    >
      <el-form :model="orderForm" label-width="120px">
        <el-form-item label="取货地址" required>
          <el-input v-model="orderForm.pickup_address" />
        </el-form-item>
        <el-form-item label="配送地址" required>
          <el-input v-model="orderForm.delivery_address" />
        </el-form-item>
        <el-form-item label="货物重量(kg)" required>
          <el-input-number v-model="orderForm.weight_kg" :min="0.1" :precision="1" />
        </el-form-item>
        <el-form-item label="货物体积(m³)">
          <el-input-number v-model="orderForm.volume_m3" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="包裹数量">
          <el-input-number v-model="orderForm.package_count" :min="1" />
        </el-form-item>
        <el-form-item label="特殊要求">
          <el-input v-model="orderForm.special_requirements" type="textarea" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createOrder">创建订单</el-button>
      </template>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog
      title="订单详情"
      v-model="showDetailsDialog"
      width="700px"
    >
      <div v-if="selectedOrder">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ selectedOrder.order_number }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(selectedOrder.status)">
              {{ getStatusText(selectedOrder.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="取货地址">{{ selectedOrder.pickup_address }}</el-descriptions-item>
          <el-descriptions-item label="配送地址">{{ selectedOrder.delivery_address }}</el-descriptions-item>
          <el-descriptions-item label="货物重量">{{ selectedOrder.weight_kg }} kg</el-descriptions-item>
          <el-descriptions-item label="货物体积">{{ selectedOrder.volume_m3 || 'N/A' }} m³</el-descriptions-item>
          <el-descriptions-item label="包裹数量">{{ selectedOrder.package_count }}</el-descriptions-item>
          <el-descriptions-item label="配送费用">¥{{ selectedOrder.calculated_cost?.toFixed(2) || 0 }}</el-descriptions-item>
          <el-descriptions-item label="预计送达">{{ formatDate(selectedOrder.estimated_delivery_time) }}</el-descriptions-item>
          <el-descriptions-item label="实际送达">{{ formatDate(selectedOrder.actual_delivery_time) || 'N/A' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(selectedOrder.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDate(selectedOrder.updated_at) }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="order-notes" v-if="selectedOrder.special_requirements || selectedOrder.delivery_notes">
          <h4>备注信息:</h4>
          <p v-if="selectedOrder.special_requirements"><strong>特殊要求:</strong> {{ selectedOrder.special_requirements }}</p>
          <p v-if="selectedOrder.delivery_notes"><strong>配送备注:</strong> {{ selectedOrder.delivery_notes }}</p>
        </div>
      </div>
    </el-dialog>

    <!-- 更新状态对话框 -->
    <el-dialog
      title="更新订单状态"
      v-model="showStatusDialog"
      width="400px"
    >
      <el-form label-width="80px">
        <el-form-item label="新状态">
          <el-select v-model="newStatus" placeholder="选择新状态">
            <el-option label="配送中" value="in_transit" />
            <el-option label="已送达" value="delivered" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="statusNotes" type="textarea" placeholder="可选的状态更新备注" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showStatusDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmStatusUpdate">确认更新</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useLogisticsStore } from '../stores/logistics'
import { ElMessage } from 'element-plus'

const logisticsStore = useLogisticsStore()

const searchQuery = ref('')
const statusFilter = ref('')
const dateRange = ref([])
const showCreateDialog = ref(false)
const showDetailsDialog = ref(false)
const showStatusDialog = ref(false)
const selectedOrder = ref(null)
const newStatus = ref('')
const statusNotes = ref('')

const orderForm = ref({
  pickup_address: '',
  delivery_address: '',
  weight_kg: 1,
  volume_m3: 0,
  package_count: 1,
  special_requirements: ''
})

const orders = ref([])
const loading = ref(false)

const orderStats = computed(() => {
  const stats = {
    total: orders.value.length,
    pending: 0,
    in_transit: 0,
    delivered: 0,
    cancelled: 0
  }
  
  orders.value.forEach(order => {
    if (stats.hasOwnProperty(order.status)) {
      stats[order.status]++
    }
  })
  
  return stats
})

const filteredOrders = computed(() => {
  let filtered = orders.value

  if (searchQuery.value) {
    filtered = filtered.filter(order =>
      order.order_number.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  if (statusFilter.value) {
    filtered = filtered.filter(order => order.status === statusFilter.value)
  }

  // 日期筛选逻辑可以在这里添加

  return filtered
})

const fetchOrders = async () => {
  loading.value = true
  try {
    await logisticsStore.fetchDeliveryOrders()
    orders.value = logisticsStore.deliveryOrders
  } catch (error) {
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 搜索逻辑已在computed中实现
}

const handleStatusFilter = () => {
  // 状态筛选逻辑已在computed中实现
}

const handleDateFilter = () => {
  // 日期筛选逻辑
}

const getStatusTagType = (status: string) => {
  const statusMap = {
    'pending': 'warning',
    'in_transit': 'primary',
    'delivered': 'success',
    'cancelled': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    'pending': '待配送',
    'in_transit': '配送中',
    'delivered': '已送达',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

const createOrder = async () => {
  try {
    await logisticsStore.createDeliveryOrder(orderForm.value)
    ElMessage.success('订单创建成功')
    showCreateDialog.value = false
    
    // 重置表单
    orderForm.value = {
      pickup_address: '',
      delivery_address: '',
      weight_kg: 1,
      volume_m3: 0,
      package_count: 1,
      special_requirements: ''
    }
    
    await fetchOrders()
  } catch (error) {
    ElMessage.error('创建订单失败')
  }
}

const viewOrderDetails = (order) => {
  selectedOrder.value = order
  showDetailsDialog.value = true
}

const updateOrderStatus = (order) => {
  selectedOrder.value = order
  newStatus.value = ''
  statusNotes.value = ''
  showStatusDialog.value = true
}

const confirmStatusUpdate = async () => {
  if (!newStatus.value) {
    ElMessage.warning('请选择新状态')
    return
  }
  
  try {
    await logisticsStore.updateOrderStatus(
      selectedOrder.value.id,
      newStatus.value,
      statusNotes.value
    )
    
    ElMessage.success('状态更新成功')
    showStatusDialog.value = false
    await fetchOrders()
  } catch (error) {
    ElMessage.error('状态更新失败')
  }
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchOrders()
})
</script>

<style scoped>
.delivery-orders-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-card.pending {
  border-left: 4px solid #E6A23C;
}

.stat-card.in-transit {
  border-left: 4px solid #409EFF;
}

.stat-card.delivered {
  border-left: 4px solid #67C23A;
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 2em;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8px;
}

.stat-label {
  color: #7f8c8d;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.orders-table {
  margin-bottom: 20px;
}

.order-notes {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.order-notes h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
}

.order-notes p {
  margin: 8px 0;
  color: #606266;
}
</style>
