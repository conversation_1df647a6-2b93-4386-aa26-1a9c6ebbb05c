# 采购入库单字段调整说明

## 调整目标

在采购入库单中去掉应收数量字段，保留实收数量字段，并添加批次号字段。

## 主要更改

### 1. 数据库模型调整

**文件**: `backend/app/models/purchase.py`

**更改内容**:
- 删除 `quantity` 字段（应收数量）
- 保留 `received_quantity` 字段（实收数量）
- 添加 `batch_no` 字段（批次号）

```python
# 修改前
quantity = Column(Integer, nullable=False, comment="应收数量")
received_quantity = Column(Integer, nullable=False, comment="实收数量")

# 修改后
received_quantity = Column(Integer, nullable=False, comment="实收数量")
batch_no = Column(String(100), comment="批次号")
```

### 2. Schema定义调整

**文件**: `backend/app/schemas/purchase.py`

**更改内容**:
- 更新 `PurchaseReceiptItemBase` schema
- 更新 `PurchaseReceiptItemUpdate` schema
- 添加字段验证和描述

```python
# 修改后的Schema
class PurchaseReceiptItemBase(BaseModel):
    product_id: int
    product_name: str
    product_sku: Optional[str] = None
    received_quantity: int = Field(..., ge=0, description="实收数量")
    batch_no: Optional[str] = Field(None, description="批次号")
```

### 3. 前端接口调整

**文件**: `frontend/src/api/purchase-receipt.ts`

**更改内容**:
- 更新 `PurchaseReceiptItem` 接口
- 更新 `PurchaseReceiptItemCreate` 接口

```typescript
// 修改后的接口
export interface PurchaseReceiptItem {
  id?: number
  receipt_id?: number
  product_id: number
  product_name: string
  product_sku?: string
  received_quantity: number
  batch_no?: string
  created_at?: string
}
```

### 4. 前端界面调整

**文件**: `frontend/src/views/PurchaseReceiptView.vue`

**主要更改**:

#### 4.1 表格列调整
- 删除"应收数量"列
- 保留"实收数量"列
- 添加"批次号"列
- 调整列宽分配

```vue
<!-- 修改后的表格 -->
<el-table-column label="商品名称" width="280">
<el-table-column label="实收数量" width="130">
<el-table-column label="批次号" width="150">
```

#### 4.2 表单数据结构调整
- 更新 `ReceiptForm` 接口
- 修改 `addReceiptItem` 函数
- 更新进度计算逻辑

#### 4.3 入库处理对话框调整
- 删除应收数量列
- 添加批次号输入框
- 调整输入验证逻辑

### 5. 后端服务调整

**文件**: `backend/app/services/purchase_receipt_service.py`

**更改内容**:
- 更新创建入库单明细的逻辑
- 更新更新入库单明细的逻辑
- 修改从采购订单创建入库单的逻辑

```python
# 修改后的明细创建逻辑
db_item = PurchaseReceiptItem(
    receipt_id=db_receipt.id,
    product_id=item_data.product_id,
    product_name=product.name,
    product_sku=product.sku,
    received_quantity=item_data.received_quantity,
    batch_no=item_data.batch_no
)
```

## 业务逻辑调整

### 1. 进度计算逻辑

**修改前**: 基于应收数量和实收数量计算完成百分比
**修改后**: 基于商品项目数量计算完成百分比（有实收数量的项目视为已完成）

```javascript
// 修改后的进度计算
const getReceiptProgress = (receipt: PurchaseReceipt) => {
  if (!receipt.items || receipt.items.length === 0) return 0
  
  const totalItems = receipt.items.length
  const receivedItems = receipt.items.filter(item => (item.received_quantity || 0) > 0).length
  
  return totalItems > 0 ? Math.round((receivedItems / totalItems) * 100) : 0
}
```

### 2. 统计显示调整

- 将"商品数量"改为"商品种类"
- 显示商品项目的数量而不是总数量

### 3. 批次管理

- 添加批次号字段，支持批次追踪
- 批次号为可选字段，可以为空
- 在入库处理时可以输入批次号

## 数据库迁移

**迁移脚本**: `backend/migrate_purchase_receipt_items.py`

**迁移内容**:
1. 添加 `batch_no` 字段
2. 删除 `quantity` 字段
3. 保留现有数据的完整性

**使用方法**:
```bash
cd backend
python migrate_purchase_receipt_items.py
```

## 影响分析

### 1. 正面影响
- 简化了入库流程，不需要预设应收数量
- 增加了批次管理功能，提升了库存追踪能力
- 减少了数据冗余，提高了数据一致性

### 2. 注意事项
- 现有的入库单数据需要通过迁移脚本处理
- 进度计算逻辑发生变化，可能影响现有的业务理解
- 需要更新相关的文档和培训材料

### 3. 兼容性
- 前后端接口保持兼容
- 数据库结构变更通过迁移脚本处理
- 现有功能不受影响

## 测试建议

1. **数据库迁移测试**
   - 在测试环境执行迁移脚本
   - 验证数据完整性
   - 测试回滚机制

2. **功能测试**
   - 测试创建入库单功能
   - 测试入库处理功能
   - 测试批次号输入和显示

3. **界面测试**
   - 验证表格列显示正确
   - 测试响应式布局
   - 验证用户交互体验

## 部署步骤

1. 备份现有数据库
2. 部署后端代码更改
3. 执行数据库迁移脚本
4. 部署前端代码更改
5. 验证功能正常
6. 更新用户文档

## 文件清单

### 后端文件
- `backend/app/models/purchase.py` - 数据模型
- `backend/app/schemas/purchase.py` - API Schema
- `backend/app/services/purchase_receipt_service.py` - 业务逻辑
- `backend/migrate_purchase_receipt_items.py` - 迁移脚本

### 前端文件
- `frontend/src/api/purchase-receipt.ts` - API接口定义
- `frontend/src/views/PurchaseReceiptView.vue` - 主界面组件

### 文档文件
- `采购入库单字段调整说明.md` - 本文档
