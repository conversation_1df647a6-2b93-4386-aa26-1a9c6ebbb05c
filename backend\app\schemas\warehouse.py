"""
仓库数据模式
"""

from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class WarehouseBase(BaseModel):
    """仓库基础模型"""
    name: str = Field(..., description="仓库名称")
    address: str = Field(..., description="仓库地址")
    total_capacity_m3: float = Field(..., description="总容量(立方米)")
    used_capacity_m3: float = Field(default=0, description="已用容量(立方米)")
    latitude: Optional[float] = Field(None, description="纬度")
    longitude: Optional[float] = Field(None, description="经度")
    operating_hours: Optional[Dict[str, Any]] = Field(None, description="营业时间")
    contact_info: Optional[Dict[str, Any]] = Field(None, description="联系信息")
    is_active: bool = Field(default=True, description="是否启用")
    status: str = Field(default="active", description="状态")


class WarehouseCreate(WarehouseBase):
    """创建仓库"""
    pass


class WarehouseUpdate(BaseModel):
    """更新仓库"""
    name: Optional[str] = None
    address: Optional[str] = None
    total_capacity_m3: Optional[float] = None
    used_capacity_m3: Optional[float] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    operating_hours: Optional[Dict[str, Any]] = None
    contact_info: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    status: Optional[str] = None


class WarehouseResponse(WarehouseBase):
    """仓库响应"""
    id: int
    available_capacity_m3: float = Field(..., description="可用容量(立方米)")
    utilization_rate: float = Field(..., description="利用率")
    inventory_count: int = Field(..., description="库存商品总数量")
    inventory_sku_count: int = Field(..., description="库存商品种类数量")
    available_stock: int = Field(..., description="可用库存总数量")
    reserved_stock: int = Field(..., description="预留库存总数量")
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class WarehouseQuery(BaseModel):
    """仓库查询参数"""
    name: Optional[str] = Field(None, description="仓库名称")
    address: Optional[str] = Field(None, description="仓库地址")
    status: Optional[str] = Field(None, description="状态")
    is_active: Optional[bool] = Field(None, description="是否启用")
    min_capacity: Optional[float] = Field(None, description="最小容量")
    max_capacity: Optional[float] = Field(None, description="最大容量")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")


class WarehouseStats(BaseModel):
    """仓库统计"""
    total_warehouses: int = Field(..., description="仓库总数")
    active_warehouses: int = Field(..., description="活跃仓库数")
    total_capacity: float = Field(..., description="总容量")
    used_capacity: float = Field(..., description="已用容量")
    available_capacity: float = Field(..., description="可用容量")
    average_utilization_rate: float = Field(..., description="平均利用率")


# 兼容旧版本的简化Schema（用于物流模块）
class WarehouseLegacyBase(BaseModel):
    """仓库基础模型（兼容旧版本）"""
    name: str
    address: str
    capacity: Optional[int] = None
    manager: Optional[str] = None
    phone: Optional[str] = None
    status: Optional[str] = "active"


class WarehouseLegacyCreate(WarehouseLegacyBase):
    """创建仓库（兼容旧版本）"""
    pass


class WarehouseLegacyUpdate(BaseModel):
    """更新仓库（兼容旧版本）"""
    name: Optional[str] = None
    address: Optional[str] = None
    capacity: Optional[int] = None
    manager: Optional[str] = None
    phone: Optional[str] = None
    status: Optional[str] = None


class WarehouseLegacy(WarehouseLegacyBase):
    """仓库（兼容旧版本）"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
