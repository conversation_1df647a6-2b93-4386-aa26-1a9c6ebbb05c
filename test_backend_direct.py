#!/usr/bin/env python3
"""
直接测试后端API
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

import asyncio
import json
from backend.app.api.inventory import get_inventory_list, get_inventory_stats, get_warehouses
from backend.app.core.database import SessionLocal

async def test_inventory_endpoints():
    """测试库存API端点"""
    db = SessionLocal()
    try:
        print("测试库存列表API...")
        
        # 测试获取库存列表
        response = await get_inventory_list(
            page=1,
            page_size=20,
            db=db
        )
        
        print(f"库存列表响应类型: {type(response)}")
        print(f"库存列表响应: {response}")
        
        # 测试获取库存统计
        print("\n测试库存统计API...")
        stats_response = await get_inventory_stats(db=db)
        print(f"统计响应: {stats_response}")
        
        # 测试获取仓库列表
        print("\n测试仓库列表API...")
        warehouses_response = await get_warehouses(db=db)
        print(f"仓库列表响应: {warehouses_response}")
        
    except Exception as e:
        print(f'测试API时出错: {e}')
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == '__main__':
    asyncio.run(test_inventory_endpoints())
