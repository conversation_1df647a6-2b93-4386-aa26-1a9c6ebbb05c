from pydantic import BaseModel
from typing import Optional, Dict, Any, List
from datetime import datetime

class LogisticsRouteBase(BaseModel):
    route_name: str
    origin: str
    destination: str
    distance_km: float
    estimated_time_hours: float
    base_cost: float
    cost_per_km: float

class LogisticsRouteCreate(LogisticsRouteBase):
    fuel_cost: Optional[float] = None
    toll_cost: Optional[float] = None
    route_details: Optional[Dict[str, Any]] = None

class LogisticsRoute(LogisticsRouteBase):
    id: int
    fuel_cost: Optional[float] = None
    toll_cost: Optional[float] = None
    is_active: str
    route_details: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class DeliveryOrderBase(BaseModel):
    pickup_address: str
    delivery_address: str
    weight_kg: float
    volume_m3: Optional[float] = None
    package_count: int = 1

class DeliveryOrderCreate(DeliveryOrderBase):
    special_requirements: Optional[str] = None

class DeliveryOrder(DeliveryOrderBase):
    id: int
    order_number: str
    route_id: int
    calculated_cost: Optional[float] = None
    estimated_delivery_time: Optional[datetime] = None
    actual_delivery_time: Optional[datetime] = None
    status: str
    special_requirements: Optional[str] = None
    delivery_notes: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class CostCalculationRequest(BaseModel):
    pickup_address: str
    delivery_address: str
    weight_kg: float
    volume_m3: Optional[float] = None
    package_count: int = 1
    delivery_type: str = "standard"  # standard, express, same_day

class CostCalculationResponse(BaseModel):
    total_cost: float
    base_cost: float
    distance_cost: float
    weight_cost: float
    additional_fees: Dict[str, float]
    estimated_delivery_time: datetime
    recommended_route: LogisticsRoute

class RouteOptimizationRequest(BaseModel):
    pickup_points: List[str]
    delivery_points: List[str]
    vehicle_capacity: float
    optimization_type: str = "cost"  # cost, time, distance

class RouteOptimizationResponse(BaseModel):
    optimized_routes: List[Dict[str, Any]]
    total_distance: float
    total_cost: float
    total_time: float
    efficiency_score: float


