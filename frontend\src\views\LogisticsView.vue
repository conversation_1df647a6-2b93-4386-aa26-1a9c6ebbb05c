<template>
  <div class="logistics-view">
    <div class="page-header">
      <h2>物流配送总览</h2>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="40" color="#409EFF">
                <Van />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ performanceData?.total_orders || 0 }}</div>
              <div class="stat-label">总订单数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="40" color="#67C23A">
                <SuccessFilled />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ performanceData?.delivery_rate || 0 }}%</div>
              <div class="stat-label">配送成功率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="40" color="#E6A23C">
                <Timer />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ performanceData?.on_time_rate || 0 }}%</div>
              <div class="stat-label">准时率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="40" color="#F56C6C">
                <Money />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">¥{{ performanceData?.average_cost || 0 }}</div>
              <div class="stat-label">平均成本</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :span="8">
        <el-card class="action-card">
          <template #header>
            <div class="card-header">
              <el-icon><Calculator /></el-icon>
              <span>成本计算</span>
            </div>
          </template>
          <p>快速计算配送成本和时效</p>
          <el-button type="primary" @click="$router.push('/logistics/cost-calculator')">
            开始计算
          </el-button>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="action-card">
          <template #header>
            <div class="card-header">
              <el-icon><MapLocation /></el-icon>
              <span>路线管理</span>
            </div>
          </template>
          <p>管理和优化配送路线</p>
          <el-button type="primary" @click="$router.push('/logistics/routes')">
            查看路线
          </el-button>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="action-card">
          <template #header>
            <div class="card-header">
              <el-icon><Document /></el-icon>
              <span>订单管理</span>
            </div>
          </template>
          <p>查看和管理配送订单</p>
          <el-button type="primary" @click="$router.push('/logistics/orders')">
            查看订单
          </el-button>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近订单 -->
    <el-card class="recent-orders">
      <template #header>
        <div class="card-header">
          <el-icon><List /></el-icon>
          <span>最近订单</span>
        </div>
      </template>
      
      <el-table :data="recentOrders" v-loading="loading">
        <el-table-column prop="order_number" label="订单号" width="150" />
        <el-table-column prop="pickup_address" label="取货地址" min-width="200" />
        <el-table-column prop="delivery_address" label="配送地址" min-width="200" />
        <el-table-column prop="calculated_cost" label="配送费用" width="100">
          <template #default="{ row }">
            ¥{{ row.calculated_cost?.toFixed(2) || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 订单状态分布图表 -->
    <el-card class="status-chart" v-if="performanceData?.status_distribution">
      <template #header>
        <div class="card-header">
          <el-icon><PieChart /></el-icon>
          <span>订单状态分布</span>
        </div>
      </template>
      
      <div class="status-grid">
        <div 
          v-for="(count, status) in performanceData.status_distribution" 
          :key="status"
          class="status-item"
        >
          <div class="status-count">{{ count }}</div>
          <div class="status-name">{{ getStatusText(status) }}</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useLogisticsStore } from '../stores/logistics'

const logisticsStore = useLogisticsStore()

const performanceData = ref(null)
const recentOrders = ref([])
const loading = ref(false)

const fetchData = async () => {
  loading.value = true
  try {
    // 获取性能数据
    performanceData.value = await logisticsStore.fetchPerformanceData('30d')
    
    // 获取最近订单
    await logisticsStore.fetchDeliveryOrders({ limit: 10 })
    recentOrders.value = logisticsStore.deliveryOrders
  } catch (error) {
    console.error('获取数据失败:', error)
  } finally {
    loading.value = false
  }
}

const getStatusTagType = (status: string) => {
  const statusMap = {
    'pending': 'warning',
    'in_transit': 'primary',
    'delivered': 'success',
    'cancelled': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    'pending': '待配送',
    'in_transit': '配送中',
    'delivered': '已送达',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchData()
})
</script>

<style>
.logistics-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 12px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 20px;
}

.stat-value {
  font-size: 2em;
  font-weight: bold;
  color: #2c3e50;
}

.stat-label {
  color: #7f8c8d;
  font-size: 0.9em;
}

.quick-actions {
  margin-bottom: 30px;
}

.action-card {
  text-align: center;
  min-height: 150px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.action-card p {
  margin: 15px 0;
  color: #7f8c8d;
}

.recent-orders {
  margin-bottom: 30px;
}

.status-chart {
  margin-bottom: 20px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.status-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-count {
  font-size: 2em;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.status-name {
  color: #7f8c8d;
}
</style>
