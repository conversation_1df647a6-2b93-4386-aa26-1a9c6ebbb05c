"""
采购订单API路由
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
import math
from app.core.database import get_db
from app.schemas.purchase import (
    PurchaseOrder, PurchaseOrderCreate, PurchaseOrderUpdate,
    PurchaseOrderListResponse, PurchaseOrderQuery, PurchaseOrderStats,
    PurchaseOrderStatus,
    PurchaseReceipt, PurchaseReceiptCreate, PurchaseReceiptUpdate,
    PurchaseReceiptListResponse, PurchaseReceiptQuery, PurchaseReceiptStatus,
    PurchaseReturn as PurchaseReturnSchema,
    PurchaseReturnCreate, PurchaseReturnUpdate, PurchaseReturnStatusUpdate,
    PurchaseReturnStats, PurchaseReturnQuery
)
from app.services.purchase_service import PurchaseService
from app.models.purchase import PurchaseReturn, PurchaseReturnItem, PurchaseReturnStatus
from app.models.purchase import PurchaseReceipt as PurchaseReceiptModel
from app.models.supplier import Supplier
from app.core.utils import generate_order_number

router = APIRouter()


@router.get("/", response_model=PurchaseOrderListResponse)
async def get_purchase_orders(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    order_no: Optional[str] = Query(None, description="订单号"),
    supplier_id: Optional[int] = Query(None, description="供应商ID"),
    status: Optional[PurchaseOrderStatus] = Query(None, description="订单状态"),
    created_by: Optional[str] = Query(None, description="创建人"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    db: Session = Depends(get_db)
):
    """获取采购订单列表"""
    
    service = PurchaseService(db)
    
    # 构建查询参数
    query = PurchaseOrderQuery(
        order_no=order_no,
        supplier_id=supplier_id,
        status=status,
        created_by=created_by,
        start_date=start_date,
        end_date=end_date
    )
    
    # 计算偏移量
    skip = (page - 1) * page_size
    
    # 获取数据
    orders, total = service.get_purchase_orders(skip=skip, limit=page_size, query=query)
    
    # 添加供应商名称
    for order in orders:
        if order.supplier:
            order.supplier_name = order.supplier.name
    
    # 计算总页数
    total_pages = math.ceil(total / page_size) if total > 0 else 1
    
    return PurchaseOrderListResponse(
        items=orders,
        total=total,
        page=page,
        page_size=page_size,
        total_pages=total_pages
    )


@router.get("/stats", response_model=PurchaseOrderStats)
async def get_purchase_order_stats(db: Session = Depends(get_db)):
    """获取采购订单统计信息"""
    service = PurchaseService(db)
    return service.get_purchase_order_stats()


@router.get("/{order_id}", response_model=PurchaseOrder)
async def get_purchase_order(order_id: int, db: Session = Depends(get_db)):
    """获取单个采购订单"""
    service = PurchaseService(db)
    order = service.get_purchase_order(order_id)
    if not order:
        raise HTTPException(status_code=404, detail="采购订单不存在")
    
    # 添加供应商名称
    if order.supplier:
        order.supplier_name = order.supplier.name
    
    return order


@router.post("/", response_model=PurchaseOrder)
async def create_purchase_order(order_data: PurchaseOrderCreate, db: Session = Depends(get_db)):
    """创建采购订单"""
    service = PurchaseService(db)
    try:
        order = service.create_purchase_order(order_data)
        
        # 添加供应商名称
        if order.supplier:
            order.supplier_name = order.supplier.name
        
        return order
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{order_id}", response_model=PurchaseOrder)
async def update_purchase_order(
    order_id: int, 
    order_data: PurchaseOrderUpdate, 
    db: Session = Depends(get_db)
):
    """更新采购订单"""
    service = PurchaseService(db)
    try:
        order = service.update_purchase_order(order_id, order_data)
        if not order:
            raise HTTPException(status_code=404, detail="采购订单不存在")
        
        # 添加供应商名称
        if order.supplier:
            order.supplier_name = order.supplier.name
        
        return order
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/{order_id}")
async def delete_purchase_order(order_id: int, db: Session = Depends(get_db)):
    """删除采购订单"""
    service = PurchaseService(db)
    try:
        success = service.delete_purchase_order(order_id)
        if not success:
            raise HTTPException(status_code=404, detail="采购订单不存在")
        return {"message": "采购订单删除成功"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{order_id}/submit")
async def submit_purchase_order(
    order_id: int,
    submitted_by: str,
    db: Session = Depends(get_db)
):
    """提交采购订单"""
    service = PurchaseService(db)

    # 检查订单是否存在且状态为草稿
    order = service.get_purchase_order(order_id)
    if not order:
        raise HTTPException(status_code=404, detail="采购订单不存在")

    if order.status != PurchaseOrderStatus.DRAFT:
        raise HTTPException(status_code=400, detail="只有草稿状态的订单才能提交")

    # 更新为已提交状态
    update_data = PurchaseOrderUpdate(
        status=PurchaseOrderStatus.SUBMITTED,
        submitted_by=submitted_by,
        submitted_at=datetime.now()
    )

    updated_order = service.update_purchase_order(order_id, update_data)
    return {"message": "采购订单提交成功", "order": updated_order}


@router.put("/{order_id}/approve")
async def approve_purchase_order(
    order_id: int,
    approved_by: str = Query(..., description="审核人"),
    approved: bool = Query(True, description="是否通过审核"),
    remark: Optional[str] = Query(None, description="审核备注"),
    db: Session = Depends(get_db)
):
    """审核采购订单"""
    service = PurchaseService(db)

    # 检查订单是否存在且状态为已提交
    order = service.get_purchase_order(order_id)
    if not order:
        raise HTTPException(status_code=404, detail="采购订单不存在")

    if order.status != PurchaseOrderStatus.SUBMITTED:
        raise HTTPException(status_code=400, detail="只有已提交状态的订单才能审核")

    # 根据审核结果更新状态
    if approved:
        new_status = PurchaseOrderStatus.APPROVED
        message = "采购订单审核通过"
    else:
        new_status = PurchaseOrderStatus.REJECTED
        message = "采购订单审核拒绝"

    update_data = PurchaseOrderUpdate(
        status=new_status,
        approved_by=approved_by,
        approved_at=datetime.now()
    )

    # 如果有审核备注，添加到备注字段
    if remark:
        update_data.remark = remark

    updated_order = service.update_purchase_order(order_id, update_data)
    return {"message": message, "order": updated_order}


@router.put("/{order_id}/recall")
async def recall_purchase_order(
    order_id: int,
    db: Session = Depends(get_db)
):
    """撤回采购订单"""
    service = PurchaseService(db)

    # 检查订单是否存在且状态为已提交
    order = service.get_purchase_order(order_id)
    if not order:
        raise HTTPException(status_code=404, detail="采购订单不存在")

    if order.status != PurchaseOrderStatus.SUBMITTED:
        raise HTTPException(status_code=400, detail="只有已提交状态的订单才能撤回")

    # 更新为草稿状态，清除提交信息
    update_data = PurchaseOrderUpdate(
        status=PurchaseOrderStatus.DRAFT,
        submitted_by=None,
        submitted_at=None
    )

    updated_order = service.update_purchase_order(order_id, update_data)
    return {"message": "采购订单撤回成功", "order": updated_order}


@router.put("/{order_id}/status")
async def update_order_status(
    order_id: int,
    status: PurchaseOrderStatus,
    approved_by: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """更新订单状态（保留兼容性）"""
    service = PurchaseService(db)

    update_data = PurchaseOrderUpdate(status=status)
    if status == PurchaseOrderStatus.APPROVED and approved_by:
        update_data.approved_by = approved_by
        update_data.approved_at = datetime.now()

    order = service.update_purchase_order(order_id, update_data)
    if not order:
        raise HTTPException(status_code=404, detail="采购订单不存在")

    return {"message": "订单状态更新成功"}


# ==================== 采购入库单相关API ====================

@router.get("/receipts/", response_model=PurchaseReceiptListResponse)
async def get_purchase_receipts(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    receipt_no: Optional[str] = Query(None, description="入库单号"),
    purchase_order_no: Optional[str] = Query(None, description="采购订单号"),
    supplier_id: Optional[int] = Query(None, description="供应商ID"),
    warehouse_id: Optional[int] = Query(None, description="仓库ID"),
    status: Optional[PurchaseReceiptStatus] = Query(None, description="入库单状态"),
    created_by: Optional[str] = Query(None, description="创建人"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    db: Session = Depends(get_db)
):
    """获取采购入库单列表"""

    service = PurchaseService(db)

    # 构建查询参数
    query_params = PurchaseReceiptQuery(
        receipt_no=receipt_no,
        purchase_order_no=purchase_order_no,
        supplier_id=supplier_id,
        warehouse_id=warehouse_id,
        status=status,
        created_by=created_by,
        start_date=start_date,
        end_date=end_date
    )

    # 计算分页参数
    skip = (page - 1) * page_size

    # 获取数据
    receipts, total = service.get_purchase_receipts(skip=skip, limit=page_size, query=query_params)

    # 添加关联对象名称
    for receipt in receipts:
        if receipt.supplier:
            receipt.supplier_name = receipt.supplier.name
        if receipt.warehouse:
            receipt.warehouse_name = receipt.warehouse.name

    # 计算总页数
    total_pages = math.ceil(total / page_size) if total > 0 else 1

    return PurchaseReceiptListResponse(
        items=receipts,
        total=total,
        page=page,
        page_size=page_size,
        total_pages=total_pages
    )


@router.get("/receipts/stats")
async def get_purchase_receipt_stats(db: Session = Depends(get_db)):
    """获取采购入库单统计信息"""
    service = PurchaseService(db)
    return service.get_purchase_receipt_stats()


@router.get("/receipts/{receipt_id}", response_model=PurchaseReceipt)
async def get_purchase_receipt(receipt_id: int, db: Session = Depends(get_db)):
    """获取单个采购入库单"""
    service = PurchaseService(db)
    receipt = service.get_purchase_receipt(receipt_id)

    if not receipt:
        raise HTTPException(status_code=404, detail="采购入库单不存在")

    # 添加关联对象名称
    if receipt.supplier:
        receipt.supplier_name = receipt.supplier.name
    if receipt.warehouse:
        receipt.warehouse_name = receipt.warehouse.name

    return receipt


@router.post("/receipts/", response_model=PurchaseReceipt)
async def create_purchase_receipt(receipt_data: PurchaseReceiptCreate, db: Session = Depends(get_db)):
    """创建采购入库单"""
    service = PurchaseService(db)
    try:
        receipt = service.create_purchase_receipt(receipt_data)

        # 添加关联对象名称
        if receipt.supplier:
            receipt.supplier_name = receipt.supplier.name
        if receipt.warehouse:
            receipt.warehouse_name = receipt.warehouse.name

        return receipt
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/receipts/{receipt_id}", response_model=PurchaseReceipt)
async def update_purchase_receipt(
    receipt_id: int,
    receipt_data: PurchaseReceiptUpdate,
    db: Session = Depends(get_db)
):
    """更新采购入库单"""
    service = PurchaseService(db)
    try:
        receipt = service.update_purchase_receipt(receipt_id, receipt_data)
        if not receipt:
            raise HTTPException(status_code=404, detail="采购入库单不存在")

        # 添加关联对象名称
        if receipt.supplier:
            receipt.supplier_name = receipt.supplier.name
        if receipt.warehouse:
            receipt.warehouse_name = receipt.warehouse.name

        return receipt
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/receipts/{receipt_id}")
async def delete_purchase_receipt(receipt_id: int, db: Session = Depends(get_db)):
    """删除采购入库单"""
    service = PurchaseService(db)
    try:
        success = service.delete_purchase_receipt(receipt_id)
        if not success:
            raise HTTPException(status_code=404, detail="采购入库单不存在")

        return {"message": "采购入库单删除成功"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/receipts/from-order/{order_id}", response_model=PurchaseReceipt)
async def create_receipt_from_order(
    order_id: int,
    warehouse_id: int = Query(..., description="仓库ID"),
    created_by: str = Query(..., description="创建人"),
    db: Session = Depends(get_db)
):
    """从采购订单创建入库单"""
    service = PurchaseService(db)
    try:
        receipt = service.create_receipt_from_order(order_id, warehouse_id, created_by)

        # 添加关联对象名称
        if receipt.supplier:
            receipt.supplier_name = receipt.supplier.name
        if receipt.warehouse:
            receipt.warehouse_name = receipt.warehouse.name

        return receipt
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/receipts/{receipt_id}/submit")
async def submit_purchase_receipt(
    receipt_id: int,
    submitted_by: str = Query(..., description="提交人"),
    db: Session = Depends(get_db)
):
    """提交采购入库单"""
    service = PurchaseService(db)

    try:
        updated_receipt = service.submit_purchase_receipt(receipt_id, submitted_by)
        if not updated_receipt:
            raise HTTPException(status_code=404, detail="采购入库单不存在")

        return {"message": "采购入库单提交成功", "receipt": updated_receipt}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/receipts/{receipt_id}/approve")
async def approve_purchase_receipt(
    receipt_id: int,
    approved_by: str = Query(..., description="审核人"),
    approved: bool = Query(True, description="是否通过审核"),
    remark: Optional[str] = Query(None, description="审核备注"),
    db: Session = Depends(get_db)
):
    """审核采购入库单"""
    service = PurchaseService(db)

    try:
        updated_receipt = service.approve_purchase_receipt(receipt_id, approved_by, approved, remark)
        if not updated_receipt:
            raise HTTPException(status_code=404, detail="采购入库单不存在")

        action = "通过" if approved else "拒绝"
        return {"message": f"采购入库单审核{action}成功", "receipt": updated_receipt}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/receipts/{receipt_id}/recall")
async def recall_purchase_receipt(
    receipt_id: int,
    db: Session = Depends(get_db)
):
    """撤回采购入库单"""
    service = PurchaseService(db)

    try:
        updated_receipt = service.recall_purchase_receipt(receipt_id)
        if not updated_receipt:
            raise HTTPException(status_code=404, detail="采购入库单不存在")

        return {"message": "采购入库单撤回成功", "receipt": updated_receipt}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/receipts/{receipt_id}/cancel")
async def cancel_purchase_receipt(
    receipt_id: int,
    db: Session = Depends(get_db)
):
    """取消采购入库单"""
    service = PurchaseService(db)

    try:
        updated_receipt = service.cancel_purchase_receipt(receipt_id)
        if not updated_receipt:
            raise HTTPException(status_code=404, detail="采购入库单不存在")

        return {"message": "采购入库单取消成功", "receipt": updated_receipt}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


# ==================== 采购退货单相关API ====================

def get_purchase_return_stats(db: Session) -> PurchaseReturnStats:
    """获取采购退货单统计信息"""
    from sqlalchemy import func

    # 基础统计
    total_returns = db.query(PurchaseReturn).count()
    draft_returns = db.query(PurchaseReturn).filter(PurchaseReturn.status == PurchaseReturnStatus.DRAFT).count()
    submitted_returns = db.query(PurchaseReturn).filter(PurchaseReturn.status == PurchaseReturnStatus.SUBMITTED).count()
    approved_returns = db.query(PurchaseReturn).filter(PurchaseReturn.status == PurchaseReturnStatus.APPROVED).count()
    returned_returns = db.query(PurchaseReturn).filter(PurchaseReturn.status == PurchaseReturnStatus.RETURNED).count()
    completed_returns = db.query(PurchaseReturn).filter(PurchaseReturn.status == PurchaseReturnStatus.COMPLETED).count()

    # 金额统计
    total_amount = db.query(func.sum(PurchaseReturn.total_amount)).scalar() or 0

    return PurchaseReturnStats(
        total_returns=total_returns,
        draft_returns=draft_returns,
        submitted_returns=submitted_returns,
        approved_returns=approved_returns,
        returned_returns=returned_returns,
        completed_returns=completed_returns,
        total_amount=total_amount
    )


@router.get("/returns/stats", response_model=PurchaseReturnStats)
def get_return_stats(db: Session = Depends(get_db)):
    """获取采购退货单统计信息"""
    return get_purchase_return_stats(db)


@router.get("/returns/", response_model=List[PurchaseReturnSchema])
def get_purchase_returns(
    return_no: Optional[str] = Query(None, description="退货单号"),
    supplier_id: Optional[int] = Query(None, description="供应商ID"),
    status: Optional[PurchaseReturnStatus] = Query(None, description="状态"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取采购退货单列表"""
    query = db.query(PurchaseReturn)

    # 应用筛选条件
    if return_no:
        query = query.filter(PurchaseReturn.return_no.contains(return_no))
    if supplier_id:
        query = query.filter(PurchaseReturn.supplier_id == supplier_id)
    if status:
        query = query.filter(PurchaseReturn.status == status)
    if start_date:
        query = query.filter(PurchaseReturn.return_date >= start_date)
    if end_date:
        query = query.filter(PurchaseReturn.return_date <= end_date)

    # 分页
    offset = (page - 1) * page_size
    returns = query.offset(offset).limit(page_size).all()

    # 添加关联信息
    result = []
    for return_obj in returns:
        return_dict = return_obj.__dict__.copy()

        # 添加供应商名称
        if return_obj.supplier:
            return_dict['supplier_name'] = return_obj.supplier.name

        # 添加采购入库单号
        if return_obj.purchase_receipt:
            return_dict['purchase_receipt_no'] = return_obj.purchase_receipt.receipt_no

        result.append(PurchaseReturnSchema.model_validate(return_dict))

    return result


@router.get("/returns/{return_id}", response_model=PurchaseReturnSchema)
def get_purchase_return(return_id: int, db: Session = Depends(get_db)):
    """获取采购退货单详情"""
    return_obj = db.query(PurchaseReturn).filter(PurchaseReturn.id == return_id).first()
    if not return_obj:
        raise HTTPException(status_code=404, detail="采购退货单不存在")

    return_dict = return_obj.__dict__.copy()

    # 添加关联信息
    if return_obj.supplier:
        return_dict['supplier_name'] = return_obj.supplier.name
    if return_obj.purchase_receipt:
        return_dict['purchase_receipt_no'] = return_obj.purchase_receipt.receipt_no

    return PurchaseReturnSchema.model_validate(return_dict)


@router.post("/returns/", response_model=PurchaseReturnSchema)
def create_purchase_return(return_data: PurchaseReturnCreate, db: Session = Depends(get_db)):
    """创建采购退货单"""
    # 验证供应商是否存在
    supplier = db.query(Supplier).filter(Supplier.id == return_data.supplier_id).first()
    if not supplier:
        raise HTTPException(status_code=404, detail="供应商不存在")

    # 验证采购入库单是否存在（如果提供）
    if return_data.purchase_receipt_id:
        receipt = db.query(PurchaseReceiptModel).filter(PurchaseReceiptModel.id == return_data.purchase_receipt_id).first()
        if not receipt:
            raise HTTPException(status_code=404, detail="采购入库单不存在")

    # 生成退货单号
    if not return_data.return_no:
        return_data.return_no = generate_order_number("PR")

    # 创建退货单
    db_return = PurchaseReturn(
        return_no=return_data.return_no,
        purchase_receipt_id=return_data.purchase_receipt_id,
        supplier_id=return_data.supplier_id,
        return_date=return_data.return_date,
        reason=return_data.reason,
        total_amount=return_data.total_amount,
        remark=return_data.remark,
        status=PurchaseReturnStatus.DRAFT
    )

    db.add(db_return)
    db.flush()  # 获取ID

    # 创建退货明细
    for item_data in return_data.items:
        db_item = PurchaseReturnItem(
            purchase_return_id=db_return.id,
            product_id=item_data.product_id,
            product_name=item_data.product_name,
            product_sku=item_data.product_sku,
            return_quantity=item_data.return_quantity,
            unit_price=item_data.unit_price,
            total_price=item_data.total_price,
            quality_issue=item_data.quality_issue
        )
        db.add(db_item)

    db.commit()
    db.refresh(db_return)

    return_dict = db_return.__dict__.copy()
    if db_return.supplier:
        return_dict['supplier_name'] = db_return.supplier.name
    if db_return.purchase_receipt:
        return_dict['purchase_receipt_no'] = db_return.purchase_receipt.receipt_no

    return PurchaseReturnSchema.model_validate(return_dict)


@router.put("/returns/{return_id}", response_model=PurchaseReturnSchema)
def update_purchase_return(return_id: int, return_data: PurchaseReturnUpdate, db: Session = Depends(get_db)):
    """更新采购退货单"""
    db_return = db.query(PurchaseReturn).filter(PurchaseReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="采购退货单不存在")

    # 只有草稿状态才能修改
    if db_return.status != PurchaseReturnStatus.DRAFT:
        raise HTTPException(status_code=400, detail="只有草稿状态的退货单才能修改")

    # 更新基本信息
    update_data = return_data.model_dump(exclude_unset=True)
    items_data = update_data.pop('items', None)

    for field, value in update_data.items():
        setattr(db_return, field, value)

    # 更新明细
    if items_data is not None:
        # 删除原有明细
        db.query(PurchaseReturnItem).filter(PurchaseReturnItem.purchase_return_id == return_id).delete()

        # 创建新明细
        for item_data in items_data:
            db_item = PurchaseReturnItem(
                purchase_return_id=return_id,
                **item_data
            )
            db.add(db_item)

    db.commit()
    db.refresh(db_return)

    return_dict = db_return.__dict__.copy()
    if db_return.supplier:
        return_dict['supplier_name'] = db_return.supplier.name
    if db_return.purchase_receipt:
        return_dict['purchase_receipt_no'] = db_return.purchase_receipt.receipt_no

    return PurchaseReturnSchema.model_validate(return_dict)


@router.delete("/returns/{return_id}")
def delete_purchase_return(return_id: int, db: Session = Depends(get_db)):
    """删除采购退货单"""
    db_return = db.query(PurchaseReturn).filter(PurchaseReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="采购退货单不存在")

    # 只有草稿状态才能删除
    if db_return.status != PurchaseReturnStatus.DRAFT:
        raise HTTPException(status_code=400, detail="只有草稿状态的退货单才能删除")

    db.delete(db_return)
    db.commit()

    return {"message": "采购退货单删除成功"}


@router.post("/returns/{return_id}/submit")
def submit_purchase_return(return_id: int, db: Session = Depends(get_db)):
    """提交采购退货单"""
    db_return = db.query(PurchaseReturn).filter(PurchaseReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="采购退货单不存在")

    if db_return.status != PurchaseReturnStatus.DRAFT:
        raise HTTPException(status_code=400, detail="只有草稿状态的退货单才能提交")

    db_return.status = PurchaseReturnStatus.SUBMITTED
    db_return.submitted_at = datetime.now()
    db_return.submitted_by = "system"  # 实际应用中应该是当前用户

    db.commit()

    return {"message": "采购退货单提交成功"}


@router.post("/returns/{return_id}/approve")
def approve_purchase_return(
    return_id: int,
    status_data: PurchaseReturnStatusUpdate,
    db: Session = Depends(get_db)
):
    """审核采购退货单"""
    db_return = db.query(PurchaseReturn).filter(PurchaseReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="采购退货单不存在")

    if db_return.status != PurchaseReturnStatus.SUBMITTED:
        raise HTTPException(status_code=400, detail="只有已提交的退货单才能审核")

    if status_data.status not in [PurchaseReturnStatus.APPROVED, PurchaseReturnStatus.REJECTED]:
        raise HTTPException(status_code=400, detail="审核状态只能是已审核或已拒绝")

    db_return.status = status_data.status
    db_return.approved_at = datetime.now()
    db_return.approved_by = "system"  # 实际应用中应该是当前用户
    db_return.approval_note = status_data.note

    db.commit()

    action = "审核通过" if status_data.status == PurchaseReturnStatus.APPROVED else "审核拒绝"
    return {"message": f"采购退货单{action}"}


@router.post("/returns/{return_id}/return")
def return_purchase_return(return_id: int, db: Session = Depends(get_db)):
    """确认退货"""
    db_return = db.query(PurchaseReturn).filter(PurchaseReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="采购退货单不存在")

    if db_return.status != PurchaseReturnStatus.APPROVED:
        raise HTTPException(status_code=400, detail="只有已审核的退货单才能确认退货")

    db_return.status = PurchaseReturnStatus.RETURNED
    db_return.returned_at = datetime.now()
    db_return.returned_by = "system"  # 实际应用中应该是当前用户

    # TODO: 这里应该更新库存，减少相应商品的库存数量

    db.commit()

    return {"message": "采购退货确认成功"}


@router.post("/returns/{return_id}/complete")
def complete_purchase_return(return_id: int, db: Session = Depends(get_db)):
    """完成采购退货单"""
    db_return = db.query(PurchaseReturn).filter(PurchaseReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="采购退货单不存在")

    if db_return.status != PurchaseReturnStatus.RETURNED:
        raise HTTPException(status_code=400, detail="只有已退货的退货单才能完成")

    db_return.status = PurchaseReturnStatus.COMPLETED

    db.commit()

    return {"message": "采购退货单完成"}
