<template>
  <div class="home-view">
    <!-- 主要内容容器 -->
    <div class="main-container">
      <!-- 欢迎区域 -->
      <div class="welcome-section">
        <div class="welcome-content">
          <div class="welcome-text">
            <h1>欢迎回来，{{ authStore.user?.username || '用户' }}！</h1>
            <p>今天是 {{ currentDate }}，让我们开始高效的工作吧</p>
          </div>
          <div class="welcome-actions">
            <el-button type="primary" size="large" @click="$router.push('/products')">
              <el-icon><Plus /></el-icon>
              快速添加商品
            </el-button>
            <el-button size="large" @click="$router.push('/logistics/cost-calculator')">
              <el-icon><Tools /></el-icon>
              成本计算
            </el-button>
          </div>
        </div>
      </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="24" class="stats-cards">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32" color="#667eea">
                  <Box />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ dashboardData?.total_products || 0 }}</div>
                <div class="stat-label">商品总数</div>
              </div>
            </div>
            <div class="stat-trend">
              <el-icon color="#67C23A"><TrendCharts /></el-icon>
              <span class="trend-text">+12%</span>
            </div>
          </div>
        </el-col>

        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32" color="#67C23A">
                  <TrendCharts />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ dashboardData?.average_profit_margin || 0 }}%</div>
                <div class="stat-label">平均利润率</div>
              </div>
            </div>
            <div class="stat-trend">
              <el-icon color="#67C23A"><TrendCharts /></el-icon>
              <span class="trend-text">+5.2%</span>
            </div>
          </div>
        </el-col>

        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32" color="#E6A23C">
                  <Van />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ logisticsData?.total_orders || 0 }}</div>
                <div class="stat-label">配送订单</div>
              </div>
            </div>
            <div class="stat-trend">
              <el-icon color="#67C23A"><TrendCharts /></el-icon>
              <span class="trend-text">+8.1%</span>
            </div>
          </div>
        </el-col>

        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32" color="#F56C6C">
                  <DataAnalysis />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ logisticsData?.delivery_rate || 0 }}%</div>
                <div class="stat-label">配送成功率</div>
              </div>
            </div>
            <div class="stat-trend">
              <el-icon color="#67C23A"><TrendCharts /></el-icon>
              <span class="trend-text">+2.3%</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 功能模块 -->
    <div class="modules-section">
      <el-row :gutter="24" class="feature-modules">
        <el-col :span="12">
          <div class="feature-card">
            <div class="card-header">
              <div class="header-icon">
                <el-icon size="24" color="#667eea">
                  <ShoppingBag />
                </el-icon>
              </div>
              <div class="header-content">
                <h3>选品决策</h3>
                <p>智能商品分析与推荐系统</p>
              </div>
            </div>

            <div class="feature-content">
              <div class="feature-actions">
                <el-button type="primary" @click="$router.push('/products')">
                  <el-icon><View /></el-icon>
                  商品管理
                </el-button>
                <el-button @click="$router.push('/products/analysis')">
                  <el-icon><DataAnalysis /></el-icon>
                  商品分析
                </el-button>
                <el-button @click="$router.push('/products/recommendation')">
                  <el-icon><Star /></el-icon>
                  智能推荐
                </el-button>
                <el-button @click="$router.push('/products/bigdata-analysis')">
                  <el-icon><DataAnalysis /></el-icon>
                  大数据分析
                </el-button>
              </div>

              <!-- 热门商品 -->
              <div class="hot-products" v-if="dashboardData?.hot_products">
                <h4>热门商品</h4>
                <div class="product-list">
                  <div
                    v-for="product in dashboardData.hot_products"
                    :key="product.id"
                    class="product-item"
                  >
                    <span class="product-name">{{ product.name }}</span>
                    <span class="product-score">{{ product.score?.toFixed(2) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>

        <el-col :span="12">
          <div class="feature-card">
            <div class="card-header">
              <div class="header-icon">
                <el-icon size="24" color="#667eea">
                  <Van />
                </el-icon>
              </div>
              <div class="header-content">
                <h3>物流配送</h3>
                <p>智能路线规划与成本优化</p>
              </div>
            </div>

            <div class="feature-content">
              <div class="feature-actions">
                <el-button type="primary" @click="$router.push('/logistics')">
                  <el-icon><View /></el-icon>
                  物流总览
                </el-button>
                <el-button @click="$router.push('/logistics/routes')">
                  <el-icon><MapLocation /></el-icon>
                  配送路线
                </el-button>
                <el-button @click="$router.push('/logistics/cost-calculator')">
                  <el-icon><Tools /></el-icon>
                  成本计算
                </el-button>
              </div>

              <!-- 配送统计 -->
              <div class="delivery-stats" v-if="logisticsData">
                <h4>配送统计</h4>
                <div class="stats-grid">
                  <div class="stat-item">
                    <span class="label">准时率</span>
                    <span class="value">{{ logisticsData.on_time_rate || 0 }}%</span>
                  </div>
                  <div class="stat-item">
                    <span class="label">平均成本</span>
                    <span class="value">¥{{ logisticsData.average_cost || 0 }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions-section">
      <div class="quick-actions">
        <div class="section-header">
          <div class="header-icon">
            <el-icon size="20" color="#667eea">
              <Lightning />
            </el-icon>
          </div>
          <h3>快速操作</h3>
        </div>

        <el-row :gutter="16" class="quick-buttons">
          <el-col :span="6">
            <div class="quick-btn" @click="$router.push('/products/archive')">
              <div class="btn-icon">
                <el-icon size="24" color="#667eea"><Files /></el-icon>
              </div>
              <span>商品档案</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="quick-btn" @click="$router.push('/sales/orders')">
              <div class="btn-icon">
                <el-icon size="24" color="#667eea"><Sell /></el-icon>
              </div>
              <span>销售订单</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="quick-btn" @click="$router.push('/inventory/query')">
              <div class="btn-icon">
                <el-icon size="24" color="#667eea"><Search /></el-icon>
              </div>
              <span>库存查询</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="quick-btn" @click="$router.push('/inventory/outbound')">
              <div class="btn-icon">
                <el-icon size="24" color="#667eea"><Upload /></el-icon>
              </div>
              <span>销售出库单</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useProductStore } from '../stores/products'
import { useLogisticsStore } from '../stores/logistics'
import { useAuthStore } from '../stores/auth'
import {
  Plus,
  Box,
  TrendCharts,
  Van,
  DataAnalysis,
  ShoppingBag,
  View,
  Star,
  MapLocation,
  Lightning,
  Tools,
  Files,
  Search,
  Sell,
  Upload
} from '@element-plus/icons-vue'

const productStore = useProductStore()
const logisticsStore = useLogisticsStore()
const authStore = useAuthStore()

const dashboardData = ref<any>(null)
const logisticsData = ref<any>(null)

const currentDate = computed(() => {
  const now = new Date()
  return now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

onMounted(async () => {
  // 加载仪表板数据
  dashboardData.value = await productStore.fetchDashboard()
  logisticsData.value = await logisticsStore.fetchPerformanceData()
})
</script>

<style scoped>
.home-view {
  padding: 0;
  background: transparent;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 主要内容容器 */
.main-container {
  flex: 1;
  padding: 30px 30px 30px 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* 主容器滚动条样式 */
.main-container::-webkit-scrollbar {
  width: 6px;
}

.main-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.main-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.main-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 欢迎区域 */
.welcome-section {
  flex-shrink: 0;
}

.welcome-content {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h1 {
  font-size: 2.2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-text p {
  font-size: 1.1rem;
  color: #7f8c8d;
  margin: 0;
}

.welcome-actions {
  display: flex;
  gap: 16px;
}

.welcome-actions .el-button {
  height: 48px;
  padding: 0 24px;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.welcome-actions .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.welcome-actions .el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.welcome-actions .el-button:not(.el-button--primary) {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(102, 126, 234, 0.2);
  color: #667eea;
}

.welcome-actions .el-button:not(.el-button--primary):hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  transform: translateY(-2px);
}

/* 统计区域 */
.stats-section {
  flex-shrink: 0;
}

.stats-cards {
  margin-bottom: 0;
}

.stat-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.stat-icon {
  margin-right: 16px;
  padding: 12px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
}

.stat-value {
  font-size: 2.2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-label {
  color: #7f8c8d;
  font-size: 0.9rem;
  font-weight: 500;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 6px;
}

.trend-text {
  font-size: 0.85rem;
  font-weight: 600;
  color: #67C23A;
}

/* 功能模块区域 */
.modules-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.feature-modules {
  flex: 1;
  margin-bottom: 0;
}

.feature-modules .el-row {
  height: 100%;
}

.feature-modules .el-col {
  height: 100%;
}

.feature-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
  flex-shrink: 0;
}

.header-icon {
  padding: 12px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
}

.header-content h3 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.header-content p {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin: 0;
}

.feature-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.feature-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex-wrap: wrap;
  flex-shrink: 0;
}

.feature-actions .el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.feature-actions .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.feature-actions .el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.feature-actions .el-button:not(.el-button--primary) {
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  color: #667eea;
}

.feature-actions .el-button:not(.el-button--primary):hover {
  background: rgba(102, 126, 234, 0.15);
  border-color: #667eea;
  transform: translateY(-2px);
}

.hot-products, .delivery-stats {
  margin-top: 20px;
}

.hot-products h4, .delivery-stats h4 {
  margin-bottom: 12px;
  color: #2c3e50;
  font-weight: 600;
  font-size: 1rem;
}

.product-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-item {
  display: flex;
  justify-content: space-between;
  padding: 12px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

.product-item:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateX(4px);
}

.product-name {
  color: #2c3e50;
  font-weight: 500;
}

.product-score {
  color: #667eea;
  font-weight: 700;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 12px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.stat-item .label {
  color: #7f8c8d;
  font-weight: 500;
}

.stat-item .value {
  color: #2c3e50;
  font-weight: 700;
}

/* 快速操作区域 */
.quick-actions-section {
  flex-shrink: 0;
}

.quick-actions {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

.section-header .header-icon {
  padding: 8px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
}

.section-header h3 {
  font-size: 1.2rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
}

.quick-buttons {
  margin: 0;
}

.quick-btn {
  background: rgba(102, 126, 234, 0.05);
  border: 1px solid rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.quick-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.btn-icon {
  padding: 12px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.quick-btn:hover .btn-icon {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(1.1);
}

.quick-btn span {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .welcome-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .welcome-actions {
    justify-content: center;
  }

  .main-container {
    padding: 20px 20px 20px 0;
  }
}

@media (max-width: 768px) {
  .main-container {
    padding: 15px 15px 15px 0;
    gap: 20px;
  }

  .welcome-text h1 {
    font-size: 1.8rem;
  }

  .stats-cards .el-col {
    margin-bottom: 16px;
  }

  .feature-modules .el-col {
    margin-bottom: 20px;
    height: auto;
  }

  .feature-card {
    height: auto;
  }

  .feature-actions {
    flex-direction: column;
  }

  .feature-actions .el-button {
    width: 100%;
  }

  .quick-buttons .el-col {
    margin-bottom: 12px;
  }

  .modules-section {
    flex: none;
  }
}

@media (max-width: 480px) {
  .main-container {
    padding: 10px 10px 10px 0;
    gap: 15px;
  }

  .welcome-content {
    padding: 20px;
  }

  .welcome-text h1 {
    font-size: 1.5rem;
  }

  .welcome-actions {
    flex-direction: column;
    width: 100%;
  }

  .welcome-actions .el-button {
    width: 100%;
  }

  .stat-card {
    padding: 16px;
  }

  .feature-card {
    padding: 16px;
  }

  .quick-actions {
    padding: 16px;
  }
}
</style>
