<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <!-- 404动画图标 -->
      <div class="error-animation">
        <div class="error-number">
          <span class="four">4</span>
          <span class="zero">0</span>
          <span class="four">4</span>
        </div>
        <div class="floating-elements">
          <div class="element element-1"></div>
          <div class="element element-2"></div>
          <div class="element element-3"></div>
        </div>
      </div>
      
      <!-- 错误信息 -->
      <div class="error-info">
        <h1 class="error-title">页面未找到</h1>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移除。
          <br>
          请检查URL是否正确，或返回首页继续浏览。
        </p>
      </div>
      
      <!-- 操作按钮 -->
      <div class="error-actions">
        <el-button type="primary" size="large" @click="goHome">
          <el-icon><HomeFilled /></el-icon>
          返回首页
        </el-button>
        <el-button size="large" @click="goBack">
          <el-icon><Back /></el-icon>
          返回上页
        </el-button>
      </div>
      
      <!-- 快速导航 -->
      <div class="quick-nav">
        <h3>您可能想要访问：</h3>
        <div class="nav-links">
          <el-link
            type="primary"
            underline="never"
            @click="$router.push('/products')"
            class="nav-link"
          >
            <el-icon><Box /></el-icon>
            商品管理
          </el-link>

          <el-link
            type="primary"
            underline="never"
            @click="$router.push('/products/analysis')"
            class="nav-link"
          >
            <el-icon><TrendCharts /></el-icon>
            商品分析
          </el-link>

          <el-link
            type="primary"
            underline="never"
            @click="$router.push('/logistics')"
            class="nav-link"
          >
            <el-icon><Van /></el-icon>
            物流配送
          </el-link>

          <el-link
            type="primary"
            underline="never"
            @click="$router.push('/logistics/cost-calculator')"
            class="nav-link"
          >
            <el-icon><Calculator /></el-icon>
            成本计算
          </el-link>
        </div>
      </div>
    </div>
    
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="bg-shape bg-shape-1"></div>
      <div class="bg-shape bg-shape-2"></div>
      <div class="bg-shape bg-shape-3"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>

<style scoped>
.not-found-container {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.not-found-content {
  position: relative;
  z-index: 10;
  text-align: center;
  max-width: 600px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

/* 404动画 */
.error-animation {
  position: relative;
  margin-bottom: 40px;
}

.error-number {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.error-number span {
  font-size: 8rem;
  font-weight: 900;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: bounce 2s ease-in-out infinite;
}

.error-number .zero {
  animation-delay: 0.2s;
}

.error-number .four:last-child {
  animation-delay: 0.4s;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.element {
  position: absolute;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.3);
  animation: float 3s ease-in-out infinite;
}

.element-1 {
  width: 30px;
  height: 30px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.element-2 {
  width: 20px;
  height: 20px;
  top: 60%;
  right: 15%;
  animation-delay: 1s;
}

.element-3 {
  width: 25px;
  height: 25px;
  bottom: 30%;
  left: 20%;
  animation-delay: 2s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(180deg);
  }
}

/* 错误信息 */
.error-info {
  margin-bottom: 40px;
}

.error-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 16px;
}

.error-description {
  font-size: 1.1rem;
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 0;
}

/* 操作按钮 */
.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 40px;
}

.error-actions .el-button {
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 600;
}

/* 快速导航 */
.quick-nav {
  border-top: 1px solid #e1e8ed;
  padding-top: 30px;
}

.quick-nav h3 {
  font-size: 1.2rem;
  color: #2c3e50;
  margin-bottom: 20px;
}

.nav-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 16px;
}

.nav-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
  text-decoration: none !important;
}

.nav-link:hover {
  background: #e3f2fd;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.nav-link .el-icon {
  font-size: 24px;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.bg-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float-bg 8s ease-in-out infinite;
}

.bg-shape-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.bg-shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.bg-shape-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float-bg {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
    opacity: 0.6;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .not-found-content {
    margin: 20px;
    padding: 30px 20px;
  }
  
  .error-number span {
    font-size: 5rem;
  }
  
  .error-title {
    font-size: 2rem;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-actions .el-button {
    width: 200px;
  }
  
  .nav-links {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .error-number {
    gap: 10px;
  }
  
  .error-number span {
    font-size: 4rem;
  }
  
  .nav-links {
    grid-template-columns: 1fr;
  }
}
</style>
