"""
客户管理API
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.schemas.customer import (
    Customer, CustomerCreate, CustomerUpdate, CustomerListResponse, 
    CustomerQuery, CustomerStats, CustomerType, CustomerLevel, CustomerStatus
)
from app.services.customer_service import CustomerService
import math

router = APIRouter()


@router.get("/", response_model=CustomerListResponse)
async def get_customers(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    name: str = Query(None, description="客户名称"),
    code: str = Query(None, description="客户编码"),
    customer_type: CustomerType = Query(None, description="客户类型"),
    level: CustomerLevel = Query(None, description="客户等级"),
    status: CustomerStatus = Query(None, description="客户状态"),
    phone: str = Query(None, description="联系电话"),
    email: str = Query(None, description="邮箱"),
    address: str = Query(None, description="地址"),
    db: Session = Depends(get_db)
):
    """获取客户列表"""
    
    service = CustomerService(db)
    
    # 构建查询参数
    query = CustomerQuery(
        name=name,
        code=code,
        customer_type=customer_type,
        level=level,
        status=status,
        phone=phone,
        email=email,
        address=address
    )
    
    # 计算偏移量
    skip = (page - 1) * page_size
    
    # 获取数据
    customers, total = service.get_customers(skip=skip, limit=page_size, query=query)
    
    # 计算总页数
    total_pages = math.ceil(total / page_size) if total > 0 else 1
    
    return CustomerListResponse(
        items=customers,
        total=total,
        page=page,
        page_size=page_size,
        total_pages=total_pages
    )


@router.get("/stats", response_model=CustomerStats)
async def get_customer_stats(db: Session = Depends(get_db)):
    """获取客户统计信息"""
    service = CustomerService(db)
    return service.get_customer_stats()


@router.get("/{customer_id}", response_model=Customer)
async def get_customer(customer_id: int, db: Session = Depends(get_db)):
    """获取单个客户详情"""
    service = CustomerService(db)
    customer = service.get_customer(customer_id)
    if not customer:
        raise HTTPException(status_code=404, detail="客户不存在")
    return customer


@router.post("/", response_model=Customer)
async def create_customer(customer_data: CustomerCreate, db: Session = Depends(get_db)):
    """创建客户"""
    service = CustomerService(db)
    try:
        return service.create_customer(customer_data)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{customer_id}", response_model=Customer)
async def update_customer(
    customer_id: int, 
    customer_data: CustomerUpdate, 
    db: Session = Depends(get_db)
):
    """更新客户"""
    service = CustomerService(db)
    try:
        customer = service.update_customer(customer_id, customer_data)
        if not customer:
            raise HTTPException(status_code=404, detail="客户不存在")
        return customer
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/{customer_id}")
async def delete_customer(customer_id: int, db: Session = Depends(get_db)):
    """删除客户"""
    service = CustomerService(db)
    success = service.delete_customer(customer_id)
    if not success:
        raise HTTPException(status_code=404, detail="客户不存在")
    return {"message": "客户删除成功"}
