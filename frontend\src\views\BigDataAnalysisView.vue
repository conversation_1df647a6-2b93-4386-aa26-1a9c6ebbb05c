<template>
  <div class="bigdata-analysis-view">
    <div class="page-header">
      <h2>大数据选品决策</h2>
      <p class="page-description">基于多平台数据源的智能选品分析系统</p>
    </div>

    <!-- 数据源概览 -->
    <el-row :gutter="20" class="data-source-overview">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon">
              <el-icon size="32" color="#409EFF"><Coin /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ dataSourceStats.total_sources || 0 }}</div>
              <div class="overview-label">数据源总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon">
              <el-icon size="32" color="#67C23A"><Box /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ dataSourceStats.total_products || 0 }}</div>
              <div class="overview-label">商品数据量</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon">
              <el-icon size="32" color="#E6A23C"><Refresh /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ dataSourceStats.last_sync || '未同步' }}</div>
              <div class="overview-label">最后同步</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon">
              <el-icon size="32" color="#F56C6C"><TrendCharts /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ dataSourceStats.analysis_ready || 0 }}</div>
              <div class="overview-label">可分析商品</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据源管理 -->
    <el-card class="data-sources-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <el-icon><DataBoard /></el-icon>
            <span>数据源管理</span>
          </div>
          <div class="header-right">
            <el-button type="primary" @click="showAddDataSourceDialog = true">
              <el-icon><Plus /></el-icon>
              添加数据源
            </el-button>
            <el-button @click="syncAllDataSources" :loading="syncing">
              <el-icon><Refresh /></el-icon>
              同步所有数据源
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="dataSources" style="width: 100%">
        <el-table-column prop="name" label="数据源名称" width="200">
          <template #default="{ row }">
            <div class="data-source-name">
              <el-icon :color="getSourceTypeColor(row.type)">
                <component :is="getSourceTypeIcon(row.type)" />
              </el-icon>
              <span>{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="type" label="平台类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getSourceTypeTagType(row.type)">
              {{ getSourceTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '活跃' : '离线' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="product_count" label="商品数量" width="120">
          <template #default="{ row }">
            <span>{{ row.product_count?.toLocaleString() || 0 }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="last_sync" label="最后同步" width="180">
          <template #default="{ row }">
            <span>{{ formatDateTime(row.last_sync) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="sync_frequency" label="同步频率" width="120">
          <template #default="{ row }">
            <span>{{ getSyncFrequencyLabel(row.sync_frequency) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="syncDataSource(row.id)" :loading="row.syncing">
              同步
            </el-button>
            <el-button size="small" type="primary" @click="viewDataSourceDetails(row)">
              详情
            </el-button>
            <el-button size="small" type="danger" @click="deleteDataSource(row.id)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 智能分析配置 -->
    <el-card class="analysis-config-card">
      <template #header>
        <div class="card-header">
          <el-icon><DataAnalysis /></el-icon>
          <span>智能分析配置</span>
        </div>
      </template>
      
      <el-form :model="analysisConfig" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="分析维度">
              <el-select v-model="analysisConfig.dimensions" multiple placeholder="选择分析维度">
                <el-option label="价格趋势" value="price_trend" />
                <el-option label="销量分析" value="sales_analysis" />
                <el-option label="竞争分析" value="competition" />
                <el-option label="市场需求" value="market_demand" />
                <el-option label="季节性分析" value="seasonal" />
                <el-option label="用户评价" value="user_rating" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="商品类别">
              <el-select v-model="analysisConfig.categories" multiple placeholder="选择商品类别">
                <el-option label="电子产品" value="electronics" />
                <el-option label="服装鞋帽" value="clothing" />
                <el-option label="家居用品" value="home" />
                <el-option label="美妆护肤" value="beauty" />
                <el-option label="食品饮料" value="food" />
                <el-option label="运动户外" value="sports" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="价格区间">
              <el-input-number
                v-model="analysisConfig.price_min"
                :min="0"
                placeholder="最低价格"
                style="width: 45%"
              />
              <span style="margin: 0 8px">-</span>
              <el-input-number
                v-model="analysisConfig.price_max"
                :min="0"
                placeholder="最高价格"
                style="width: 45%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="analysisConfig.date_range"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY年MM月DD日"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="数据源">
              <el-select v-model="analysisConfig.data_sources" multiple placeholder="选择数据源">
                <el-option
                  v-for="source in dataSources.filter(s => s.status === 'active')"
                  :key="source.id"
                  :label="source.name"
                  :value="source.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="">
              <el-button 
                type="primary" 
                size="large"
                @click="startBigDataAnalysis"
                :loading="analyzing"
                :disabled="!canStartAnalysis"
              >
                <el-icon><DataAnalysis /></el-icon>
                开始大数据分析
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 添加数据源对话框 -->
    <el-dialog
      v-model="showAddDataSourceDialog"
      title="添加数据源"
      width="600px"
    >
      <el-form :model="newDataSource" label-width="100px">
        <el-form-item label="数据源名称" required>
          <el-input v-model="newDataSource.name" placeholder="请输入数据源名称" />
        </el-form-item>
        
        <el-form-item label="平台类型" required>
          <el-select v-model="newDataSource.type" placeholder="选择平台类型">
            <el-option label="淘宝" value="taobao" />
            <el-option label="天猫" value="tmall" />
            <el-option label="京东" value="jd" />
            <el-option label="拼多多" value="pdd" />
            <el-option label="抖音电商" value="douyin" />
            <el-option label="小红书" value="xiaohongshu" />
            <el-option label="亚马逊" value="amazon" />
            <el-option label="沃尔玛" value="walmart" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="API地址">
          <el-input v-model="newDataSource.api_url" placeholder="请输入API地址" />
        </el-form-item>
        
        <el-form-item label="同步频率">
          <el-select v-model="newDataSource.sync_frequency">
            <el-option label="实时" value="realtime" />
            <el-option label="每小时" value="hourly" />
            <el-option label="每日" value="daily" />
            <el-option label="每周" value="weekly" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDataSourceDialog = false">取消</el-button>
        <el-button type="primary" @click="addDataSource">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Coin,
  Box,
  Refresh,
  TrendCharts,
  DataBoard,
  Plus,
  DataAnalysis,
  ShoppingBag,
  Van,
  Monitor,
  Phone,
  VideoCamera
} from '@element-plus/icons-vue'

// 类型定义
interface DataSource {
  id: number
  name: string
  type: string
  status: 'active' | 'inactive'
  product_count: number
  last_sync: string | null
  sync_frequency: string
  syncing: boolean
}

interface DataSourceStats {
  total_sources: number
  total_products: number
  last_sync: string
  analysis_ready: number
}

interface AnalysisConfig {
  dimensions: string[]
  categories: string[]
  price_min: number | null
  price_max: number | null
  date_range: [Date, Date] | null
  data_sources: number[]
}

interface NewDataSource {
  name: string
  type: string
  api_url: string
  sync_frequency: string
}

// 响应式数据
const dataSourceStats = ref<DataSourceStats>({
  total_sources: 0,
  total_products: 0,
  last_sync: '',
  analysis_ready: 0
})

const dataSources = ref<DataSource[]>([])
const showAddDataSourceDialog = ref(false)
const syncing = ref(false)
const analyzing = ref(false)

const analysisConfig = ref<AnalysisConfig>({
  dimensions: [],
  categories: [],
  price_min: null,
  price_max: null,
  date_range: null,
  data_sources: []
})

const newDataSource = ref<NewDataSource>({
  name: '',
  type: '',
  api_url: '',
  sync_frequency: 'daily'
})

// 计算属性
const canStartAnalysis = computed(() => {
  return analysisConfig.value.dimensions.length > 0 && 
         analysisConfig.value.data_sources.length > 0
})

// 方法
const fetchDataSources = async () => {
  // 模拟数据
  dataSources.value = [
    {
      id: 1,
      name: '淘宝商品数据',
      type: 'taobao',
      status: 'active',
      product_count: 1250000,
      last_sync: '2024-07-18 14:30:00',
      sync_frequency: 'hourly',
      syncing: false
    },
    {
      id: 2,
      name: '京东商品数据',
      type: 'jd',
      status: 'active',
      product_count: 890000,
      last_sync: '2024-07-18 14:25:00',
      sync_frequency: 'hourly',
      syncing: false
    },
    {
      id: 3,
      name: '拼多多商品数据',
      type: 'pdd',
      status: 'active',
      product_count: 2100000,
      last_sync: '2024-07-18 14:20:00',
      sync_frequency: 'daily',
      syncing: false
    }
  ]
  
  // 更新统计数据
  dataSourceStats.value = {
    total_sources: dataSources.value.length,
    total_products: dataSources.value.reduce((sum, source) => sum + (source.product_count || 0), 0),
    last_sync: '2024-07-18 14:30:00',
    analysis_ready: dataSources.value.reduce((sum, source) => sum + (source.product_count || 0), 0)
  }
}

const getSourceTypeIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    'taobao': ShoppingBag,
    'tmall': ShoppingBag,
    'jd': Box,
    'pdd': Van,
    'douyin': VideoCamera,
    'xiaohongshu': Phone,
    'amazon': Monitor,
    'walmart': Coin,
    'other': Coin
  }
  return iconMap[type] || Coin
}

const getSourceTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'taobao': '#FF6900',
    'tmall': '#FF0036',
    'jd': '#E1251B',
    'pdd': '#E02E24',
    'douyin': '#000000',
    'xiaohongshu': '#FF2442',
    'amazon': '#FF9900',
    'walmart': '#004C91',
    'other': '#909399'
  }
  return colorMap[type] || '#909399'
}

const getSourceTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    'taobao': '淘宝',
    'tmall': '天猫',
    'jd': '京东',
    'pdd': '拼多多',
    'douyin': '抖音',
    'xiaohongshu': '小红书',
    'amazon': '亚马逊',
    'walmart': '沃尔玛',
    'other': '其他'
  }
  return labelMap[type] || type
}

const getSourceTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    'taobao': 'warning',
    'tmall': 'danger',
    'jd': 'danger',
    'pdd': 'danger',
    'douyin': '',
    'xiaohongshu': 'danger',
    'amazon': 'warning',
    'walmart': 'primary',
    'other': 'info'
  }
  return typeMap[type] || 'info'
}

const getSyncFrequencyLabel = (frequency: string) => {
  const labelMap: Record<string, string> = {
    'realtime': '实时',
    'hourly': '每小时',
    'daily': '每日',
    'weekly': '每周'
  }
  return labelMap[frequency] || frequency
}

const formatDateTime = (dateTime: string | null) => {
  if (!dateTime) return '未同步'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const syncDataSource = async (sourceId: number) => {
  const source = dataSources.value.find(s => s.id === sourceId)
  if (source) {
    source.syncing = true
    try {
      // 模拟同步过程
      await new Promise(resolve => setTimeout(resolve, 2000))
      source.last_sync = new Date().toISOString().replace('T', ' ').substring(0, 19)
      ElMessage.success('数据源同步成功')
    } catch (error) {
      ElMessage.error('数据源同步失败')
    } finally {
      source.syncing = false
    }
  }
}

const syncAllDataSources = async () => {
  syncing.value = true
  try {
    // 模拟同步所有数据源
    await new Promise(resolve => setTimeout(resolve, 3000))
    dataSources.value.forEach(source => {
      source.last_sync = new Date().toISOString().replace('T', ' ').substring(0, 19)
    })
    ElMessage.success('所有数据源同步成功')
  } catch (error) {
    ElMessage.error('数据源同步失败')
  } finally {
    syncing.value = false
  }
}

const addDataSource = async () => {
  if (!newDataSource.value.name || !newDataSource.value.type) {
    ElMessage.warning('请填写必要信息')
    return
  }

  try {
    // 模拟添加数据源
    const newSource: DataSource = {
      id: Date.now(),
      ...newDataSource.value,
      status: 'active' as const,
      product_count: 0,
      last_sync: null,
      syncing: false
    }

    dataSources.value.push(newSource)
    showAddDataSourceDialog.value = false

    // 重置表单
    newDataSource.value = {
      name: '',
      type: '',
      api_url: '',
      sync_frequency: 'daily'
    }

    ElMessage.success('数据源添加成功')
  } catch (error) {
    ElMessage.error('数据源添加失败')
  }
}

const deleteDataSource = async (sourceId: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个数据源吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const index = dataSources.value.findIndex(s => s.id === sourceId)
    if (index > -1) {
      dataSources.value.splice(index, 1)
      ElMessage.success('数据源删除成功')
    }
  } catch (error) {
    // 用户取消删除
  }
}

const viewDataSourceDetails = (source: DataSource) => {
  ElMessage.info(`查看 ${source.name} 的详细信息`)
}

const startBigDataAnalysis = async () => {
  analyzing.value = true
  try {
    // 模拟大数据分析过程
    ElMessage.info('正在启动大数据分析引擎...')
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.info('正在收集多平台数据...')
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.info('正在进行智能分析...')
    await new Promise(resolve => setTimeout(resolve, 3000))

    ElMessage.success('大数据分析完成！正在跳转到结果页面...')

    // 跳转到分析结果页面
    setTimeout(() => {
      window.location.href = '/products/bigdata-results'
    }, 1000)

  } catch (error) {
    ElMessage.error('大数据分析失败')
  } finally {
    analyzing.value = false
  }
}

onMounted(() => {
  fetchDataSources()
})
</script>

<style scoped>
.bigdata-analysis-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
}

.page-description {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px;
}

/* 数据源概览 */
.data-source-overview {
  margin-bottom: 24px;
}

.overview-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.overview-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.overview-icon {
  padding: 12px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 12px;
  flex-shrink: 0;
}

.overview-info {
  flex: 1;
}

.overview-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 14px;
  color: #7f8c8d;
}

/* 卡片样式 */
.data-sources-card,
.analysis-config-card {
  margin-bottom: 24px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  color: #2c3e50;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 数据源名称 */
.data-source-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 表格样式 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

/* 按钮样式 */
.el-button {
  border-radius: 8px;
  font-weight: 500;
}

.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 20px;
}

.el-select,
.el-input,
.el-date-picker {
  width: 100%;
}

/* 对话框样式 */
.el-dialog {
  border-radius: 12px;
}

.el-dialog__header {
  padding: 20px 20px 10px;
}

.el-dialog__body {
  padding: 10px 20px 20px;
}

/* 标签样式 */
.el-tag {
  border-radius: 6px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .data-source-overview .el-col {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .bigdata-analysis-view {
    padding: 16px;
  }

  .page-header h2 {
    font-size: 24px;
  }

  .overview-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-right {
    width: 100%;
    justify-content: flex-start;
  }
}
</style>
