from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

from app.core.database import get_db
from app.models.warehouse import Warehouse
from app.models.inventory import Inventory
from app.schemas.warehouse import (
    WarehouseResponse,
    WarehouseCreate,
    WarehouseUpdate,
    WarehouseQuery,
    WarehouseStats
)
from app.services.warehouse_service import WarehouseService
from sqlalchemy import func, text

router = APIRouter()


def calculate_warehouse_metrics(warehouse: Warehouse, db: Session) -> dict:
    """计算仓库指标"""
    # 计算可用容量
    available_capacity = warehouse.total_capacity_m3 - warehouse.used_capacity_m3

    # 计算利用率
    utilization_rate = (warehouse.used_capacity_m3 / warehouse.total_capacity_m3 * 100) if warehouse.total_capacity_m3 > 0 else 0

    # 计算库存商品总数量（所有商品的当前库存之和）
    total_inventory_quantity = db.query(func.sum(Inventory.current_stock)).filter(
        Inventory.warehouse_id == warehouse.id,
        Inventory.is_active == True
    ).scalar() or 0

    # 计算库存商品种类数量（有多少个不同的商品SKU）
    inventory_sku_count = db.query(Inventory).filter(
        Inventory.warehouse_id == warehouse.id,
        Inventory.is_active == True
    ).count()

    # 计算可用库存总数量
    total_available_stock = db.query(func.sum(Inventory.available_stock)).filter(
        Inventory.warehouse_id == warehouse.id,
        Inventory.is_active == True
    ).scalar() or 0

    # 计算预留库存总数量
    total_reserved_stock = db.query(func.sum(Inventory.reserved_stock)).filter(
        Inventory.warehouse_id == warehouse.id,
        Inventory.is_active == True
    ).scalar() or 0

    return {
        "available_capacity_m3": available_capacity,
        "utilization_rate": round(utilization_rate, 2),
        "inventory_count": int(total_inventory_quantity),  # 库存商品总数量
        "inventory_sku_count": inventory_sku_count,        # 库存商品种类数量
        "available_stock": int(total_available_stock),     # 可用库存总数量
        "reserved_stock": int(total_reserved_stock)        # 预留库存总数量
    }


@router.get("/", response_model=List[WarehouseResponse])
def get_warehouses(
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    name: Optional[str] = Query(None, description="仓库名称"),
    status: Optional[str] = Query(None, description="状态"),
    is_active: Optional[bool] = Query(None, description="是否启用"),
    db: Session = Depends(get_db)
):
    """获取仓库列表"""
    query = db.query(Warehouse)

    # 应用筛选条件
    if name:
        query = query.filter(Warehouse.name.contains(name))
    if status:
        query = query.filter(Warehouse.status == status)
    if is_active is not None:
        query = query.filter(Warehouse.is_active == is_active)

    # 分页
    warehouses = query.offset(skip).limit(limit).all()

    # 构建响应数据
    result = []
    for warehouse in warehouses:
        try:
            metrics = calculate_warehouse_metrics(warehouse, db)
            warehouse_dict = warehouse.__dict__.copy()
            warehouse_dict.update(metrics)

            # 确保必需字段有默认值
            if warehouse_dict.get('is_active') is None:
                warehouse_dict['is_active'] = True
            if warehouse_dict.get('status') is None:
                warehouse_dict['status'] = 'active'

            result.append(WarehouseResponse.model_validate(warehouse_dict))
        except Exception as e:
            # 跳过有问题的仓库记录
            continue

    return result


@router.get("/{warehouse_id}", response_model=WarehouseResponse)
def get_warehouse(warehouse_id: int, db: Session = Depends(get_db)):
    """获取仓库详情"""
    warehouse = db.query(Warehouse).filter(Warehouse.id == warehouse_id).first()
    if not warehouse:
        raise HTTPException(status_code=404, detail="仓库不存在")
    
    metrics = calculate_warehouse_metrics(warehouse, db)
    warehouse_dict = warehouse.__dict__.copy()
    warehouse_dict.update(metrics)
    
    return WarehouseResponse.model_validate(warehouse_dict)


@router.post("/", response_model=WarehouseResponse)
def create_warehouse(warehouse_data: WarehouseCreate, db: Session = Depends(get_db)):
    """创建仓库"""
    # 检查仓库名称是否已存在
    existing = db.query(Warehouse).filter(Warehouse.name == warehouse_data.name).first()
    if existing:
        raise HTTPException(status_code=400, detail="仓库名称已存在")
    
    # 创建仓库
    db_warehouse = Warehouse(
        name=warehouse_data.name,
        address=warehouse_data.address,
        total_capacity_m3=warehouse_data.total_capacity_m3,
        used_capacity_m3=warehouse_data.used_capacity_m3,
        latitude=warehouse_data.latitude,
        longitude=warehouse_data.longitude,
        operating_hours=warehouse_data.operating_hours,
        contact_info=warehouse_data.contact_info,
        is_active=warehouse_data.is_active,
        status=warehouse_data.status
    )
    
    db.add(db_warehouse)
    db.commit()
    db.refresh(db_warehouse)
    
    metrics = calculate_warehouse_metrics(db_warehouse, db)
    warehouse_dict = db_warehouse.__dict__.copy()
    warehouse_dict.update(metrics)
    
    return WarehouseResponse.model_validate(warehouse_dict)


@router.put("/{warehouse_id}", response_model=WarehouseResponse)
def update_warehouse(
    warehouse_id: int, 
    warehouse_data: WarehouseUpdate, 
    db: Session = Depends(get_db)
):
    """更新仓库"""
    db_warehouse = db.query(Warehouse).filter(Warehouse.id == warehouse_id).first()
    if not db_warehouse:
        raise HTTPException(status_code=404, detail="仓库不存在")
    
    # 检查名称是否与其他仓库冲突
    if warehouse_data.name and warehouse_data.name != db_warehouse.name:
        existing = db.query(Warehouse).filter(
            Warehouse.name == warehouse_data.name,
            Warehouse.id != warehouse_id
        ).first()
        if existing:
            raise HTTPException(status_code=400, detail="仓库名称已存在")
    
    # 更新字段
    update_data = warehouse_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_warehouse, field, value)
    
    db_warehouse.updated_at = datetime.now()
    db.commit()
    db.refresh(db_warehouse)
    
    metrics = calculate_warehouse_metrics(db_warehouse, db)
    warehouse_dict = db_warehouse.__dict__.copy()
    warehouse_dict.update(metrics)
    
    return WarehouseResponse.model_validate(warehouse_dict)


@router.delete("/{warehouse_id}")
def delete_warehouse(warehouse_id: int, db: Session = Depends(get_db)):
    """删除仓库

    删除规则：
    - 如果仓库没有任何出入库记录，可以直接删除
    - 如果仓库有出入库记录，不能删除，只能停用
    """
    db_warehouse = db.query(Warehouse).filter(Warehouse.id == warehouse_id).first()
    if not db_warehouse:
        raise HTTPException(status_code=404, detail="仓库不存在")

    # 检查所有出入库相关记录
    blocking_records = []

    # 1. 检查库存记录（有库存说明有过入库）
    inventory_count = db.query(Inventory).filter(Inventory.warehouse_id == warehouse_id).count()
    if inventory_count > 0:
        blocking_records.append(f"库存记录 {inventory_count} 条")

    # 2. 检查采购入库记录
    try:
        from app.models.purchase import PurchaseReceipt
        purchase_count = db.query(PurchaseReceipt).filter(PurchaseReceipt.warehouse_id == warehouse_id).count()
        if purchase_count > 0:
            blocking_records.append(f"采购入库记录 {purchase_count} 条")
    except Exception:
        pass

    # 3. 检查库存调拨记录
    try:
        from app.models.inventory import InventoryTransfer
        outbound_count = db.query(InventoryTransfer).filter(InventoryTransfer.from_warehouse_id == warehouse_id).count()
        inbound_count = db.query(InventoryTransfer).filter(InventoryTransfer.to_warehouse_id == warehouse_id).count()
        if outbound_count > 0:
            blocking_records.append(f"调出记录 {outbound_count} 条")
        if inbound_count > 0:
            blocking_records.append(f"调入记录 {inbound_count} 条")
    except Exception:
        pass

    # 4. 检查销售出库记录（如果表存在）
    try:
        # 使用原生SQL检查，避免导入不存在的模型
        result = db.execute(text("SELECT COUNT(*) FROM sales_outbound WHERE warehouse_id = :warehouse_id"),
                          {"warehouse_id": warehouse_id})
        sales_count = result.scalar()
        if sales_count > 0:
            blocking_records.append(f"销售出库记录 {sales_count} 条")
    except Exception:
        pass

    # 如果有出入库记录，不能删除
    if blocking_records:
        records_text = "、".join(blocking_records)
        detail = (f"该仓库存在历史出入库记录（{records_text}），为保证数据完整性不能删除。"
                 f"如需停止使用此仓库，请使用'停用'功能。")
        raise HTTPException(status_code=400, detail=detail)

    # 没有出入库记录，可以安全删除
    try:
        db.execute(text("DELETE FROM warehouses WHERE id = :warehouse_id"), {"warehouse_id": warehouse_id})
        db.commit()
        return {"message": "仓库删除成功"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除仓库失败: {str(e)}")


@router.post("/{warehouse_id}/activate")
def activate_warehouse(warehouse_id: int, db: Session = Depends(get_db)):
    """启用仓库"""
    db_warehouse = db.query(Warehouse).filter(Warehouse.id == warehouse_id).first()
    if not db_warehouse:
        raise HTTPException(status_code=404, detail="仓库不存在")
    
    db_warehouse.is_active = True
    db_warehouse.status = "active"
    db_warehouse.updated_at = datetime.now()
    db.commit()
    
    return {"message": "仓库启用成功"}


@router.post("/{warehouse_id}/deactivate")
def deactivate_warehouse(warehouse_id: int, db: Session = Depends(get_db)):
    """停用仓库"""
    db_warehouse = db.query(Warehouse).filter(Warehouse.id == warehouse_id).first()
    if not db_warehouse:
        raise HTTPException(status_code=404, detail="仓库不存在")
    
    db_warehouse.is_active = False
    db_warehouse.status = "inactive"
    db_warehouse.updated_at = datetime.now()
    db.commit()
    
    return {"message": "仓库停用成功"}


@router.get("/{warehouse_id}/inventory")
def get_warehouse_inventory(
    warehouse_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """获取仓库库存"""
    warehouse = db.query(Warehouse).filter(Warehouse.id == warehouse_id).first()
    if not warehouse:
        raise HTTPException(status_code=404, detail="仓库不存在")
    
    # 获取库存记录
    inventory_query = db.query(Inventory).filter(
        Inventory.warehouse_id == warehouse_id,
        Inventory.is_active == True
    )
    
    total = inventory_query.count()
    inventories = inventory_query.offset(skip).limit(limit).all()
    
    return {
        "warehouse_id": warehouse_id,
        "warehouse_name": warehouse.name,
        "total": total,
        "inventories": inventories
    }


@router.get("/stats/overview")
def get_warehouse_stats(db: Session = Depends(get_db)):
    """获取仓库统计概览"""
    # 总仓库数
    total_warehouses = db.query(Warehouse).count()
    
    # 活跃仓库数
    active_warehouses = db.query(Warehouse).filter(Warehouse.is_active == True).count()
    
    # 总容量
    total_capacity = db.query(func.sum(Warehouse.total_capacity_m3)).scalar() or 0
    
    # 已用容量
    used_capacity = db.query(func.sum(Warehouse.used_capacity_m3)).scalar() or 0
    
    # 平均利用率
    avg_utilization = (used_capacity / total_capacity * 100) if total_capacity > 0 else 0
    
    # 库存商品总数
    total_inventory_items = db.query(Inventory).filter(Inventory.is_active == True).count()
    
    return {
        "total_warehouses": total_warehouses,
        "active_warehouses": active_warehouses,
        "inactive_warehouses": total_warehouses - active_warehouses,
        "total_capacity_m3": total_capacity,
        "used_capacity_m3": used_capacity,
        "available_capacity_m3": total_capacity - used_capacity,
        "average_utilization_rate": round(avg_utilization, 2),
        "total_inventory_items": total_inventory_items
    }
