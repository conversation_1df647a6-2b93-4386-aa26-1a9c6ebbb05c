import time
from datetime import datetime


def generate_order_number(prefix: str = "ORDER") -> str:
    """
    生成订单号
    
    Args:
        prefix: 订单号前缀
        
    Returns:
        生成的订单号
    """
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    return f"{prefix}{timestamp}"


def generate_unique_code(prefix: str = "", length: int = 8) -> str:
    """
    生成唯一编码
    
    Args:
        prefix: 编码前缀
        length: 编码长度（不包括前缀）
        
    Returns:
        生成的唯一编码
    """
    import random
    import string
    
    # 生成随机字符串
    chars = string.ascii_uppercase + string.digits
    random_str = ''.join(random.choice(chars) for _ in range(length))
    
    return f"{prefix}{random_str}" if prefix else random_str


def format_currency(amount: float, currency: str = "¥") -> str:
    """
    格式化货币显示
    
    Args:
        amount: 金额
        currency: 货币符号
        
    Returns:
        格式化后的货币字符串
    """
    return f"{currency}{amount:,.2f}"


def validate_phone(phone: str) -> bool:
    """
    验证手机号格式
    
    Args:
        phone: 手机号
        
    Returns:
        是否有效
    """
    import re
    pattern = r'^1[3-9]\d{9}$'
    return bool(re.match(pattern, phone))


def validate_email(email: str) -> bool:
    """
    验证邮箱格式
    
    Args:
        email: 邮箱地址
        
    Returns:
        是否有效
    """
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))
