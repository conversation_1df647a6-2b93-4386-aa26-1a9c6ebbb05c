<template>
  <div class="warehouse-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>仓库管理</h2>
        <p class="page-description">管理仓库信息，监控仓库容量和库存状态</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建仓库
        </el-button>
        <el-button @click="exportWarehouses">
          <el-icon><Download /></el-icon>
          导出仓库
        </el-button>
      </div>
    </div>

    <!-- 仓库概览 -->
    <el-row :gutter="20" class="warehouse-overview">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon total">
              <el-icon size="32"><OfficeBuilding /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ warehouseStats.total_warehouses }}</div>
              <div class="overview-label">仓库总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon active">
              <el-icon size="32"><CircleCheck /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ warehouseStats.active_warehouses }}</div>
              <div class="overview-label">活跃仓库</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon capacity">
              <el-icon size="32"><Box /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ warehouseStats.total_capacity_m3.toFixed(0) }}</div>
              <div class="overview-label">总容量(m³)</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon utilization">
              <el-icon size="32"><TrendCharts /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ warehouseStats.average_utilization_rate }}%</div>
              <div class="overview-label">平均利用率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true">
        <el-form-item label="仓库名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入仓库名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 150px">
            <el-option label="活跃" value="active" />
            <el-option label="停用" value="inactive" />
            <el-option label="维护中" value="maintenance" />
          </el-select>
        </el-form-item>

        <el-form-item label="启用状态">
          <el-select v-model="searchForm.is_active" placeholder="选择启用状态" clearable style="width: 150px">
            <el-option label="启用" :value="true" />
            <el-option label="停用" :value="false" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button @click="fetchWarehouses" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 仓库列表 -->
    <el-card class="warehouse-list-card">
      <template #header>
        <div class="card-header">
          <span>仓库列表</span>
          <div class="header-actions">
            <el-button-group>
              <el-button 
                :type="viewMode === 'table' ? 'primary' : ''" 
                @click="viewMode = 'table'"
                :icon="List"
              >
                表格视图
              </el-button>
              <el-button 
                :type="viewMode === 'card' ? 'primary' : ''" 
                @click="viewMode = 'card'"
                :icon="Grid"
              >
                卡片视图
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'">
        <el-table :data="warehouses" style="width: 100%" v-loading="loading">
          <el-table-column prop="name" label="仓库名称" width="150" />
          <el-table-column prop="address" label="地址" min-width="200" show-overflow-tooltip />
          <el-table-column prop="total_capacity_m3" label="总容量(m³)" width="120">
            <template #default="{ row }">
              <span class="capacity">{{ row.total_capacity_m3.toFixed(0) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="used_capacity_m3" label="已用容量(m³)" width="130">
            <template #default="{ row }">
              <span class="used-capacity">{{ row.used_capacity_m3.toFixed(0) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="utilization_rate" label="利用率" width="100">
            <template #default="{ row }">
              <el-progress 
                :percentage="row.utilization_rate" 
                :color="getProgressColor(row.utilization_rate)"
                :stroke-width="8"
              />
            </template>
          </el-table-column>
          <el-table-column prop="inventory_count" label="库存总数量" width="120">
            <template #default="{ row }">
              <span class="inventory-count">{{ row.inventory_count || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="inventory_sku_count" label="商品种类" width="100">
            <template #default="{ row }">
              <span class="inventory-sku-count">{{ row.inventory_sku_count || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="available_stock" label="可用库存" width="100">
            <template #default="{ row }">
              <span class="available-stock">{{ row.available_stock || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="reserved_stock" label="预留库存" width="100">
            <template #default="{ row }">
              <span class="reserved-stock">{{ row.reserved_stock || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="is_active" label="启用状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.is_active ? 'success' : 'danger'">
                {{ row.is_active ? '启用' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="150">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="240" fixed="right">
            <template #default="{ row }">
              <div class="table-actions">
                <el-button size="small" @click="viewWarehouseDetail(row)">查看</el-button>
                <el-dropdown @command="(command: string) => handleWarehouseAction(command, row)">
                  <el-button size="small" type="primary">
                    操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit">编辑</el-dropdown-item>
                      <el-dropdown-item command="inventory">查看库存</el-dropdown-item>
                      <el-dropdown-item command="activate" v-if="!row.is_active">启用</el-dropdown-item>
                      <el-dropdown-item command="deactivate" v-if="row.is_active">停用</el-dropdown-item>
                      <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <el-row :gutter="20">
          <el-col :span="8" v-for="warehouse in warehouses" :key="warehouse.id">
            <el-card class="warehouse-card" @click="viewWarehouseDetail(warehouse)">
              <div class="card-header">
                <h3>{{ warehouse.name }}</h3>
                <div class="card-status">
                  <el-tag :type="warehouse.is_active ? 'success' : 'danger'" size="small">
                    {{ warehouse.is_active ? '启用' : '停用' }}
                  </el-tag>
                  <el-tag :type="getStatusTagType(warehouse.status)" size="small">
                    {{ getStatusLabel(warehouse.status) }}
                  </el-tag>
                </div>
              </div>
              <div class="card-content">
                <p><strong>地址:</strong> {{ warehouse.address }}</p>
                <p><strong>总容量:</strong> {{ warehouse.total_capacity_m3.toFixed(0) }} m³</p>
                <p><strong>已用容量:</strong> {{ warehouse.used_capacity_m3.toFixed(0) }} m³</p>

                <div class="inventory-stats">
                  <div class="stat-row">
                    <span><strong>库存总数量:</strong> {{ warehouse.inventory_count || 0 }}</span>
                    <span><strong>商品种类:</strong> {{ warehouse.inventory_sku_count || 0 }}</span>
                  </div>
                  <div class="stat-row">
                    <span><strong>可用库存:</strong> {{ warehouse.available_stock || 0 }}</span>
                    <span><strong>预留库存:</strong> {{ warehouse.reserved_stock || 0 }}</span>
                  </div>
                </div>

                <div class="utilization-info">
                  <span class="label">利用率:</span>
                  <el-progress
                    :percentage="warehouse.utilization_rate || 0"
                    :color="getProgressColor(warehouse.utilization_rate || 0)"
                    :stroke-width="6"
                    style="flex: 1; margin-left: 10px;"
                  />
                </div>
              </div>
              <div class="card-actions" @click.stop>
                <el-button size="small" @click="viewWarehouseDetail(warehouse)">查看详情</el-button>
                <el-dropdown @command="(command: string) => handleWarehouseAction(command, warehouse)">
                  <el-button size="small" type="primary">
                    操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit">编辑</el-dropdown-item>
                      <el-dropdown-item command="inventory">查看库存</el-dropdown-item>
                      <el-dropdown-item command="activate" v-if="!warehouse.is_active">启用</el-dropdown-item>
                      <el-dropdown-item command="deactivate" v-if="warehouse.is_active">停用</el-dropdown-item>
                      <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchWarehouses"
          @current-change="fetchWarehouses"
        />
      </div>
    </el-card>

    <!-- 创建/编辑仓库对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingWarehouse ? '编辑仓库' : '新建仓库'"
      width="600px"
      @close="resetForm"
    >
      <el-form :model="warehouseForm" :rules="formRules" ref="formRef" label-width="120px">
        <el-form-item label="仓库名称" prop="name">
          <el-input v-model="warehouseForm.name" placeholder="请输入仓库名称" />
        </el-form-item>
        
        <el-form-item label="仓库地址" prop="address">
          <el-input v-model="warehouseForm.address" placeholder="请输入仓库地址" />
        </el-form-item>
        
        <el-form-item label="总容量(m³)" prop="total_capacity_m3">
          <el-input-number 
            v-model="warehouseForm.total_capacity_m3" 
            :min="0" 
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="已用容量(m³)" prop="used_capacity_m3">
          <el-input-number 
            v-model="warehouseForm.used_capacity_m3" 
            :min="0" 
            :max="warehouseForm.total_capacity_m3"
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="纬度">
              <el-input-number 
                v-model="warehouseForm.latitude" 
                :precision="6"
                style="width: 100%"
                placeholder="纬度"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="经度">
              <el-input-number 
                v-model="warehouseForm.longitude" 
                :precision="6"
                style="width: 100%"
                placeholder="经度"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="状态">
          <el-select v-model="warehouseForm.status" style="width: 100%">
            <el-option label="活跃" value="active" />
            <el-option label="停用" value="inactive" />
            <el-option label="维护中" value="maintenance" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="启用状态">
          <el-switch v-model="warehouseForm.is_active" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ editingWarehouse ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 仓库详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="仓库详情" width="800px">
      <div v-if="selectedWarehouse" class="warehouse-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="仓库名称">{{ selectedWarehouse.name }}</el-descriptions-item>
          <el-descriptions-item label="地址">{{ selectedWarehouse.address }}</el-descriptions-item>
          <el-descriptions-item label="总容量">{{ selectedWarehouse.total_capacity_m3.toFixed(2) }} m³</el-descriptions-item>
          <el-descriptions-item label="已用容量">{{ selectedWarehouse.used_capacity_m3.toFixed(2) }} m³</el-descriptions-item>
          <el-descriptions-item label="可用容量">{{ selectedWarehouse.available_capacity_m3?.toFixed(2) }} m³</el-descriptions-item>
          <el-descriptions-item label="利用率">
            <el-progress
              :percentage="selectedWarehouse.utilization_rate || 0"
              :color="getProgressColor(selectedWarehouse.utilization_rate || 0)"
            />
          </el-descriptions-item>
          <el-descriptions-item label="库存总数量">{{ selectedWarehouse.inventory_count || 0 }}</el-descriptions-item>
          <el-descriptions-item label="商品种类">{{ selectedWarehouse.inventory_sku_count || 0 }}</el-descriptions-item>
          <el-descriptions-item label="可用库存">{{ selectedWarehouse.available_stock || 0 }}</el-descriptions-item>
          <el-descriptions-item label="预留库存">{{ selectedWarehouse.reserved_stock || 0 }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(selectedWarehouse.status)">
              {{ getStatusLabel(selectedWarehouse.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="启用状态">
            <el-tag :type="selectedWarehouse.is_active ? 'success' : 'danger'">
              {{ selectedWarehouse.is_active ? '启用' : '停用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(selectedWarehouse.created_at) }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  warehouseApi,
  type Warehouse,
  type WarehouseStats,
  type WarehouseCreate,
  type WarehouseUpdate
} from '@/api/warehouses'
import {
  Plus,
  Download,
  OfficeBuilding,
  CircleCheck,
  Box,
  TrendCharts,
  Search,
  List,
  Grid,
  ArrowDown,
  Refresh
} from '@element-plus/icons-vue'

// 响应式数据
const warehouses = ref<Warehouse[]>([])
const loading = ref(false)
const submitting = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const viewMode = ref<'table' | 'card'>('table')

const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const selectedWarehouse = ref<Warehouse | null>(null)
const editingWarehouse = ref<Warehouse | null>(null)
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  name: '',
  status: '',
  is_active: null as boolean | null
})

// 仓库表单
const warehouseForm = reactive<WarehouseCreate>({
  name: '',
  address: '',
  total_capacity_m3: 0,
  used_capacity_m3: 0,
  latitude: undefined,
  longitude: undefined,
  is_active: true,
  status: 'active'
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入仓库名称', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入仓库地址', trigger: 'blur' }
  ],
  total_capacity_m3: [
    { required: true, message: '请输入总容量', trigger: 'blur' },
    { type: 'number', min: 0, message: '总容量必须大于0', trigger: 'blur' }
  ]
}

// 统计数据
const warehouseStats = ref<WarehouseStats>({
  total_warehouses: 0,
  active_warehouses: 0,
  inactive_warehouses: 0,
  total_capacity_m3: 0,
  used_capacity_m3: 0,
  available_capacity_m3: 0,
  average_utilization_rate: 0,
  total_inventory_items: 0
})

// 方法
const fetchWarehouses = async () => {
  loading.value = true
  try {
    const params: any = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value
    }

    if (searchForm.name) {
      params.name = searchForm.name
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }
    if (searchForm.is_active !== null) {
      params.is_active = searchForm.is_active
    }

    const response = await warehouseApi.getWarehouses(params)
    warehouses.value = response || []
    total.value = response?.length || 0

    // 获取统计数据
    const statsResponse = await warehouseApi.getWarehouseStats()
    if (statsResponse) {
      warehouseStats.value = statsResponse
    }
  } catch (error: any) {
    console.error('获取仓库列表失败:', error)
    ElMessage.error('获取仓库列表失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchWarehouses()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    status: '',
    is_active: null
  })
  handleSearch()
}

const viewWarehouseDetail = (warehouse: Warehouse) => {
  selectedWarehouse.value = warehouse
  showDetailDialog.value = true
}

const handleWarehouseAction = async (command: string, warehouse: Warehouse) => {
  switch (command) {
    case 'edit':
      editWarehouse(warehouse)
      break
    case 'inventory':
      ElMessage.info('查看库存功能开发中...')
      break
    case 'activate':
      await activateWarehouse(warehouse)
      break
    case 'deactivate':
      await deactivateWarehouse(warehouse)
      break
    case 'delete':
      await deleteWarehouse(warehouse)
      break
    default:
      ElMessage.warning('未知操作')
  }
}

const editWarehouse = (warehouse: Warehouse) => {
  editingWarehouse.value = warehouse
  Object.assign(warehouseForm, {
    name: warehouse.name,
    address: warehouse.address,
    total_capacity_m3: warehouse.total_capacity_m3,
    used_capacity_m3: warehouse.used_capacity_m3,
    latitude: warehouse.latitude,
    longitude: warehouse.longitude,
    is_active: warehouse.is_active,
    status: warehouse.status
  })
  showCreateDialog.value = true
}

const activateWarehouse = async (warehouse: Warehouse) => {
  try {
    await ElMessageBox.confirm('确定要启用这个仓库吗？', '确认启用', {
      type: 'warning'
    })

    await warehouseApi.activateWarehouse(warehouse.id!)
    ElMessage.success('仓库启用成功')
    fetchWarehouses()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('启用仓库失败:', error)
      ElMessage.error('启用失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const deactivateWarehouse = async (warehouse: Warehouse) => {
  try {
    await ElMessageBox.confirm('确定要停用这个仓库吗？', '确认停用', {
      type: 'warning'
    })

    await warehouseApi.deactivateWarehouse(warehouse.id!)
    ElMessage.success('仓库停用成功')
    fetchWarehouses()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('停用仓库失败:', error)
      ElMessage.error('停用失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const deleteWarehouse = async (warehouse: Warehouse) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除仓库"${warehouse.name}"吗？\n\n注意：只有没有任何出入库记录的仓库才能删除。\n如果仓库有历史记录，建议使用"停用"功能。`,
      '确认删除',
      {
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    await warehouseApi.deleteWarehouse(warehouse.id!)
    ElMessage.success('仓库删除成功')
    fetchWarehouses()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除仓库失败:', error)
      const errorMessage = error.response?.data?.detail || error.message

      // 如果是因为有出入库记录而无法删除，提供停用选项
      if (errorMessage.includes('历史出入库记录')) {
        try {
          await ElMessageBox.confirm(
            `${errorMessage}\n\n是否改为停用此仓库？停用后仓库将不可用，但数据会保留。`,
            '无法删除',
            {
              type: 'info',
              confirmButtonText: '停用仓库',
              cancelButtonText: '取消',
              dangerouslyUseHTMLString: false
            }
          )

          // 用户选择停用
          await deactivateWarehouse(warehouse)
        } catch (deactivateError) {
          // 用户取消停用操作，不显示错误
        }
      } else {
        ElMessage.error('删除失败: ' + errorMessage)
      }
    }
  }
}

const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    if (editingWarehouse.value) {
      // 更新仓库
      await warehouseApi.updateWarehouse(editingWarehouse.value.id!, warehouseForm as WarehouseUpdate)
      ElMessage.success('仓库更新成功')
    } else {
      // 创建仓库
      await warehouseApi.createWarehouse(warehouseForm)
      ElMessage.success('仓库创建成功')
    }

    showCreateDialog.value = false
    fetchWarehouses()
  } catch (error: any) {
    console.error('提交表单失败:', error)
    ElMessage.error('操作失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  editingWarehouse.value = null
  Object.assign(warehouseForm, {
    name: '',
    address: '',
    total_capacity_m3: 0,
    used_capacity_m3: 0,
    latitude: undefined,
    longitude: undefined,
    is_active: true,
    status: 'active'
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const exportWarehouses = () => {
  ElMessage.success('仓库数据导出成功')
}

// 辅助方法
const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '活跃',
    inactive: '停用',
    maintenance: '维护中'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    active: 'success',
    inactive: 'danger',
    maintenance: 'warning'
  }
  return typeMap[status] || ''
}

const getProgressColor = (percentage: number) => {
  if (percentage < 50) return '#67C23A'
  if (percentage < 80) return '#E6A23C'
  return '#F56C6C'
}

const formatDateTime = (dateStr: string | null | undefined) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  fetchWarehouses()
})
</script>

<style scoped>
.warehouse-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
}

.page-description {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 仓库概览 */
.warehouse-overview {
  margin-bottom: 12px;
}

.overview-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.overview-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.overview-icon {
  padding: 12px;
  border-radius: 12px;
  flex-shrink: 0;
}

.overview-icon.total {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-icon.active {
  background: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.overview-icon.capacity {
  background: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.overview-icon.utilization {
  background: rgba(245, 108, 108, 0.1);
  color: #F56C6C;
}

.overview-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 14px;
  color: #7f8c8d;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 仓库列表卡片 */
.warehouse-list-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.warehouse-list-card .el-card__header {
  padding: 14px 20px !important;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 数据样式 */
.capacity {
  font-weight: 600;
  color: #E6A23C;
}

.used-capacity {
  font-weight: 600;
  color: #F56C6C;
}

.inventory-count {
  font-weight: 600;
  color: #409EFF;
}

/* 卡片视图 */
.card-view {
  margin-top: 20px;
}

.warehouse-card {
  margin-bottom: 20px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.warehouse-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.warehouse-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.warehouse-card .card-header h3 {
  margin: 0;
  color: #303133;
}

.card-status {
  display: flex;
  gap: 8px;
}

.warehouse-card .card-content {
  margin-bottom: 16px;
}

.warehouse-card .card-content p {
  margin: 8px 0;
  color: #606266;
}

.utilization-info {
  display: flex;
  align-items: center;
  margin-top: 12px;
}

.utilization-info .label {
  color: #606266;
  font-weight: 500;
  min-width: 60px;
}

.warehouse-card .card-actions {
  display: flex;
  gap: 8px;
}

/* 表格操作 */
.table-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.table-actions .el-button {
  margin: 0;
}

/* 分页 */
.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 仓库详情 */
.warehouse-detail {
  padding: 20px 0;
}

/* 库存统计样式 */
.inventory-stats {
  margin: 12px 0;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.stat-row:last-child {
  margin-bottom: 0;
}

.stat-row span {
  font-size: 13px;
  color: #666;
}

.inventory-count {
  color: #409eff;
  font-weight: 500;
}

.inventory-sku-count {
  color: #67c23a;
  font-weight: 500;
}

.available-stock {
  color: #67c23a;
  font-weight: 500;
}

.reserved-stock {
  color: #e6a23c;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .warehouse-view {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .warehouse-overview .el-col {
    margin-bottom: 12px;
  }

  .overview-content {
    padding: 16px;
  }

  .search-card .el-form {
    flex-direction: column;
  }

  .search-card .el-form-item {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .table-actions {
    flex-direction: column;
    gap: 4px;
  }

  .card-view .el-col {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .header-left h2 {
    font-size: 24px;
  }

  .overview-value {
    font-size: 20px;
  }

  .overview-content {
    gap: 12px;
  }

  .overview-icon {
    padding: 8px;
  }
}
</style>
