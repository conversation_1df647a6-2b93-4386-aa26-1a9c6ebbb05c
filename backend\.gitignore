# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# poetry
#   Similar to Pipfile.lock, it is generally recommended to include poetry.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#   https://python-poetry.org/docs/basic-usage/#commit-your-poetrylock-file-to-version-control
#poetry.lock

# pdm
#   Similar to Pipfile.lock, it is generally recommended to include pdm.lock in version control.
#pdm.lock
#   pdm stores project-wide configurations in .pdm.toml, but it is recommended to not include it
#   in version control.
#   https://pdm.fming.dev/#use-with-ide
.pdm.toml

# PEP 582; used by e.g. github.com/David-OConnor/pyflow and github.com/pdm-project/pdm
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be added to the global gitignore or merged into this project gitignore.  For a PyCharm
#  project, it is recommended to use the following settings:
.idea/

# VS Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# Local History for Visual Studio Code
.history/

# Built Visual Studio Code Extensions
*.vsix

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~

# Database files
*.db
*.sqlite
*.sqlite3

# Log files
*.log
logs/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Backup files
*.bak
*.backup
*.old
*.orig

# FastAPI specific
.pytest_cache/

# Alembic
# Uncomment if you want to ignore migration files
# alembic/versions/*.py

# SQLAlchemy
*.db-journal

# Redis dump file
dump.rdb

# Configuration files with sensitive data
config.ini
settings.ini
secrets.json

# SSL certificates
*.pem
*.key
*.crt
*.cert

# Docker
.dockerignore
Dockerfile.dev

# Kubernetes
*.yaml.local
*.yml.local

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# AWS
.aws/

# Google Cloud
.gcloud/

# Azure
.azure/

# Local development
.local/

# Coverage reports
htmlcov/
.coverage
coverage.xml

# Profiling
*.prof

# Memory dumps
*.hprof

# JetBrains IDEs
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# TextMate
*.tmproj
*.tmproject
tmtags

# Xcode
*.pbxuser
*.mode1v3
*.mode2v3
*.perspectivev3
*.xcuserstate
project.xcworkspace/
xcuserdata/

# Android
*.apk
*.ap_
*.dex
*.class
bin/
gen/
out/
build/
.gradle/
local.properties
proguard/

# iOS
*.ipa
*.dSYM.zip
*.dSYM
DerivedData/
*.hmap
*.ipa
*.xcuserstate

# Node.js (if using Node.js tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python virtual environments
venv/
env/
ENV/
.venv/
.env/

# Conda
.conda/

# Jupyter
.ipynb_checkpoints/

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# Django
*.log
local_settings.py
db.sqlite3
media/

# Flask
instance/
.webassets-cache

# Scrapy
.scrapy

# Sphinx
docs/_build/

# PyBuilder
target/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py

# Environments
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre
.pyre/

# pytype
.pytype/

# Cython
cython_debug/
