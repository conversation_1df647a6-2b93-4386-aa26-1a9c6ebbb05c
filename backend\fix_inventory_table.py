#!/usr/bin/env python3
"""
修复 inventory 表，添加缺失的 enable_batch_management 字段
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from backend.config import config
import pymysql

def fix_inventory_table():
    """添加 enable_batch_management 字段到 inventory 表"""
    try:
        # 创建数据库连接
        connection = pymysql.connect(
            host=config.DB_HOST,
            port=config.DB_PORT,
            user=config.DB_USER,
            password=config.DB_PASSWORD,
            database=config.DB_NAME,
            charset=config.DB_CHARSET
        )
        
        with connection.cursor() as cursor:
            # 检查字段是否已存在
            check_column_sql = """
                SELECT COUNT(*) as count 
                FROM information_schema.columns 
                WHERE table_schema = %s 
                AND table_name = 'inventory' 
                AND column_name = 'enable_batch_management'
            """
            
            cursor.execute(check_column_sql, (config.DB_NAME,))
            result = cursor.fetchone()
            
            if result[0] == 0:
                # 字段不存在，添加字段
                add_column_sql = """
                    ALTER TABLE inventory 
                    ADD COLUMN enable_batch_management BOOLEAN DEFAULT FALSE 
                    COMMENT '是否启用批次管理'
                """
                
                print("正在添加 enable_batch_management 字段到 inventory 表...")
                cursor.execute(add_column_sql)
                connection.commit()
                print("字段添加成功!")
            else:
                print("enable_batch_management 字段已存在，无需添加。")
                
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        return False
    finally:
        try:
            connection.close()
        except:
            pass
    
    return True

if __name__ == "__main__":
    print("开始修复 inventory 表...")
    if fix_inventory_table():
        print("修复完成!")
    else:
        print("修复失败!")
        sys.exit(1)