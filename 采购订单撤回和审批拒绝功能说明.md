# 采购订单撤回和审批拒绝功能说明

## 功能概述

根据收货单操作区按钮的摆放规则，为采购订单添加了撤回功能和审批拒绝功能，使采购订单的工作流程更加完善。

## 主要功能

### 1. 撤回功能
- **适用状态**: 已提交状态的采购订单
- **操作位置**: 表格操作列的主要按钮区域
- **功能描述**: 将已提交的采购订单撤回到草稿状态，允许重新编辑

### 2. 审批拒绝功能
- **适用状态**: 已提交状态的采购订单
- **操作位置**: "更多"下拉菜单中
- **功能描述**: 审核人员可以拒绝采购订单，订单状态变为"已拒绝"

### 3. 重新编辑功能
- **适用状态**: 已拒绝状态的采购订单
- **操作位置**: "更多"下拉菜单中
- **功能描述**: 允许对被拒绝的采购订单重新编辑

## 按钮摆放规则

参考收货单的操作区按钮摆放规则：

### 表格视图操作列
```
[查看] [状态相关的主要操作按钮] [更多 ▼]
```

#### 主要操作按钮（根据状态显示）
- **草稿状态**: 显示"提交"按钮
- **已提交状态**: 显示"撤回"按钮
- **已审核状态**: 显示"开始采购"按钮

#### 更多下拉菜单（根据状态显示不同选项）
- **草稿状态**: 编辑
- **已提交状态**: 审核通过、审核拒绝
- **已拒绝状态**: 重新编辑
- **采购中状态**: 确认收货
- **通用选项**: 取消订单、删除（仅草稿状态）

### 卡片视图操作区
与表格视图保持一致的按钮摆放规则。

## 状态流转

```
草稿 → 已提交 → 已审核 → 采购中 → 已完成
  ↑      ↓         ↓
  └─── 撤回    审批拒绝 → 已拒绝
                        ↓
                    重新编辑 → 草稿
```

## 技术实现

### 后端API接口

#### 1. 撤回采购订单
```
PUT /api/purchase/{order_id}/recall
```

#### 2. 审批采购订单（支持通过和拒绝）
```
PUT /api/purchase/{order_id}/approve?approved_by={user}&approved={true/false}&remark={remark}
```

### 前端功能

#### 1. 新增状态支持
- 添加 `REJECTED` 状态到 `PurchaseOrderStatus` 枚举
- 更新状态标签和颜色映射
- 更新进度条显示

#### 2. 操作按钮优化
- 重新组织操作按钮布局
- 根据状态动态显示相应操作
- 统一表格视图和卡片视图的操作逻辑

#### 3. 用户交互优化
- 添加确认对话框
- 提供清晰的操作反馈
- 支持审核备注功能

## 使用说明

### 撤回操作
1. 在采购订单列表中找到"已提交"状态的订单
2. 点击操作列中的"撤回"按钮
3. 在确认对话框中点击"确定撤回"
4. 订单状态变为"草稿"，可以重新编辑

### 审批拒绝操作
1. 在采购订单列表中找到"已提交"状态的订单
2. 点击操作列中的"更多"按钮
3. 在下拉菜单中选择"审核拒绝"
4. 在确认对话框中点击"审核拒绝"
5. 订单状态变为"已拒绝"

### 重新编辑操作
1. 在采购订单列表中找到"已拒绝"状态的订单
2. 点击操作列中的"更多"按钮
3. 在下拉菜单中选择"重新编辑"
4. 进入编辑页面，修改订单信息
5. 保存后订单状态变为"草稿"

## 注意事项

1. **权限控制**: 撤回和审批操作需要相应的用户权限
2. **数据一致性**: 撤回操作会清除提交相关的时间戳和操作人信息
3. **审核记录**: 审批拒绝操作会记录审核人和审核时间
4. **状态限制**: 只有特定状态的订单才能执行相应操作

## 测试建议

1. 测试不同状态下按钮的显示和隐藏
2. 测试撤回操作的状态变更和数据清理
3. 测试审批拒绝操作的状态变更和记录保存
4. 测试重新编辑功能的完整流程
5. 测试权限控制和错误处理
