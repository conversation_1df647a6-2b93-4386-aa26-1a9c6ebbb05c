import api from './index'

export interface Product {
  id: number
  name: string
  sku?: string
  brand?: string
  category: string
  price: number
  cost: number
  stock: number
  description?: string
  specifications?: string
  image?: string
  status: 'active' | 'inactive' | 'discontinued'
  is_active: boolean
  market_trend?: number
  competition_level?: number
  profit_margin?: number
  recommendation_score?: number
  sales_data?: any
  created_at: string
  updated_at?: string
}

export interface ProductListResponse {
  items: Product[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

export interface ProductQuery {
  name?: string
  category?: string
  brand?: string
  status?: string
  min_price?: number
  max_price?: number
  low_stock?: boolean
  page?: number
  page_size?: number
}

export interface ProductAnalysisRequest {
  product_ids: number[]
  analysis_type: string
  parameters?: any
}

export interface ProductAnalysisResponse {
  product_id: number
  analysis_type: string
  analysis_result: string
  confidence_score: number
  analysis_data: any
}

export interface ProductRecommendationRequest {
  category?: string
  price_range?: number[]
  market_conditions?: any
  limit?: number
}

export interface ProductRecommendationResponse {
  products: Product[]
  recommendation_reasons: Record<number, string>
}

// 产品API
export const productApi = {
  // 获取产品列表
  getProducts: (params?: ProductQuery) => {
    return api.get<ProductListResponse>('/api/products/', { params })
  },

  // 获取单个产品
  getProduct: (id: number) => {
    return api.get<Product>(`/api/products/${id}`)
  },

  // 创建产品
  createProduct: (data: Omit<Product, 'id' | 'created_at' | 'updated_at'>) => {
    return api.post<Product>('/api/products/', data)
  },

  // 更新产品
  updateProduct: (id: number, data: Partial<Product>) => {
    return api.put<Product>(`/api/products/${id}`, data)
  },

  // 删除产品
  deleteProduct: (id: number) => {
    return api.delete(`/api/products/${id}`)
  },

  // 产品分析
  analyzeProducts: (data: ProductAnalysisRequest) => {
    return api.post<ProductAnalysisResponse[]>('/api/products/analysis', data)
  },

  // 获取市场趋势
  getMarketTrends: (params?: { category?: string; time_period?: string }) => {
    return api.get('/api/products/trends/market', { params })
  },

  // 产品推荐
  recommendProducts: (data: ProductRecommendationRequest) => {
    return api.post<ProductRecommendationResponse>('/api/products/recommend', data)
  },

  // 获取分析仪表板
  getDashboard: () => {
    return api.get('/api/products/analytics/dashboard')
  }
}
