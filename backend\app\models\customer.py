"""
客户数据模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, Boolean, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base


class Customer(Base):
    """客户模型"""
    __tablename__ = "customers"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(200), nullable=False, comment="客户名称")
    code = Column(String(50), unique=True, nullable=False, comment="客户编码")
    contact_person = Column(String(100), comment="联系人")
    phone = Column(String(20), comment="联系电话")
    email = Column(String(100), comment="邮箱")
    address = Column(Text, comment="地址")
    
    # 客户类型
    customer_type = Column(String(50), default="individual", comment="客户类型: individual, enterprise, distributor")
    level = Column(String(20), default="normal", comment="客户等级: vip, normal, potential")
    
    # 财务信息
    credit_limit = Column(Float, default=0.0, comment="信用额度")
    current_balance = Column(Float, default=0.0, comment="当前余额")
    total_orders = Column(Integer, default=0, comment="总订单数")
    total_amount = Column(Float, default=0.0, comment="总交易金额")
    
    # 状态
    status = Column(String(20), default="active", comment="状态: active, inactive, blacklist")
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 平台信息
    platform_accounts = Column(JSON, comment="平台账户信息")
    
    # 配送信息
    default_delivery_address = Column(Text, comment="默认配送地址")
    delivery_preferences = Column(JSON, comment="配送偏好")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    last_order_date = Column(DateTime(timezone=True), comment="最后下单时间")
    
    # 备注
    remark = Column(Text, comment="备注")

    # 关系
    sales_orders = relationship("SalesOrder", back_populates="customer")
    sales_returns = relationship("SalesReturn", back_populates="customer")

    def __repr__(self):
        return f"<Customer(id={self.id}, name='{self.name}', code='{self.code}')>"
