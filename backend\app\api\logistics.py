from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app.core.database import get_db
from app.schemas.logistics import (
    LogisticsRoute, LogisticsRouteCreate,
    DeliveryOrder, DeliveryOrderCreate,
    CostCalculationRequest, CostCalculationResponse,
    RouteOptimizationRequest, RouteOptimizationResponse
)
from app.services.logistics_service import LogisticsService

router = APIRouter()

@router.get("/routes", response_model=List[LogisticsRoute])
async def get_routes(
    skip: int = 0,
    limit: int = 100,
    origin: str = None,
    destination: str = None,
    db: Session = Depends(get_db)
):
    """获取物流路线列表"""
    service = LogisticsService(db)
    return service.get_routes(skip=skip, limit=limit, origin=origin, destination=destination)

@router.post("/routes", response_model=LogisticsRoute)
async def create_route(route: LogisticsRouteCreate, db: Session = Depends(get_db)):
    """创建新的物流路线"""
    service = LogisticsService(db)
    return service.create_route(route)

@router.get("/routes/{route_id}", response_model=LogisticsRoute)
async def get_route(route_id: int, db: Session = Depends(get_db)):
    """获取单个路线详情"""
    service = LogisticsService(db)
    route = service.get_route(route_id)
    if not route:
        raise HTTPException(status_code=404, detail="路线不存在")
    return route

@router.post("/calculate-cost", response_model=CostCalculationResponse)
async def calculate_delivery_cost(
    request: CostCalculationRequest,
    db: Session = Depends(get_db)
):
    """计算配送成本"""
    service = LogisticsService(db)
    return service.calculate_cost(request)

@router.post("/optimize-route", response_model=RouteOptimizationResponse)
async def optimize_route(
    request: RouteOptimizationRequest,
    db: Session = Depends(get_db)
):
    """路线优化"""
    service = LogisticsService(db)
    return service.optimize_route(request)

@router.get("/orders", response_model=List[DeliveryOrder])
async def get_delivery_orders(
    skip: int = 0,
    limit: int = 100,
    status: str = None,
    db: Session = Depends(get_db)
):
    """获取配送订单列表"""
    service = LogisticsService(db)
    return service.get_delivery_orders(skip=skip, limit=limit, status=status)

@router.post("/orders", response_model=DeliveryOrder)
async def create_delivery_order(
    order: DeliveryOrderCreate,
    db: Session = Depends(get_db)
):
    """创建配送订单"""
    service = LogisticsService(db)
    return service.create_delivery_order(order)

@router.get("/orders/{order_id}", response_model=DeliveryOrder)
async def get_delivery_order(order_id: int, db: Session = Depends(get_db)):
    """获取配送订单详情"""
    service = LogisticsService(db)
    order = service.get_delivery_order(order_id)
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    return order

@router.put("/orders/{order_id}/status")
async def update_order_status(
    order_id: int,
    status: str,
    notes: str = None,
    db: Session = Depends(get_db)
):
    """更新订单状态"""
    service = LogisticsService(db)
    success = service.update_order_status(order_id, status, notes)
    if not success:
        raise HTTPException(status_code=404, detail="订单不存在")
    return {"message": "订单状态更新成功"}

@router.get("/analytics/performance")
async def get_logistics_performance(
    time_period: str = "30d",
    db: Session = Depends(get_db)
):
    """获取物流性能分析"""
    service = LogisticsService(db)
    return service.get_performance_analytics(time_period)

@router.get("/estimate-time")
async def estimate_delivery_time(
    pickup_address: str,
    delivery_address: str,
    delivery_type: str = "standard",
    db: Session = Depends(get_db)
):
    """预估配送时间"""
    service = LogisticsService(db)
    return service.estimate_delivery_time(pickup_address, delivery_address, delivery_type)



