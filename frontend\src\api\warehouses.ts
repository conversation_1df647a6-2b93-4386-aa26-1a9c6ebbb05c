import { request } from './index'

// 仓库接口
export interface Warehouse {
  id?: number
  name: string
  address: string
  total_capacity_m3: number
  used_capacity_m3: number
  available_capacity_m3?: number
  utilization_rate?: number
  inventory_count?: number
  inventory_sku_count?: number
  available_stock?: number
  reserved_stock?: number
  latitude?: number
  longitude?: number
  operating_hours?: any
  contact_info?: any
  is_active: boolean
  status: string
  created_at?: string
  updated_at?: string
}

// 创建仓库数据
export interface WarehouseCreate {
  name: string
  address: string
  total_capacity_m3: number
  used_capacity_m3?: number
  latitude?: number
  longitude?: number
  operating_hours?: any
  contact_info?: any
  is_active?: boolean
  status?: string
}

// 更新仓库数据
export interface WarehouseUpdate {
  name?: string
  address?: string
  total_capacity_m3?: number
  used_capacity_m3?: number
  latitude?: number
  longitude?: number
  operating_hours?: any
  contact_info?: any
  is_active?: boolean
  status?: string
}

// 仓库统计数据
export interface WarehouseStats {
  total_warehouses: number
  active_warehouses: number
  inactive_warehouses: number
  total_capacity_m3: number
  used_capacity_m3: number
  available_capacity_m3: number
  average_utilization_rate: number
  total_inventory_items: number
}

// 查询参数
export interface WarehouseQuery {
  skip?: number
  limit?: number
  name?: string
  status?: string
  is_active?: boolean
}

// 仓库库存信息
export interface WarehouseInventory {
  warehouse_id: number
  warehouse_name: string
  total: number
  inventories: any[]
}

// API 客户端
export const warehouseApi = {
  // 获取仓库列表
  getWarehouses(params?: WarehouseQuery): Promise<Warehouse[]> {
    return request.get('/api/warehouses/', { params })
  },

  // 获取仓库详情
  getWarehouse(id: number): Promise<Warehouse> {
    return request.get(`/api/warehouses/${id}`)
  },

  // 创建仓库
  createWarehouse(data: WarehouseCreate): Promise<Warehouse> {
    return request.post('/api/warehouses/', data)
  },

  // 更新仓库
  updateWarehouse(id: number, data: WarehouseUpdate): Promise<Warehouse> {
    return request.put(`/api/warehouses/${id}`, data)
  },

  // 删除仓库
  deleteWarehouse(id: number): Promise<{ message: string }> {
    return request.delete(`/api/warehouses/${id}`)
  },

  // 启用仓库
  activateWarehouse(id: number): Promise<{ message: string }> {
    return request.post(`/api/warehouses/${id}/activate`)
  },

  // 停用仓库
  deactivateWarehouse(id: number): Promise<{ message: string }> {
    return request.post(`/api/warehouses/${id}/deactivate`)
  },

  // 获取仓库库存
  getWarehouseInventory(id: number, params?: { skip?: number; limit?: number }): Promise<WarehouseInventory> {
    return request.get(`/api/warehouses/${id}/inventory`, { params })
  },

  // 获取仓库统计
  getWarehouseStats(): Promise<WarehouseStats> {
    return request.get('/api/warehouses/stats/overview')
  }
}
