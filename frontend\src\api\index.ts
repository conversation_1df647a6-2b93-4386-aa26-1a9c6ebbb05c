import axios from 'axios'

/**
 * HTTP请求客户端配置
 * 统一的API请求客户端，用于所有API调用
 */

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:8000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加token等认证信息
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    console.error('API Error:', error)
    return Promise.reject(error)
  }
)

/**
 * 导出配置
 * - api: 主要的HTTP客户端实例
 * - request: api的别名，用于向后兼容
 * - default: 默认导出api实例
 */
export { api, api as request }
export default api
